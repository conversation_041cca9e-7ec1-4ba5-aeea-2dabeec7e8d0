package com.hzwangda.edu.audit;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.boot.autoconfigure.domain.EntityScan;

/**
 * 审计日志服务启动类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = {"com.hzwangda.edu.audit", "com.hzwangda.edu.common"})
@EnableJpaAuditing
@EnableJpaRepositories(basePackages = "com.hzwangda.edu.audit.repository")
@EntityScan(basePackages = {"com.hzwangda.edu.audit.entity"})
public class AuditServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(AuditServiceApplication.class, args);
    }
}
