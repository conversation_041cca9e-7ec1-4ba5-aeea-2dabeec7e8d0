package com.hzwangda.edu.audit.repository;

import com.hzwangda.edu.audit.entity.AuditLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 审计日志Repository接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface AuditLogRepository extends JpaRepository<AuditLog, Long>, JpaSpecificationExecutor<AuditLog> {

    /**
     * 根据用户ID查询审计日志
     */
    Page<AuditLog> findByUserIdAndDeletedFalse(Long userId, Pageable pageable);

    /**
     * 根据用户名查询审计日志
     */
    Page<AuditLog> findByUsernameContainingIgnoreCaseAndDeletedFalse(String username, Pageable pageable);

    /**
     * 根据操作类型查询审计日志
     */
    Page<AuditLog> findByOperationTypeAndDeletedFalse(String operationType, Pageable pageable);

    /**
     * 根据业务模块查询审计日志
     */
    Page<AuditLog> findByModuleAndDeletedFalse(String module, Pageable pageable);

    /**
     * 根据操作结果查询审计日志
     */
    Page<AuditLog> findByOperationResultAndDeletedFalse(String operationResult, Pageable pageable);

    /**
     * 根据风险级别查询审计日志
     */
    Page<AuditLog> findByRiskLevelAndDeletedFalse(String riskLevel, Pageable pageable);

    /**
     * 根据IP地址查询审计日志
     */
    Page<AuditLog> findByIpAddressAndDeletedFalse(String ipAddress, Pageable pageable);

    /**
     * 根据时间范围查询审计日志
     */
    Page<AuditLog> findByOperationTimeBetweenAndDeletedFalse(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * 查询指定时间范围内的高风险操作
     */
    @Query("SELECT a FROM AuditLog a WHERE a.riskLevel IN ('HIGH', 'CRITICAL') " +
           "AND a.operationTime BETWEEN :startTime AND :endTime " +
           "AND a.deleted = false ORDER BY a.operationTime DESC")
    List<AuditLog> findHighRiskOperations(@Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内的总操作数
     */
    @Query("SELECT COUNT(a) FROM AuditLog a WHERE a.operationTime BETWEEN :startTime AND :endTime AND a.deleted = false")
    Long countOperationsByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内的成功操作数
     */
    @Query("SELECT COUNT(a) FROM AuditLog a WHERE a.operationTime BETWEEN :startTime AND :endTime " +
           "AND a.operationResult = 'SUCCESS' AND a.deleted = false")
    Long countSuccessOperationsByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内的失败操作数
     */
    @Query("SELECT COUNT(a) FROM AuditLog a WHERE a.operationTime BETWEEN :startTime AND :endTime " +
           "AND a.operationResult = 'FAILED' AND a.deleted = false")
    Long countFailedOperationsByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内的高风险操作数
     */
    @Query("SELECT COUNT(a) FROM AuditLog a WHERE a.operationTime BETWEEN :startTime AND :endTime " +
           "AND a.riskLevel IN ('HIGH', 'CRITICAL') AND a.deleted = false")
    Long countHighRiskOperationsByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内的活跃用户数
     */
    @Query("SELECT COUNT(DISTINCT a.userId) FROM AuditLog a WHERE a.operationTime BETWEEN :startTime AND :endTime AND a.deleted = false")
    Long countActiveUsersByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 按操作类型统计
     */
    @Query("SELECT a.operationType, COUNT(a) FROM AuditLog a WHERE a.operationTime BETWEEN :startTime AND :endTime " +
           "AND a.deleted = false GROUP BY a.operationType")
    List<Object[]> countByOperationType(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 按业务模块统计
     */
    @Query("SELECT a.module, COUNT(a) FROM AuditLog a WHERE a.operationTime BETWEEN :startTime AND :endTime " +
           "AND a.deleted = false GROUP BY a.module")
    List<Object[]> countByModule(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 按操作结果统计
     */
    @Query("SELECT a.operationResult, COUNT(a) FROM AuditLog a WHERE a.operationTime BETWEEN :startTime AND :endTime " +
           "AND a.deleted = false GROUP BY a.operationResult")
    List<Object[]> countByOperationResult(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 按风险级别统计
     */
    @Query("SELECT a.riskLevel, COUNT(a) FROM AuditLog a WHERE a.operationTime BETWEEN :startTime AND :endTime " +
           "AND a.deleted = false GROUP BY a.riskLevel")
    List<Object[]> countByRiskLevel(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 按小时统计（最近24小时）
     */
    @Query("SELECT HOUR(a.operationTime), COUNT(a) FROM AuditLog a WHERE a.operationTime BETWEEN :startTime AND :endTime " +
           "AND a.deleted = false GROUP BY HOUR(a.operationTime) ORDER BY HOUR(a.operationTime)")
    List<Object[]> countByHour(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 按日期统计（最近7天）
     */
    @Query("SELECT DATE(a.operationTime), COUNT(a) FROM AuditLog a WHERE a.operationTime BETWEEN :startTime AND :endTime " +
           "AND a.deleted = false GROUP BY DATE(a.operationTime) ORDER BY DATE(a.operationTime)")
    List<Object[]> countByDate(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询最活跃用户TOP10
     */
    @Query("SELECT a.userId, a.username, a.realName, COUNT(a) as operationCount FROM AuditLog a " +
           "WHERE a.operationTime BETWEEN :startTime AND :endTime AND a.deleted = false " +
           "GROUP BY a.userId, a.username, a.realName ORDER BY operationCount DESC")
    List<Object[]> findTopActiveUsers(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, Pageable pageable);

    /**
     * 查询最频繁IP地址TOP10
     */
    @Query("SELECT a.ipAddress, COUNT(a) as operationCount, COUNT(DISTINCT a.userId) as userCount FROM AuditLog a " +
           "WHERE a.operationTime BETWEEN :startTime AND :endTime AND a.deleted = false " +
           "GROUP BY a.ipAddress ORDER BY operationCount DESC")
    List<Object[]> findTopActiveIps(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, Pageable pageable);

    /**
     * 根据关键词搜索操作描述
     */
    @Query("SELECT a FROM AuditLog a WHERE a.operationDescription LIKE %:keyword% AND a.deleted = false")
    Page<AuditLog> findByOperationDescriptionContaining(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 查询指定用户的操作历史
     */
    @Query("SELECT a FROM AuditLog a WHERE a.userId = :userId AND a.operationTime BETWEEN :startTime AND :endTime " +
           "AND a.deleted = false ORDER BY a.operationTime DESC")
    List<AuditLog> findUserOperationHistory(@Param("userId") Long userId,
                                           @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);

    /**
     * 查询指定对象的操作历史
     */
    @Query("SELECT a FROM AuditLog a WHERE a.objectId = :objectId AND a.deleted = false ORDER BY a.operationTime DESC")
    List<AuditLog> findObjectOperationHistory(@Param("objectId") String objectId);
}
