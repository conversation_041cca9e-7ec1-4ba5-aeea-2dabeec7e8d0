package com.hzwangda.edu.audit.entity;

import com.hzwangda.edu.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * 审计日志实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "audit_log", indexes = {
    @Index(name = "idx_audit_user_id", columnList = "user_id"),
    @Index(name = "idx_audit_operation_time", columnList = "operation_time"),
    @Index(name = "idx_audit_operation_type", columnList = "operation_type"),
    @Index(name = "idx_audit_module", columnList = "module"),
    @Index(name = "idx_audit_ip_address", columnList = "ip_address"),
    @Index(name = "idx_audit_operation_result", columnList = "operation_result")
})
@Schema(description = "审计日志")
public class AuditLog extends BaseEntity {

    @NotNull(message = "用户ID不能为空")
    @Column(name = "user_id", nullable = false)
    @Schema(description = "操作用户ID", example = "1")
    private Long userId;

    @NotBlank(message = "用户名不能为空")
    @Size(max = 64, message = "用户名长度不能超过64个字符")
    @Column(name = "username", length = 64, nullable = false)
    @Schema(description = "操作用户名", example = "admin")
    private String username;

    @Size(max = 64, message = "真实姓名长度不能超过64个字符")
    @Column(name = "real_name", length = 64)
    @Schema(description = "操作用户真实姓名", example = "张三")
    private String realName;

    @NotNull(message = "操作时间不能为空")
    @Column(name = "operation_time", nullable = false)
    @Schema(description = "操作时间")
    private LocalDateTime operationTime;

    @NotBlank(message = "操作类型不能为空")
    @Size(max = 32, message = "操作类型长度不能超过32个字符")
    @Column(name = "operation_type", length = 32, nullable = false)
    @Schema(description = "操作类型", example = "CREATE")
    private String operationType;

    @NotBlank(message = "业务模块不能为空")
    @Size(max = 64, message = "业务模块长度不能超过64个字符")
    @Column(name = "module", length = 64, nullable = false)
    @Schema(description = "业务模块", example = "EMPLOYEE")
    private String module;

    @Size(max = 128, message = "操作对象长度不能超过128个字符")
    @Column(name = "operation_object", length = 128)
    @Schema(description = "操作对象", example = "员工信息")
    private String operationObject;

    @Size(max = 64, message = "操作对象ID长度不能超过64个字符")
    @Column(name = "object_id", length = 64)
    @Schema(description = "操作对象ID", example = "E001")
    private String objectId;

    @NotBlank(message = "操作结果不能为空")
    @Size(max = 16, message = "操作结果长度不能超过16个字符")
    @Column(name = "operation_result", length = 16, nullable = false)
    @Schema(description = "操作结果", example = "SUCCESS")
    private String operationResult;

    @Size(max = 255, message = "操作描述长度不能超过255个字符")
    @Column(name = "operation_description", length = 255)
    @Schema(description = "操作描述", example = "创建新员工")
    private String operationDescription;

    @NotBlank(message = "IP地址不能为空")
    @Size(max = 45, message = "IP地址长度不能超过45个字符")
    @Column(name = "ip_address", length = 45, nullable = false)
    @Schema(description = "操作IP地址", example = "*************")
    private String ipAddress;

    @Size(max = 255, message = "用户代理长度不能超过255个字符")
    @Column(name = "user_agent", length = 255)
    @Schema(description = "用户代理", example = "Mozilla/5.0...")
    private String userAgent;

    @Size(max = 128, message = "请求URI长度不能超过128个字符")
    @Column(name = "request_uri", length = 128)
    @Schema(description = "请求URI", example = "/api/v1/employees")
    private String requestUri;

    @Size(max = 16, message = "请求方法长度不能超过16个字符")
    @Column(name = "request_method", length = 16)
    @Schema(description = "请求方法", example = "POST")
    private String requestMethod;

    @Column(name = "request_params", columnDefinition = "TEXT")
    @Schema(description = "请求参数")
    private String requestParams;

    @Column(name = "response_data", columnDefinition = "TEXT")
    @Schema(description = "响应数据")
    private String responseData;

    @Column(name = "execution_time")
    @Schema(description = "执行时间(毫秒)", example = "150")
    private Long executionTime;

    @Size(max = 500, message = "错误信息长度不能超过500个字符")
    @Column(name = "error_message", length = 500)
    @Schema(description = "错误信息")
    private String errorMessage;

    @Size(max = 64, message = "会话ID长度不能超过64个字符")
    @Column(name = "session_id", length = 64)
    @Schema(description = "会话ID")
    private String sessionId;

    @Size(max = 32, message = "风险级别长度不能超过32个字符")
    @Column(name = "risk_level", length = 32)
    @Schema(description = "风险级别", example = "LOW")
    private String riskLevel;

    // 构造函数
    public AuditLog() {
    }

    public AuditLog(Long userId, String username, String operationType, String module) {
        this.userId = userId;
        this.username = username;
        this.operationType = operationType;
        this.module = module;
        this.operationTime = LocalDateTime.now();
    }

    // Getter and Setter methods
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public LocalDateTime getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(LocalDateTime operationTime) {
        this.operationTime = operationTime;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public String getOperationObject() {
        return operationObject;
    }

    public void setOperationObject(String operationObject) {
        this.operationObject = operationObject;
    }

    public String getObjectId() {
        return objectId;
    }

    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    public String getOperationResult() {
        return operationResult;
    }

    public void setOperationResult(String operationResult) {
        this.operationResult = operationResult;
    }

    public String getOperationDescription() {
        return operationDescription;
    }

    public void setOperationDescription(String operationDescription) {
        this.operationDescription = operationDescription;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getRequestUri() {
        return requestUri;
    }

    public void setRequestUri(String requestUri) {
        this.requestUri = requestUri;
    }

    public String getRequestMethod() {
        return requestMethod;
    }

    public void setRequestMethod(String requestMethod) {
        this.requestMethod = requestMethod;
    }

    public String getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(String requestParams) {
        this.requestParams = requestParams;
    }

    public String getResponseData() {
        return responseData;
    }

    public void setResponseData(String responseData) {
        this.responseData = responseData;
    }

    public Long getExecutionTime() {
        return executionTime;
    }

    public void setExecutionTime(Long executionTime) {
        this.executionTime = executionTime;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }
}
