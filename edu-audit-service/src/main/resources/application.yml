# 服务配置
server:
  port: 8002
  servlet:
    context-path: /audit-service
    encoding:
      charset: UTF-8
      enabled: true
      force: true

# Spring配置
spring:
  application:
    name: edu-audit-service
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_SERVER_ADDR:127.0.0.1:8848}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:nacos}
        namespace: ${NACOS_NAMESPACE:hky-hr}

  # 数据源配置
  datasource:
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:************}:${DB_PORT:31252}/${DB_NAME:hky_hr_db}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${DB_USER:sasa}
    password: ${DB_PWD:RApubone95}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariCP-AuditService
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    show-sql: ${SPRING_JPA_SHOW_SQL:false}
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
        batch_versioned_data: true

  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:31000}
      password: ${REDIS_PWD:sjyt_cywKZHAl}
      database: ${REDIS_DB:6}
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 20
          max-wait: -1ms
          max-idle: 10
          min-idle: 5

  # Jackson配置
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# 日志配置
logging:
  level:
    com.hky.hr.audit: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/audit-service.log

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,tracing
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
  tracing:
    sampling:
      probability: 1.0
  zipkin:
    tracing:
      endpoint: ${ZIPKIN_ENDPOINT:http://localhost:9411/api/v2/spans}

# SpringDoc配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  info:
    title: 业务管理系统 - 审计日志服务 API
    description: 提供审计日志记录、查询、分析、导出等功能，确保系统操作的全程可追溯
    version: 1.0.0
    contact:
      name: HKY-HR-System
      email: <EMAIL>

# 审计日志配置
hky:
  audit:
    # 日志保留天数
    retention-days: 365
    # 批量处理大小
    batch-size: 1000
    # 异步处理队列大小
    async-queue-size: 10000
    # 高风险操作阈值
    high-risk-threshold: 100
    # 导出限制
    export-limit: 10000
    # 缓存过期时间（小时）
    cache-expire-hours: 1

# Seata分布式事务配置（暂时禁用）
seata:
  enabled: false