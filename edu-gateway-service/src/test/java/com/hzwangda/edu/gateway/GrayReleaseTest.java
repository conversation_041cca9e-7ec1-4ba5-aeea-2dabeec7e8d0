package com.hzwangda.edu.gateway;

import com.hzwangda.edu.gateway.config.GrayReleaseConfig;
import com.hzwangda.edu.gateway.service.GrayReleaseService;
import com.hzwangda.edu.gateway.strategy.impl.WeightGrayStrategy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.HttpHeaders;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.reactive.server.WebTestClient;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 灰度发布测试
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
public class GrayReleaseTest {
    
    @LocalServerPort
    private int port;
    
    @Autowired
    private GrayReleaseConfig grayReleaseConfig;
    
    @Autowired
    private GrayReleaseService grayReleaseService;
    
    @Autowired
    private WeightGrayStrategy weightGrayStrategy;
    
    private WebTestClient webTestClient;
    
    @BeforeEach
    public void setUp() {
        webTestClient = WebTestClient.bindToServer()
            .baseUrl("http://localhost:" + port)
            .responseTimeout(Duration.ofSeconds(10))
            .build();
        
        // 启用灰度发布
        grayReleaseConfig.setEnabled(true);
        grayReleaseConfig.setStrategy("weight");
    }
    
    @Test
    public void testGrayReleaseConfig() {
        // 测试灰度配置获取
        webTestClient.get()
            .uri("/gray-release/config")
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.enabled").isEqualTo(true)
            .jsonPath("$.strategy").isEqualTo("weight");
    }
    
    @Test
    public void testEnableDisableGrayRelease() {
        // 测试禁用灰度发布
        webTestClient.post()
            .uri("/gray-release/enable?enable=false")
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.success").isEqualTo(true)
            .jsonPath("$.enabled").isEqualTo(false);
        
        // 测试启用灰度发布
        webTestClient.post()
            .uri("/gray-release/enable?enable=true")
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.success").isEqualTo(true)
            .jsonPath("$.enabled").isEqualTo(true);
    }
    
    @Test
    public void testSwitchStrategy() {
        // 测试切换策略
        webTestClient.post()
            .uri("/gray-release/strategy?strategy=user")
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.success").isEqualTo(true)
            .jsonPath("$.strategy").isEqualTo("user");
    }
    
    @Test
    public void testUpdateServiceConfig() {
        // 准备测试数据
        GrayReleaseConfig.GrayRule rule = new GrayReleaseConfig.GrayRule();
        rule.setEnabled(true);
        rule.setWeight(30);
        rule.setVersion("v2.0.0-test");
        
        Map<String, Object> request = new HashMap<>();
        request.put("grayRule", rule);
        request.put("operation", "update");
        request.put("operator", "test");
        
        // 测试更新配置
        webTestClient.post()
            .uri("/gray-release/config/edu-auth-service")
            .bodyValue(request)
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.success").isEqualTo(true);
        
        // 验证更新结果
        GrayReleaseConfig.GrayRule updatedRule = grayReleaseConfig.getRules().get("edu-auth-service");
        assertNotNull(updatedRule);
        assertEquals(30, updatedRule.getWeight());
        assertEquals("v2.0.0-test", updatedRule.getVersion());
    }
    
    @Test
    public void testTrafficSwitch() {
        // 测试流量切换
        webTestClient.post()
            .uri("/gray-release/traffic/edu-file-service?weight=50")
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.success").isEqualTo(true);
        
        // 验证权重更新
        GrayReleaseConfig.GrayRule rule = grayReleaseConfig.getRules().get("edu-file-service");
        assertNotNull(rule);
        assertEquals(50, rule.getWeight());
    }
    
    @Test
    public void testRollback() {
        // 先设置灰度配置
        GrayReleaseConfig.GrayRule rule = new GrayReleaseConfig.GrayRule();
        rule.setEnabled(true);
        rule.setWeight(20);
        grayReleaseConfig.getRules().put("edu-workflow-service", rule);
        
        // 测试回滚
        webTestClient.post()
            .uri("/gray-release/rollback/edu-workflow-service")
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.success").isEqualTo(true);
        
        // 验证回滚结果
        GrayReleaseConfig.GrayRule rollbackedRule = grayReleaseConfig.getRules().get("edu-workflow-service");
        assertNotNull(rollbackedRule);
        assertFalse(rollbackedRule.isEnabled());
        assertEquals(0, rollbackedRule.getWeight());
    }
    
    @Test
    public void testWeightDistribution() {
        // 测试权重分布
        String serviceName = "edu-auth-service";
        GrayReleaseConfig.GrayRule rule = new GrayReleaseConfig.GrayRule();
        rule.setEnabled(true);
        rule.setWeight(30); // 30%灰度流量
        
        grayReleaseConfig.getRules().put(serviceName, rule);
        
        // 模拟1000次请求
        AtomicInteger grayCount = new AtomicInteger(0);
        AtomicInteger stableCount = new AtomicInteger(0);
        
        for (int i = 0; i < 1000; i++) {
            boolean shouldRouteToGray = weightGrayStrategy.shouldRouteToGray(null, serviceName, rule);
            if (shouldRouteToGray) {
                grayCount.incrementAndGet();
            } else {
                stableCount.incrementAndGet();
            }
        }
        
        // 验证分布是否接近配置的权重（允许5%误差）
        double grayPercentage = (double) grayCount.get() / 1000 * 100;
        assertTrue(Math.abs(grayPercentage - 30) < 5, 
            "灰度流量比例应接近30%，实际: " + grayPercentage + "%");
    }
    
    @Test
    public void testGrayRouteHeader() {
        // 配置灰度规则
        GrayReleaseConfig.GrayRule rule = new GrayReleaseConfig.GrayRule();
        rule.setEnabled(true);
        rule.setWeight(100); // 100%灰度
        grayReleaseConfig.getRules().put("edu-auth-service", rule);
        
        // 发送请求到认证服务
        webTestClient.get()
            .uri("/api/auth/health")
            .exchange()
            .expectStatus().isOk()
            .expectHeader().valueEquals("X-Gray-Version", "v2.0.0-gray")
            .expectHeader().valueEquals("X-Gray-Route", "true");
    }
    
    @Test
    public void testUserBasedGrayRouting() {
        // 切换到用户策略
        grayReleaseConfig.setStrategy("user");
        
        // 配置用户白名单
        GrayReleaseConfig.GrayRule rule = new GrayReleaseConfig.GrayRule();
        rule.setEnabled(true);
        rule.setUserWhitelist(new String[]{"admin", "test001"});
        grayReleaseConfig.getRules().put("edu-auth-service", rule);
        
        // 测试白名单用户
        webTestClient.get()
            .uri("/api/auth/health")
            .header("X-User-Id", "admin")
            .exchange()
            .expectStatus().isOk()
            .expectHeader().valueEquals("X-Gray-Route", "true");
        
        // 测试非白名单用户
        webTestClient.get()
            .uri("/api/auth/health")
            .header("X-User-Id", "user123")
            .exchange()
            .expectStatus().isOk()
            .expectHeader().valueEquals("X-Gray-Route", "false");
    }
    
    @Test
    public void testHeaderBasedGrayRouting() {
        // 切换到请求头策略
        grayReleaseConfig.setStrategy("header");
        
        // 配置请求头规则
        GrayReleaseConfig.GrayRule rule = new GrayReleaseConfig.GrayRule();
        rule.setEnabled(true);
        Map<String, String> headerRules = new HashMap<>();
        headerRules.put("X-Gray-Test", "true");
        headerRules.put("X-Test-Env", "gray");
        rule.setHeaderRules(headerRules);
        grayReleaseConfig.getRules().put("edu-auth-service", rule);
        
        // 测试匹配的请求头
        webTestClient.get()
            .uri("/api/auth/health")
            .header("X-Gray-Test", "true")
            .header("X-Test-Env", "gray")
            .exchange()
            .expectStatus().isOk()
            .expectHeader().valueEquals("X-Gray-Route", "true");
        
        // 测试不匹配的请求头
        webTestClient.get()
            .uri("/api/auth/health")
            .header("X-Gray-Test", "false")
            .exchange()
            .expectStatus().isOk()
            .expectHeader().valueEquals("X-Gray-Route", "false");
    }
}