package com.hzwangda.edu.gateway.filter;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Objects;
import java.util.UUID;

/**
 * 链路追踪过滤器
 * 实现简单的分布式追踪功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class TraceFilter implements GlobalFilter, Ordered {
    
    private static final String TRACE_ID_HEADER = "X-Trace-Id";
    private static final String SPAN_ID_HEADER = "X-Span-Id";
    private static final String PARENT_SPAN_ID_HEADER = "X-Parent-Span-Id";
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        
        // 获取或生成TraceId
        String traceId = request.getHeaders().getFirst(TRACE_ID_HEADER);
        if (traceId == null || traceId.isEmpty()) {
            traceId = generateTraceId();
        }
        
        // 获取父SpanId
        String parentSpanId = request.getHeaders().getFirst(SPAN_ID_HEADER);
        
        // 生成新的SpanId
        String spanId = generateSpanId();
        
        // 添加trace信息到请求头
        ServerHttpRequest modifiedRequest = request.mutate()
                .header(TRACE_ID_HEADER, traceId)
                .header(SPAN_ID_HEADER, spanId)
                .header(PARENT_SPAN_ID_HEADER, parentSpanId != null ? parentSpanId : "")
                .build();
        
        // 将trace信息存储到exchange属性中
        exchange.getAttributes().put("traceId", traceId);
        exchange.getAttributes().put("spanId", spanId);
        exchange.getAttributes().put("parentSpanId", parentSpanId);
        
        // 记录trace开始
        long startTime = System.currentTimeMillis();
        log.debug("Trace started - TraceId: {}, SpanId: {}, Path: {}", 
                traceId, spanId, request.getPath());
        
        return chain.filter(exchange.mutate().request(modifiedRequest).build())
                .doOnSuccess(aVoid -> {
                    // 成功完成，记录响应信息
                    long duration = System.currentTimeMillis() - startTime;
                    int statusCode = Objects.requireNonNull(exchange.getResponse().getStatusCode()).value();
                    
                    log.info("Trace completed - TraceId: {}, SpanId: {}, Status: {}, Duration: {}ms", 
                            traceId, spanId, statusCode, duration);
                })
                .doOnError(throwable -> {
                    // 发生错误，记录错误信息
                    long duration = System.currentTimeMillis() - startTime;
                    log.error("Trace error - TraceId: {}, SpanId: {}, Duration: {}ms, Error: {}", 
                            traceId, spanId, duration, throwable.getMessage(), throwable);
                });
    }
    
    /**
     * 生成TraceId
     */
    private String generateTraceId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 生成SpanId
     */
    private String generateSpanId() {
        return Long.toHexString(System.nanoTime());
    }
    
    @Override
    public int getOrder() {
        // 在日志过滤器之后，认证过滤器之前
        return Ordered.HIGHEST_PRECEDENCE + 10;
    }
}