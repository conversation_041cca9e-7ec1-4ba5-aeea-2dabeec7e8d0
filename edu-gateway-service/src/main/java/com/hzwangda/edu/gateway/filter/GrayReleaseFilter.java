package com.hzwangda.edu.gateway.filter;

import com.hzwangda.edu.gateway.config.GrayReleaseConfig;
import com.hzwangda.edu.gateway.service.GrayReleaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.net.URI;

/**
 * 灰度发布过滤器
 * 负责根据灰度策略路由请求到不同版本的服务
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
@Component
public class GrayReleaseFilter implements GlobalFilter, Ordered {
    
    private static final Logger logger = LoggerFactory.getLogger(GrayReleaseFilter.class);
    
    @Autowired
    private GrayReleaseConfig grayReleaseConfig;
    
    @Autowired
    private GrayReleaseService grayReleaseService;
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        // 如果灰度发布未启用，直接放行
        if (!grayReleaseConfig.isEnabled()) {
            return chain.filter(exchange);
        }
        
        // 获取路由信息
        Route route = exchange.getAttribute(ServerWebExchangeUtils.GATEWAY_ROUTE_ATTR);
        if (route == null) {
            return chain.filter(exchange);
        }
        
        // 从路由ID中提取服务名称
        String serviceName = extractServiceName(route.getId());
        if (serviceName == null) {
            return chain.filter(exchange);
        }
        
        // 获取灰度规则
        GrayReleaseConfig.GrayRule grayRule = grayReleaseConfig.getRules().get(serviceName);
        if (grayRule == null || !grayRule.isEnabled()) {
            return chain.filter(exchange);
        }
        
        try {
            // 判断是否路由到灰度版本
            boolean routeToGray = grayReleaseService.shouldRouteToGray(exchange, serviceName, grayRule);
            
            if (routeToGray) {
                // 修改路由到灰度版本
                URI grayUri = grayReleaseService.getGrayServiceUri(serviceName, grayRule);
                if (grayUri != null) {
                    logger.info("Routing request to gray version: service={}, uri={}", serviceName, grayUri);
                    
                    // 添加灰度标识到请求头
                    ServerWebExchange modifiedExchange = exchange.mutate()
                        .request(builder -> builder
                            .header("X-Gray-Version", grayRule.getVersion())
                            .header("X-Gray-Route", "true"))
                        .build();
                    
                    // 修改路由URI
                    modifiedExchange.getAttributes().put(ServerWebExchangeUtils.GATEWAY_REQUEST_URL_ATTR, grayUri);
                    
                    return chain.filter(modifiedExchange);
                }
            }
            
            // 添加稳定版本标识
            ServerWebExchange modifiedExchange = exchange.mutate()
                .request(builder -> builder
                    .header("X-Gray-Version", grayReleaseConfig.getDefaultVersion())
                    .header("X-Gray-Route", "false"))
                .build();
            
            return chain.filter(modifiedExchange);
            
        } catch (Exception e) {
            logger.error("Error in gray release filter: {}", e.getMessage(), e);
            // 出错时路由到稳定版本
            return chain.filter(exchange);
        }
    }
    
    @Override
    public int getOrder() {
        // 在路由选择之后，负载均衡之前执行
        return Ordered.LOWEST_PRECEDENCE - 100;
    }
    
    /**
     * 从路由ID中提取服务名称
     */
    private String extractServiceName(String routeId) {
        if (routeId == null) {
            return null;
        }
        
        // 假设路由ID格式为：edu-xxx-service-route
        if (routeId.endsWith("-route")) {
            return routeId.substring(0, routeId.length() - 6);
        }
        
        return routeId;
    }
}