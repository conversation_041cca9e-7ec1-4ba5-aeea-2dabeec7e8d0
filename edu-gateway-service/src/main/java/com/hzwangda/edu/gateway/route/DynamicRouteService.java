package com.hzwangda.edu.gateway.route;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.event.RefreshRoutesEvent;
import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.cloud.gateway.route.RouteDefinitionRepository;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 动态路由服务
 * 支持运行时动态添加、更新、删除路由规则
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DynamicRouteService {

    private final RouteDefinitionRepository routeDefinitionRepository;
    private final ApplicationEventPublisher publisher;

    /**
     * 添加路由
     */
    public Mono<Void> add(RouteDefinition definition) {
        return routeDefinitionRepository.save(Mono.just(definition))
                .then(Mono.fromRunnable(() -> {
                    log.info("Added route: {}", definition.getId());
                    this.publishRefreshEvent();
                }));
    }

    /**
     * 更新路由
     */
    public Mono<Void> update(RouteDefinition definition) {
        return this.delete(definition.getId())
                .then(this.add(definition));
    }

    /**
     * 删除路由
     */
    public Mono<Void> delete(String id) {
        return routeDefinitionRepository.delete(Mono.just(id))
                .then(Mono.fromRunnable(() -> {
                    log.info("Deleted route: {}", id);
                    this.publishRefreshEvent();
                }));
    }

    /**
     * 批量添加路由
     */
    public Mono<Void> addBatch(List<RouteDefinition> definitions) {
        return Flux.fromIterable(definitions)
                .flatMap(definition -> routeDefinitionRepository.save(Mono.just(definition)))
                .then(Mono.fromRunnable(() -> {
                    log.info("Added {} routes", definitions.size());
                    this.publishRefreshEvent();
                }));
    }

    /**
     * 获取所有路由
     */
    public Flux<RouteDefinition> getRoutes() {
        return routeDefinitionRepository.getRouteDefinitions();
    }

    /**
     * 发布路由刷新事件
     */
    private void publishRefreshEvent() {
        publisher.publishEvent(new RefreshRoutesEvent(this));
    }
    
    /**
     * 刷新路由缓存
     */
    public Mono<Void> refreshRoutes() {
        log.info("Refreshing routes cache");
        publisher.publishEvent(new RefreshRoutesEvent(this));
        return Mono.empty();
    }
}