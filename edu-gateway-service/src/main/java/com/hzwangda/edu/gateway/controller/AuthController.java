package com.hzwangda.edu.gateway.controller;

import com.hzwangda.edu.gateway.service.AuthCacheService;
import com.hzwangda.edu.gateway.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 * 提供令牌刷新、注销等功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/gateway/auth")
@RequiredArgsConstructor
public class AuthController {
    
    private final JwtUtil jwtUtil;
    private final AuthCacheService authCacheService;
    
    /**
     * 刷新令牌
     * 
     * @param authHeader 认证头
     * @return 新令牌
     */
    @PostMapping("/refresh")
    public Mono<ResponseEntity<Map<String, Object>>> refreshToken(
            @RequestHeader("Authorization") String authHeader) {
        
        // 提取令牌
        String token = extractToken(authHeader);
        if (token == null) {
            return Mono.just(ResponseEntity.badRequest()
                    .body(Map.of("error", "Invalid authorization header")));
        }
        
        // 验证令牌
        if (!jwtUtil.validateToken(token)) {
            return Mono.just(ResponseEntity.unauthorized()
                    .body(Map.of("error", "Invalid or expired token")));
        }
        
        // 检查是否即将过期（30分钟内）
        if (!jwtUtil.isTokenExpiringSoon(token, 1800)) {
            return Mono.just(ResponseEntity.ok()
                    .body(Map.of(
                            "message", "Token is still valid",
                            "token", token
                    )));
        }
        
        // 刷新令牌
        String newToken = jwtUtil.refreshToken(token);
        if (newToken == null) {
            return Mono.just(ResponseEntity.internalServerError()
                    .body(Map.of("error", "Failed to refresh token")));
        }
        
        // 将旧令牌加入黑名单
        return authCacheService.addToBlacklist(token, Duration.ofHours(24))
                .map(result -> {
                    Map<String, Object> response = new HashMap<>();
                    response.put("token", newToken);
                    response.put("type", "Bearer");
                    response.put("expiresIn", 86400); // 24小时
                    
                    log.info("Token refreshed for user: {}", jwtUtil.getUsername(newToken));
                    return ResponseEntity.ok(response);
                });
    }
    
    /**
     * 注销
     * 
     * @param authHeader 认证头
     * @return 注销结果
     */
    @PostMapping("/logout")
    public Mono<ResponseEntity<Map<String, String>>> logout(
            @RequestHeader("Authorization") String authHeader) {
        
        // 提取令牌
        String token = extractToken(authHeader);
        if (token == null) {
            return Mono.just(ResponseEntity.badRequest()
                    .body(Map.of("error", "Invalid authorization header")));
        }
        
        // 获取用户信息
        String username = jwtUtil.getUsername(token);
        String userId = jwtUtil.getUserId(token);
        
        // 将令牌加入黑名单
        return authCacheService.addToBlacklist(token, Duration.ofHours(24))
                .flatMap(result -> {
                    // 清除用户的所有认证缓存
                    return authCacheService.clearUserAuthCache(userId)
                            .map(count -> {
                                log.info("User {} logged out, cleared {} cache entries", 
                                        username, count);
                                return ResponseEntity.ok(Map.of(
                                        "message", "Logout successful",
                                        "username", username
                                ));
                            });
                });
    }
    
    /**
     * 验证令牌
     * 
     * @param authHeader 认证头
     * @return 验证结果
     */
    @GetMapping("/validate")
    public Mono<ResponseEntity<Map<String, Object>>> validateToken(
            @RequestHeader("Authorization") String authHeader) {
        
        // 提取令牌
        String token = extractToken(authHeader);
        if (token == null) {
            return Mono.just(ResponseEntity.badRequest()
                    .body(Map.of(
                            "valid", false,
                            "error", "Invalid authorization header"
                    )));
        }
        
        // 检查黑名单
        return authCacheService.isBlacklisted(token)
                .map(blacklisted -> {
                    if (blacklisted) {
                        return ResponseEntity.ok(Map.of(
                                "valid", false,
                                "error", "Token is blacklisted"
                        ));
                    }
                    
                    // 验证令牌
                    boolean valid = jwtUtil.validateToken(token);
                    if (!valid) {
                        return ResponseEntity.ok(Map.of(
                                "valid", false,
                                "error", "Invalid or expired token"
                        ));
                    }
                    
                    // 返回令牌信息
                    Map<String, Object> response = new HashMap<>();
                    response.put("valid", true);
                    response.put("userId", jwtUtil.getUserId(token));
                    response.put("username", jwtUtil.getUsername(token));
                    response.put("roles", jwtUtil.getRoles(token));
                    response.put("tenantId", jwtUtil.getTenantId(token));
                    response.put("expiringSoon", jwtUtil.isTokenExpiringSoon(token, 1800));
                    
                    return ResponseEntity.ok(response);
                });
    }
    
    /**
     * 提取令牌
     */
    private String extractToken(String authHeader) {
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        return null;
    }
}