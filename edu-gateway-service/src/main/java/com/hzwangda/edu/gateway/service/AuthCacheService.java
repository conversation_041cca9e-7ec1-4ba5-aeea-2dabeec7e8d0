package com.hzwangda.edu.gateway.service;

import com.hzwangda.edu.gateway.context.AuthContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Duration;

/**
 * 认证缓存服务
 * 缓存JWT验证结果以提高性能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthCacheService {
    
    private final ReactiveRedisTemplate<String, String> redisTemplate;
    
    /**
     * 缓存键前缀
     */
    private static final String CACHE_PREFIX = "gateway:auth:";
    
    /**
     * 缓存过期时间（5分钟）
     */
    private static final Duration CACHE_DURATION = Duration.ofMinutes(5);
    
    /**
     * 黑名单键前缀
     */
    private static final String BLACKLIST_PREFIX = "gateway:blacklist:";
    
    /**
     * 获取缓存的认证信息
     *
     * @param token JWT令牌
     * @return 认证上下文
     */
    public Mono<AuthContext> getCachedAuth(String token) {
        String key = CACHE_PREFIX + token;
        
        return redisTemplate.opsForValue().get(key)
                .map(json -> {
                    // 这里简化处理，实际应该使用JSON反序列化
                    log.debug("Found cached auth for token");
                    return null; // TODO: 实现反序列化
                })
                .doOnError(e -> log.error("Failed to get cached auth", e))
                .onErrorReturn(null);
    }
    
    /**
     * 缓存认证信息
     *
     * @param token JWT令牌
     * @param authContext 认证上下文
     * @return 操作结果
     */
    public Mono<Boolean> cacheAuth(String token, AuthContext authContext) {
        String key = CACHE_PREFIX + token;
        
        // 这里简化处理，实际应该使用JSON序列化
        String value = authContext.getUserId(); // TODO: 实现序列化
        
        return redisTemplate.opsForValue()
                .set(key, value, CACHE_DURATION)
                .doOnSuccess(result -> {
                    if (Boolean.TRUE.equals(result)) {
                        log.debug("Cached auth for user: {}", authContext.getUsername());
                    }
                })
                .doOnError(e -> log.error("Failed to cache auth", e))
                .onErrorReturn(false);
    }
    
    /**
     * 检查令牌是否在黑名单中
     *
     * @param token JWT令牌
     * @return 是否在黑名单
     */
    public Mono<Boolean> isBlacklisted(String token) {
        String key = BLACKLIST_PREFIX + token;
        
        return redisTemplate.hasKey(key)
                .doOnError(e -> log.error("Failed to check blacklist", e))
                .onErrorReturn(false);
    }
    
    /**
     * 将令牌加入黑名单
     *
     * @param token JWT令牌
     * @param duration 黑名单持续时间
     * @return 操作结果
     */
    public Mono<Boolean> addToBlacklist(String token, Duration duration) {
        String key = BLACKLIST_PREFIX + token;
        
        return redisTemplate.opsForValue()
                .set(key, "1", duration)
                .doOnSuccess(result -> {
                    if (Boolean.TRUE.equals(result)) {
                        log.info("Added token to blacklist");
                    }
                })
                .doOnError(e -> log.error("Failed to add to blacklist", e))
                .onErrorReturn(false);
    }
    
    /**
     * 清除认证缓存
     *
     * @param token JWT令牌
     * @return 操作结果
     */
    public Mono<Long> clearAuthCache(String token) {
        String key = CACHE_PREFIX + token;
        
        return redisTemplate.delete(key)
                .doOnSuccess(count -> {
                    if (count > 0) {
                        log.debug("Cleared auth cache for token");
                    }
                })
                .doOnError(e -> log.error("Failed to clear auth cache", e))
                .onErrorReturn(0L);
    }
    
    /**
     * 批量清除用户的所有认证缓存
     *
     * @param userId 用户ID
     * @return 清除数量
     */
    public Mono<Long> clearUserAuthCache(String userId) {
        // 使用模式匹配查找该用户的所有缓存
        String pattern = CACHE_PREFIX + "*";
        
        return redisTemplate.keys(pattern)
                .flatMap(key -> redisTemplate.opsForValue().get(key)
                        .filter(value -> value.contains(userId))
                        .flatMap(value -> redisTemplate.delete(key))
                )
                .reduce(0L, Long::sum)
                .doOnSuccess(count -> {
                    if (count > 0) {
                        log.info("Cleared {} auth cache entries for user: {}", count, userId);
                    }
                })
                .doOnError(e -> log.error("Failed to clear user auth cache", e))
                .onErrorReturn(0L);
    }
}