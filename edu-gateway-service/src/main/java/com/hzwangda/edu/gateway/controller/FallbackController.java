package com.hzwangda.edu.gateway.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

/**
 * 降级处理控制器
 * 提供服务降级时的备用响应
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/fallback")
public class FallbackController {
    
    /**
     * 通用降级处理
     */
    @RequestMapping(value = "/**", method = {RequestMethod.GET, RequestMethod.POST, 
            RequestMethod.PUT, RequestMethod.DELETE})
    public Mono<ResponseEntity<Map<String, Object>>> fallback(
            @RequestHeader(value = "X-Request-Id", required = false) String requestId,
            @RequestHeader(value = "X-Service-Name", required = false) String serviceName) {
        
        log.warn("Fallback triggered for service: {}, requestId: {}", serviceName, requestId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 503);
        response.put("message", "Service temporarily unavailable, please try again later");
        response.put("timestamp", System.currentTimeMillis());
        response.put("requestId", requestId);
        response.put("service", serviceName);
        
        return Mono.just(ResponseEntity
                .status(HttpStatus.SERVICE_UNAVAILABLE)
                .contentType(MediaType.APPLICATION_JSON)
                .body(response));
    }
    
    /**
     * 认证服务降级
     */
    @PostMapping("/auth")
    public Mono<ResponseEntity<Map<String, Object>>> authFallback() {
        log.warn("Auth service fallback triggered");
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 503);
        response.put("message", "Authentication service is temporarily unavailable");
        response.put("timestamp", System.currentTimeMillis());
        response.put("suggestion", "Please use cached credentials or try again later");
        
        return Mono.just(ResponseEntity
                .status(HttpStatus.SERVICE_UNAVAILABLE)
                .contentType(MediaType.APPLICATION_JSON)
                .body(response));
    }
    
    /**
     * 文件服务降级
     */
    @RequestMapping(value = "/file", method = {RequestMethod.GET, RequestMethod.POST})
    public Mono<ResponseEntity<Map<String, Object>>> fileFallback() {
        log.warn("File service fallback triggered");
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 503);
        response.put("message", "File service is temporarily unavailable");
        response.put("timestamp", System.currentTimeMillis());
        response.put("suggestion", "File operations are queued and will be processed when service recovers");
        
        return Mono.just(ResponseEntity
                .status(HttpStatus.SERVICE_UNAVAILABLE)
                .contentType(MediaType.APPLICATION_JSON)
                .body(response));
    }
    
    /**
     * 工作流服务降级
     */
    @RequestMapping("/workflow")
    public Mono<ResponseEntity<Map<String, Object>>> workflowFallback() {
        log.warn("Workflow service fallback triggered");
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 503);
        response.put("message", "Workflow service is experiencing high load");
        response.put("timestamp", System.currentTimeMillis());
        response.put("suggestion", "Your request has been queued and will be processed soon");
        response.put("queuePosition", generateQueuePosition());
        
        return Mono.just(ResponseEntity
                .status(HttpStatus.SERVICE_UNAVAILABLE)
                .contentType(MediaType.APPLICATION_JSON)
                .body(response));
    }
    
    /**
     * 通知服务降级
     */
    @PostMapping("/notification")
    public Mono<ResponseEntity<Map<String, Object>>> notificationFallback(
            @RequestBody(required = false) Map<String, Object> notification) {
        
        log.warn("Notification service fallback triggered");
        
        // 将通知请求加入延迟队列
        if (notification != null) {
            queueNotification(notification);
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 202);
        response.put("message", "Notification queued for delayed delivery");
        response.put("timestamp", System.currentTimeMillis());
        response.put("notificationId", generateNotificationId());
        response.put("estimatedDelivery", System.currentTimeMillis() + 300000); // 5分钟后
        
        return Mono.just(ResponseEntity
                .status(HttpStatus.ACCEPTED)
                .contentType(MediaType.APPLICATION_JSON)
                .body(response));
    }
    
    /**
     * 审计服务降级
     */
    @PostMapping("/audit")
    public Mono<ResponseEntity<Map<String, Object>>> auditFallback(
            @RequestBody(required = false) Map<String, Object> auditLog) {
        
        log.warn("Audit service fallback triggered");
        
        // 将审计日志写入本地文件或缓存
        if (auditLog != null) {
            cacheAuditLog(auditLog);
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 202);
        response.put("message", "Audit log cached for later processing");
        response.put("timestamp", System.currentTimeMillis());
        response.put("cacheId", generateCacheId());
        
        return Mono.just(ResponseEntity
                .status(HttpStatus.ACCEPTED)
                .contentType(MediaType.APPLICATION_JSON)
                .body(response));
    }
    
    /**
     * 字典服务降级
     */
    @GetMapping("/dictionary")
    public Mono<ResponseEntity<Map<String, Object>>> dictionaryFallback(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String code) {
        
        log.warn("Dictionary service fallback triggered for type: {}, code: {}", type, code);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "Using cached dictionary data");
        response.put("timestamp", System.currentTimeMillis());
        response.put("data", getCachedDictionaryData(type, code));
        response.put("cached", true);
        
        return Mono.just(ResponseEntity
                .ok()
                .contentType(MediaType.APPLICATION_JSON)
                .body(response));
    }
    
    /**
     * 生成队列位置
     */
    private int generateQueuePosition() {
        return (int) (Math.random() * 100) + 1;
    }
    
    /**
     * 生成通知ID
     */
    private String generateNotificationId() {
        return "NTF" + System.currentTimeMillis();
    }
    
    /**
     * 生成缓存ID
     */
    private String generateCacheId() {
        return "CACHE" + System.currentTimeMillis();
    }
    
    /**
     * 队列化通知（模拟）
     */
    private void queueNotification(Map<String, Object> notification) {
        log.info("Queued notification: {}", notification);
        // 实际项目中应该将通知写入消息队列或数据库
    }
    
    /**
     * 缓存审计日志（模拟）
     */
    private void cacheAuditLog(Map<String, Object> auditLog) {
        log.info("Cached audit log: {}", auditLog);
        // 实际项目中应该将审计日志写入本地文件或Redis
    }
    
    /**
     * 获取缓存的字典数据（模拟）
     */
    private Map<String, Object> getCachedDictionaryData(String type, String code) {
        Map<String, Object> data = new HashMap<>();
        
        // 模拟一些常用的缓存数据
        if ("gender".equals(type)) {
            data.put("1", "男");
            data.put("2", "女");
        } else if ("status".equals(type)) {
            data.put("0", "禁用");
            data.put("1", "启用");
        } else {
            data.put("default", "默认值");
        }
        
        return data;
    }
}