package com.hzwangda.edu.gateway.filter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Map;

/**
 * 日志上下文
 * 存储请求响应的完整信息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class LogContext {
    
    // 基本信息
    private String requestId;
    private long timestamp;
    private String method;
    private String path;
    private String uri;
    private String clientIp;
    
    // 认证信息
    private String userId;
    private String username;
    private String tenantId;
    
    // 路由信息
    private String routeId;
    private String targetUri;
    
    // 请求信息
    private Map<String, String> queryParams;
    private Map<String, String> requestHeaders;
    private String requestBody;
    
    // 响应信息
    private int statusCode;
    private Map<String, String> responseHeaders;
    private String responseBody;
    private long duration;
    
    // 错误信息
    private String error;
    private String errorType;
    
    // 扩展信息
    private Map<String, Object> extras;
    
    /**
     * 转换为日志字符串
     */
    @JsonIgnore
    public String toLogString() {
        StringBuilder sb = new StringBuilder();
        sb.append("RequestId=").append(requestId);
        sb.append(", Method=").append(method);
        sb.append(", Path=").append(path);
        sb.append(", ClientIP=").append(clientIp);
        if (userId != null) {
            sb.append(", UserId=").append(userId);
        }
        if (username != null) {
            sb.append(", Username=").append(username);
        }
        if (routeId != null) {
            sb.append(", RouteId=").append(routeId);
        }
        if (statusCode > 0) {
            sb.append(", Status=").append(statusCode);
        }
        if (duration > 0) {
            sb.append(", Duration=").append(duration).append("ms");
        }
        if (error != null) {
            sb.append(", Error=").append(error);
        }
        return sb.toString();
    }
    
    /**
     * 是否是错误响应
     */
    @JsonIgnore
    public boolean isError() {
        return error != null || statusCode >= 400;
    }
    
    /**
     * 获取日志级别
     */
    @JsonIgnore
    public String getLogLevel() {
        if (error != null) {
            return "ERROR";
        } else if (statusCode >= 500) {
            return "ERROR";
        } else if (statusCode >= 400) {
            return "WARN";
        } else if (duration > 3000) {
            return "WARN";
        } else {
            return "INFO";
        }
    }
}