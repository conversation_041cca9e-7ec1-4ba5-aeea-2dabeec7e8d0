package com.hzwangda.edu.gateway.context;

import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 认证上下文
 * 存储当前请求的用户认证信息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
public class AuthContext {
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 用户角色列表
     */
    private List<String> roles;
    
    /**
     * 用户权限列表
     */
    private List<String> permissions;
    
    /**
     * JWT令牌
     */
    private String token;
    
    /**
     * 令牌过期时间（时间戳）
     */
    private Long expiration;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 请求ID（用于链路追踪）
     */
    private String requestId;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;
    
    /**
     * 是否已认证
     */
    private boolean authenticated;
    
    /**
     * 认证时间
     */
    private Long authTime;
    
    /**
     * 检查是否拥有指定角色
     *
     * @param role 角色名称
     * @return 是否拥有
     */
    public boolean hasRole(String role) {
        return roles != null && roles.contains(role);
    }
    
    /**
     * 检查是否拥有任一角色
     *
     * @param roles 角色列表
     * @return 是否拥有
     */
    public boolean hasAnyRole(List<String> roles) {
        if (this.roles == null || roles == null) {
            return false;
        }
        return roles.stream().anyMatch(this.roles::contains);
    }
    
    /**
     * 检查是否拥有指定权限
     *
     * @param permission 权限名称
     * @return 是否拥有
     */
    public boolean hasPermission(String permission) {
        return permissions != null && permissions.contains(permission);
    }
    
    /**
     * 检查是否拥有任一权限
     *
     * @param permissions 权限列表
     * @return 是否拥有
     */
    public boolean hasAnyPermission(List<String> permissions) {
        if (this.permissions == null || permissions == null) {
            return false;
        }
        return permissions.stream().anyMatch(this.permissions::contains);
    }
    
    /**
     * 获取扩展属性
     *
     * @param key 属性键
     * @return 属性值
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key) {
        return attributes != null ? (T) attributes.get(key) : null;
    }
    
    /**
     * 设置扩展属性
     *
     * @param key 属性键
     * @param value 属性值
     */
    public void setAttribute(String key, Object value) {
        if (attributes == null) {
            attributes = new HashMap<>();
        }
        attributes.put(key, value);
    }
}