package com.hzwangda.edu.gateway.strategy.impl;

import com.hzwangda.edu.gateway.config.GrayReleaseConfig;
import com.hzwangda.edu.gateway.strategy.GrayReleaseStrategy;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;

import java.util.Arrays;

/**
 * 基于用户的灰度策略
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
@Component("userGrayStrategy")
public class UserGrayStrategy implements GrayReleaseStrategy {
    
    private static final String USER_ID_HEADER = "X-User-Id";
    private static final String USER_NAME_HEADER = "X-User-Name";
    
    @Override
    public boolean shouldRouteToGray(ServerWebExchange exchange, String serviceName, GrayReleaseConfig.GrayRule grayRule) {
        if (grayRule == null || !grayRule.isEnabled()) {
            return false;
        }
        
        String[] userWhitelist = grayRule.getUserWhitelist();
        if (userWhitelist == null || userWhitelist.length == 0) {
            return false;
        }
        
        // 从请求头获取用户信息
        String userId = exchange.getRequest().getHeaders().getFirst(USER_ID_HEADER);
        String userName = exchange.getRequest().getHeaders().getFirst(USER_NAME_HEADER);
        
        // 检查用户是否在白名单中
        return Arrays.asList(userWhitelist).contains(userId) || 
               Arrays.asList(userWhitelist).contains(userName);
    }
    
    @Override
    public String getStrategyName() {
        return "user";
    }
}