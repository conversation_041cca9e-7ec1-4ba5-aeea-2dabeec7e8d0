package com.hzwangda.edu.gateway.controller;

import com.hzwangda.edu.gateway.config.GrayReleaseConfig;
import com.hzwangda.edu.gateway.dto.GrayReleaseRequest;
import com.hzwangda.edu.gateway.dto.GrayReleaseResponse;
import com.hzwangda.edu.gateway.service.GrayReleaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 灰度发布管理控制器
 * 提供灰度配置管理和流量切换功能
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
@RestController
@RequestMapping("/gray-release")
public class GrayReleaseController {
    
    private static final Logger logger = LoggerFactory.getLogger(GrayReleaseController.class);
    
    @Autowired
    private GrayReleaseService grayReleaseService;
    
    @Autowired
    private GrayReleaseConfig grayReleaseConfig;
    
    /**
     * 获取当前灰度配置
     */
    @GetMapping("/config")
    public ResponseEntity<GrayReleaseResponse> getConfig() {
        GrayReleaseResponse response = new GrayReleaseResponse();
        response.setEnabled(grayReleaseConfig.isEnabled());
        response.setStrategy(grayReleaseConfig.getStrategy());
        response.setRules(grayReleaseConfig.getRules());
        response.setMessage("获取灰度配置成功");
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取指定服务的灰度配置
     */
    @GetMapping("/config/{serviceName}")
    public ResponseEntity<GrayReleaseResponse> getServiceConfig(@PathVariable String serviceName) {
        GrayReleaseResponse response = new GrayReleaseResponse();
        GrayReleaseConfig.GrayRule rule = grayReleaseConfig.getRules().get(serviceName);
        
        if (rule == null) {
            response.setSuccess(false);
            response.setMessage("服务 " + serviceName + " 未配置灰度规则");
            return ResponseEntity.ok(response);
        }
        
        Map<String, GrayReleaseConfig.GrayRule> singleRule = new HashMap<>();
        singleRule.put(serviceName, rule);
        response.setRules(singleRule);
        response.setMessage("获取服务灰度配置成功");
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 更新灰度配置
     */
    @PostMapping("/config/{serviceName}")
    public ResponseEntity<GrayReleaseResponse> updateConfig(
            @PathVariable String serviceName,
            @RequestBody GrayReleaseRequest request) {
        
        GrayReleaseResponse response = new GrayReleaseResponse();
        
        try {
            // 更新灰度规则
            grayReleaseService.updateGrayRule(serviceName, request.getGrayRule());
            
            response.setMessage("灰度配置更新成功");
            logger.info("Updated gray release config for service: {}", serviceName);
            
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage("更新灰度配置失败: " + e.getMessage());
            logger.error("Failed to update gray release config", e);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 启用/禁用灰度发布
     */
    @PostMapping("/enable")
    public ResponseEntity<GrayReleaseResponse> enableGrayRelease(@RequestParam boolean enable) {
        GrayReleaseResponse response = new GrayReleaseResponse();
        
        grayReleaseConfig.setEnabled(enable);
        response.setEnabled(enable);
        response.setMessage(enable ? "灰度发布已启用" : "灰度发布已禁用");
        
        logger.info("Gray release {}", enable ? "enabled" : "disabled");
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 切换灰度策略
     */
    @PostMapping("/strategy")
    public ResponseEntity<GrayReleaseResponse> switchStrategy(@RequestParam String strategy) {
        GrayReleaseResponse response = new GrayReleaseResponse();
        
        grayReleaseConfig.setStrategy(strategy);
        response.setStrategy(strategy);
        response.setMessage("灰度策略已切换为: " + strategy);
        
        logger.info("Switched gray release strategy to: {}", strategy);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取服务实例版本信息
     */
    @GetMapping("/instances/{serviceName}")
    public ResponseEntity<Map<String, Object>> getServiceInstances(@PathVariable String serviceName) {
        Map<String, List<ServiceInstance>> versions = grayReleaseService.getServiceVersions(serviceName);
        
        Map<String, Object> result = new HashMap<>();
        result.put("service", serviceName);
        result.put("versions", versions);
        result.put("totalInstances", versions.values().stream().mapToInt(List::size).sum());
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 流量切换
     */
    @PostMapping("/traffic/{serviceName}")
    public ResponseEntity<GrayReleaseResponse> switchTraffic(
            @PathVariable String serviceName,
            @RequestParam int weight) {
        
        GrayReleaseResponse response = new GrayReleaseResponse();
        
        try {
            // 获取或创建灰度规则
            GrayReleaseConfig.GrayRule rule = grayReleaseConfig.getRules().get(serviceName);
            if (rule == null) {
                rule = new GrayReleaseConfig.GrayRule();
                rule.setEnabled(true);
            }
            
            // 更新权重
            rule.setWeight(weight);
            grayReleaseService.updateGrayRule(serviceName, rule);
            
            response.setMessage(String.format("服务 %s 的灰度流量已调整为 %d%%", serviceName, weight));
            logger.info("Adjusted gray traffic for service {} to {}%", serviceName, weight);
            
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage("流量切换失败: " + e.getMessage());
            logger.error("Failed to switch traffic", e);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 回滚操作
     */
    @PostMapping("/rollback/{serviceName}")
    public ResponseEntity<GrayReleaseResponse> rollback(@PathVariable String serviceName) {
        GrayReleaseResponse response = new GrayReleaseResponse();
        
        try {
            // 禁用灰度规则
            GrayReleaseConfig.GrayRule rule = grayReleaseConfig.getRules().get(serviceName);
            if (rule != null) {
                rule.setEnabled(false);
                rule.setWeight(0);
                grayReleaseService.updateGrayRule(serviceName, rule);
            }
            
            response.setMessage(String.format("服务 %s 已回滚到稳定版本", serviceName));
            logger.info("Rolled back service {} to stable version", serviceName);
            
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage("回滚失败: " + e.getMessage());
            logger.error("Failed to rollback", e);
        }
        
        return ResponseEntity.ok(response);
    }
}