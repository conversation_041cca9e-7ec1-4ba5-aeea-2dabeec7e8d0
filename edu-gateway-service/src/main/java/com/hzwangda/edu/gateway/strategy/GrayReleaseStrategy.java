package com.hzwangda.edu.gateway.strategy;

import com.hzwangda.edu.gateway.config.GrayReleaseConfig;
import org.springframework.web.server.ServerWebExchange;

/**
 * 灰度发布策略接口
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
public interface GrayReleaseStrategy {
    
    /**
     * 判断是否路由到灰度版本
     * 
     * @param exchange 请求交换对象
     * @param serviceName 服务名称
     * @param grayRule 灰度规则
     * @return true: 路由到灰度版本, false: 路由到稳定版本
     */
    boolean shouldRouteToGray(ServerWebExchange exchange, String serviceName, GrayReleaseConfig.GrayRule grayRule);
    
    /**
     * 获取策略名称
     * 
     * @return 策略名称
     */
    String getStrategyName();
}