package com.hzwangda.edu.gateway.config;

import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.ReactiveRedisConnectionFactory;

/**
 * 健康检查配置
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class HealthCheckConfig {

    /**
     * 服务发现健康检查
     */
    @Bean
    public HealthIndicator discoveryHealthIndicator(DiscoveryClient discoveryClient) {
        return () -> {
            try {
                int serviceCount = discoveryClient.getServices().size();
                return Health.up()
                        .withDetail("services", serviceCount)
                        .withDetail("status", "Service discovery is working")
                        .build();
            } catch (Exception e) {
                return Health.down()
                        .withDetail("error", e.getMessage())
                        .build();
            }
        };
    }

    /**
     * Redis健康检查
     */
    @Bean
    public HealthIndicator redisHealthIndicator(ReactiveRedisConnectionFactory connectionFactory) {
        return () -> {
            try {
                connectionFactory.getReactiveConnection()
                        .ping()
                        .block();
                return Health.up()
                        .withDetail("status", "Redis connection is working")
                        .build();
            } catch (Exception e) {
                return Health.down()
                        .withDetail("error", e.getMessage())
                        .build();
            }
        };
    }
}