package com.hzwangda.edu.gateway.strategy.impl;

import com.hzwangda.edu.gateway.config.GrayReleaseConfig;
import com.hzwangda.edu.gateway.strategy.GrayReleaseStrategy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;

import java.net.InetSocketAddress;
import java.util.Arrays;

/**
 * 基于IP的灰度策略
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
@Component("ipGrayStrategy")
public class IpGrayStrategy implements GrayReleaseStrategy {
    
    @Override
    public boolean shouldRouteToGray(ServerWebExchange exchange, String serviceName, GrayReleaseConfig.GrayRule grayRule) {
        if (grayRule == null || !grayRule.isEnabled()) {
            return false;
        }
        
        String[] ipWhitelist = grayRule.getIpWhitelist();
        if (ipWhitelist == null || ipWhitelist.length == 0) {
            return false;
        }
        
        // 获取客户端IP
        String clientIp = getClientIp(exchange);
        if (!StringUtils.hasText(clientIp)) {
            return false;
        }
        
        // 检查IP是否在白名单中
        return Arrays.asList(ipWhitelist).contains(clientIp) || 
               matchIpPattern(clientIp, ipWhitelist);
    }
    
    @Override
    public String getStrategyName() {
        return "ip";
    }
    
    /**
     * 获取客户端真实IP
     */
    private String getClientIp(ServerWebExchange exchange) {
        // 优先从X-Forwarded-For获取
        String ip = exchange.getRequest().getHeaders().getFirst("X-Forwarded-For");
        if (StringUtils.hasText(ip) && !"unknown".equalsIgnoreCase(ip)) {
            // 多次反向代理后会有多个IP值，第一个为真实IP
            int index = ip.indexOf(',');
            if (index != -1) {
                return ip.substring(0, index);
            }
            return ip;
        }
        
        // 从X-Real-IP获取
        ip = exchange.getRequest().getHeaders().getFirst("X-Real-IP");
        if (StringUtils.hasText(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        
        // 从RemoteAddress获取
        InetSocketAddress remoteAddress = exchange.getRequest().getRemoteAddress();
        if (remoteAddress != null) {
            return remoteAddress.getAddress().getHostAddress();
        }
        
        return null;
    }
    
    /**
     * 匹配IP模式（支持通配符）
     */
    private boolean matchIpPattern(String ip, String[] patterns) {
        for (String pattern : patterns) {
            if (pattern.contains("*")) {
                String regex = pattern.replace(".", "\\.").replace("*", ".*");
                if (ip.matches(regex)) {
                    return true;
                }
            }
        }
        return false;
    }
}