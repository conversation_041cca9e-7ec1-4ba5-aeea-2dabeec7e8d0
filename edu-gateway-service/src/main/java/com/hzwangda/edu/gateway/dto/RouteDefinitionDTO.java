package com.hzwangda.edu.gateway.dto;

import lombok.Data;
import org.springframework.cloud.gateway.filter.FilterDefinition;
import org.springframework.cloud.gateway.handler.predicate.PredicateDefinition;
import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.util.StringUtils;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 路由定义DTO
 * 用于接收前端路由配置参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class RouteDefinitionDTO {
    
    /**
     * 路由ID
     */
    private String id;
    
    /**
     * 路由URI
     * 支持 http://、https://、lb:// 等协议
     */
    private String uri;
    
    /**
     * 路由优先级，数字越小优先级越高
     */
    private int order = 0;
    
    /**
     * 断言配置列表
     */
    private List<PredicateDTO> predicates;
    
    /**
     * 过滤器配置列表
     */
    private List<FilterDTO> filters;
    
    /**
     * 元数据
     */
    private Map<String, Object> metadata;
    
    /**
     * 是否启用
     */
    private boolean enabled = true;
    
    /**
     * 路由描述
     */
    private String description;
    
    /**
     * 转换为Spring Gateway的RouteDefinition
     */
    public RouteDefinition toRouteDefinition() {
        RouteDefinition routeDefinition = new RouteDefinition();
        
        // 设置基本属性
        routeDefinition.setId(this.id);
        routeDefinition.setUri(URI.create(this.uri));
        routeDefinition.setOrder(this.order);
        
        // 设置断言
        if (this.predicates != null && !this.predicates.isEmpty()) {
            List<PredicateDefinition> predicateDefinitions = new ArrayList<>();
            for (PredicateDTO predicateDTO : this.predicates) {
                predicateDefinitions.add(predicateDTO.toPredicateDefinition());
            }
            routeDefinition.setPredicates(predicateDefinitions);
        }
        
        // 设置过滤器
        if (this.filters != null && !this.filters.isEmpty()) {
            List<FilterDefinition> filterDefinitions = new ArrayList<>();
            for (FilterDTO filterDTO : this.filters) {
                filterDefinitions.add(filterDTO.toFilterDefinition());
            }
            routeDefinition.setFilters(filterDefinitions);
        }
        
        // 设置元数据
        if (this.metadata != null && !this.metadata.isEmpty()) {
            routeDefinition.setMetadata(this.metadata);
        }
        
        return routeDefinition;
    }
    
    /**
     * 从RouteDefinition创建DTO
     */
    public static RouteDefinitionDTO fromRouteDefinition(RouteDefinition routeDefinition) {
        RouteDefinitionDTO dto = new RouteDefinitionDTO();
        dto.setId(routeDefinition.getId());
        dto.setUri(routeDefinition.getUri().toString());
        dto.setOrder(routeDefinition.getOrder());
        dto.setMetadata(routeDefinition.getMetadata());
        
        // 转换断言
        if (routeDefinition.getPredicates() != null) {
            List<PredicateDTO> predicateDTOs = new ArrayList<>();
            for (PredicateDefinition predicate : routeDefinition.getPredicates()) {
                predicateDTOs.add(PredicateDTO.fromPredicateDefinition(predicate));
            }
            dto.setPredicates(predicateDTOs);
        }
        
        // 转换过滤器
        if (routeDefinition.getFilters() != null) {
            List<FilterDTO> filterDTOs = new ArrayList<>();
            for (FilterDefinition filter : routeDefinition.getFilters()) {
                filterDTOs.add(FilterDTO.fromFilterDefinition(filter));
            }
            dto.setFilters(filterDTOs);
        }
        
        return dto;
    }
    
    /**
     * 验证路由配置
     */
    public void validate() {
        if (!StringUtils.hasText(this.id)) {
            throw new IllegalArgumentException("路由ID不能为空");
        }
        if (!StringUtils.hasText(this.uri)) {
            throw new IllegalArgumentException("路由URI不能为空");
        }
        if (this.predicates == null || this.predicates.isEmpty()) {
            throw new IllegalArgumentException("路由断言不能为空");
        }
    }
    
    /**
     * 断言配置DTO
     */
    @Data
    public static class PredicateDTO {
        /**
         * 断言名称，如：Path、Method、Header等
         */
        private String name;
        
        /**
         * 断言参数
         */
        private Map<String, String> args;
        
        /**
         * 转换为PredicateDefinition
         */
        public PredicateDefinition toPredicateDefinition() {
            PredicateDefinition definition = new PredicateDefinition();
            definition.setName(this.name);
            if (this.args != null) {
                definition.setArgs(this.args);
            }
            return definition;
        }
        
        /**
         * 从PredicateDefinition创建DTO
         */
        public static PredicateDTO fromPredicateDefinition(PredicateDefinition definition) {
            PredicateDTO dto = new PredicateDTO();
            dto.setName(definition.getName());
            dto.setArgs(definition.getArgs());
            return dto;
        }
    }
    
    /**
     * 过滤器配置DTO
     */
    @Data
    public static class FilterDTO {
        /**
         * 过滤器名称，如：StripPrefix、RequestRateLimiter等
         */
        private String name;
        
        /**
         * 过滤器参数
         */
        private Map<String, String> args;
        
        /**
         * 转换为FilterDefinition
         */
        public FilterDefinition toFilterDefinition() {
            FilterDefinition definition = new FilterDefinition();
            definition.setName(this.name);
            if (this.args != null) {
                definition.setArgs(this.args);
            }
            return definition;
        }
        
        /**
         * 从FilterDefinition创建DTO
         */
        public static FilterDTO fromFilterDefinition(FilterDefinition definition) {
            FilterDTO dto = new FilterDTO();
            dto.setName(definition.getName());
            dto.setArgs(definition.getArgs());
            return dto;
        }
    }
}