package com.hzwangda.edu.gateway.metrics;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 限流指标收集器
 * 收集限流相关的监控指标
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class RateLimitMetrics {
    
    private final MeterRegistry meterRegistry;
    private final ConcurrentHashMap<String, Counter> rateLimitCounters = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Counter> rateLimitRejectCounters = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Timer> circuitBreakerTimers = new ConcurrentHashMap<>();
    
    public RateLimitMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        initializeMetrics();
    }
    
    /**
     * 初始化基础指标
     */
    private void initializeMetrics() {
        // 总请求数
        meterRegistry.counter("gateway.ratelimit.requests.total", "type", "all");
        
        // 被限流请求数
        meterRegistry.counter("gateway.ratelimit.rejected.total", "type", "all");
        
        // 熔断器状态
        meterRegistry.gauge("gateway.circuitbreaker.status", 1.0);
    }
    
    /**
     * 记录限流请求
     */
    public void recordRateLimitRequest(String resource, String keyType, boolean allowed) {
        // 获取或创建计数器
        String key = resource + ":" + keyType;
        Counter requestCounter = rateLimitCounters.computeIfAbsent(key, k ->
                Counter.builder("gateway.ratelimit.requests")
                        .tag("resource", resource)
                        .tag("key_type", keyType)
                        .tag("allowed", String.valueOf(allowed))
                        .description("Rate limit requests")
                        .register(meterRegistry)
        );
        requestCounter.increment();
        
        // 如果被拒绝，记录拒绝计数
        if (!allowed) {
            Counter rejectCounter = rateLimitRejectCounters.computeIfAbsent(key, k ->
                    Counter.builder("gateway.ratelimit.rejected")
                            .tag("resource", resource)
                            .tag("key_type", keyType)
                            .description("Rate limit rejected requests")
                            .register(meterRegistry)
            );
            rejectCounter.increment();
            
            log.debug("Rate limit rejected for resource: {}, keyType: {}", resource, keyType);
        }
    }
    
    /**
     * 记录熔断器打开事件
     */
    public void recordCircuitBreakerOpen(String resource, String reason) {
        Counter.builder("gateway.circuitbreaker.opened")
                .tag("resource", resource)
                .tag("reason", reason)
                .description("Circuit breaker opened events")
                .register(meterRegistry)
                .increment();
        
        log.info("Circuit breaker opened for resource: {}, reason: {}", resource, reason);
    }
    
    /**
     * 记录熔断器关闭事件
     */
    public void recordCircuitBreakerClose(String resource) {
        Counter.builder("gateway.circuitbreaker.closed")
                .tag("resource", resource)
                .description("Circuit breaker closed events")
                .register(meterRegistry)
                .increment();
        
        log.info("Circuit breaker closed for resource: {}", resource);
    }
    
    /**
     * 记录熔断器半开状态
     */
    public void recordCircuitBreakerHalfOpen(String resource) {
        Counter.builder("gateway.circuitbreaker.halfopen")
                .tag("resource", resource)
                .description("Circuit breaker half-open events")
                .register(meterRegistry)
                .increment();
        
        log.info("Circuit breaker half-open for resource: {}", resource);
    }
    
    /**
     * 记录降级处理
     */
    public void recordFallback(String resource, String type, long duration) {
        Timer timer = circuitBreakerTimers.computeIfAbsent(resource, k ->
                Timer.builder("gateway.fallback.duration")
                        .tag("resource", resource)
                        .tag("type", type)
                        .description("Fallback execution duration")
                        .register(meterRegistry)
        );
        timer.record(duration, TimeUnit.MILLISECONDS);
        
        Counter.builder("gateway.fallback.executions")
                .tag("resource", resource)
                .tag("type", type)
                .description("Fallback executions")
                .register(meterRegistry)
                .increment();
    }
    
    /**
     * 记录限流配置变更
     */
    public void recordRateLimitConfigChange(String resource, int oldLimit, int newLimit) {
        Counter.builder("gateway.ratelimit.config.changes")
                .tag("resource", resource)
                .description("Rate limit configuration changes")
                .register(meterRegistry)
                .increment();
        
        log.info("Rate limit config changed for resource: {}, from {} to {}", 
                resource, oldLimit, newLimit);
    }
    
    /**
     * 获取当前限流统计
     */
    public RateLimitStats getRateLimitStats(String resource) {
        String key = resource + ":user";
        Counter requestCounter = rateLimitCounters.get(key);
        Counter rejectCounter = rateLimitRejectCounters.get(key);
        
        long totalRequests = requestCounter != null ? (long) requestCounter.count() : 0;
        long rejectedRequests = rejectCounter != null ? (long) rejectCounter.count() : 0;
        
        return new RateLimitStats(resource, totalRequests, rejectedRequests);
    }
    
    /**
     * 限流统计信息
     */
    public static class RateLimitStats {
        private final String resource;
        private final long totalRequests;
        private final long rejectedRequests;
        private final double rejectRate;
        
        public RateLimitStats(String resource, long totalRequests, long rejectedRequests) {
            this.resource = resource;
            this.totalRequests = totalRequests;
            this.rejectedRequests = rejectedRequests;
            this.rejectRate = totalRequests > 0 ? 
                    (double) rejectedRequests / totalRequests * 100 : 0;
        }
        
        public String getResource() {
            return resource;
        }
        
        public long getTotalRequests() {
            return totalRequests;
        }
        
        public long getRejectedRequests() {
            return rejectedRequests;
        }
        
        public double getRejectRate() {
            return rejectRate;
        }
    }
}