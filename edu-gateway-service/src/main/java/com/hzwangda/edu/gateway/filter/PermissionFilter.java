package com.hzwangda.edu.gateway.filter;

import com.hzwangda.edu.gateway.context.AuthContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 权限验证过滤器
 * 基于路径和角色进行权限控制
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PermissionFilter implements GlobalFilter, Ordered {
    
    /**
     * 路径权限映射
     * 实际项目中应该从配置中心或数据库加载
     */
    private static final Map<String, List<String>> PATH_PERMISSIONS = new HashMap<>();
    
    static {
        // 管理员接口
        PATH_PERMISSIONS.put("/api/*/admin/**", List.of("ROLE_ADMIN", "ROLE_SUPER_ADMIN"));
        
        // 用户管理接口
        PATH_PERMISSIONS.put("/api/auth/users/**", List.of("ROLE_USER_ADMIN", "ROLE_ADMIN"));
        
        // 审计日志接口
        PATH_PERMISSIONS.put("/api/audit/**", List.of("ROLE_AUDITOR", "ROLE_ADMIN"));
        
        // 工作流管理接口
        PATH_PERMISSIONS.put("/api/workflow/admin/**", List.of("ROLE_WORKFLOW_ADMIN", "ROLE_ADMIN"));
        
        // 文件管理接口
        PATH_PERMISSIONS.put("/api/file/admin/**", List.of("ROLE_FILE_ADMIN", "ROLE_ADMIN"));
        
        // 系统配置接口
        PATH_PERMISSIONS.put("/api/dictionary/admin/**", List.of("ROLE_CONFIG_ADMIN", "ROLE_ADMIN"));
        
        // 网关管理接口
        PATH_PERMISSIONS.put("/gateway/routes/**", List.of("ROLE_GATEWAY_ADMIN", "ROLE_ADMIN"));
    }
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();
        
        // 获取认证上下文
        AuthContext authContext = exchange.getAttribute("authContext");
        if (authContext == null || !authContext.isAuthenticated()) {
            // 如果没有认证上下文，说明可能是白名单路径，直接放行
            return chain.filter(exchange);
        }
        
        // 检查路径权限
        for (Map.Entry<String, List<String>> entry : PATH_PERMISSIONS.entrySet()) {
            if (pathMatches(path, entry.getKey())) {
                List<String> requiredRoles = entry.getValue();
                
                // 检查用户是否有任一所需角色
                if (!authContext.hasAnyRole(requiredRoles)) {
                    log.warn("User {} lacks required roles {} for path: {}", 
                            authContext.getUsername(), requiredRoles, path);
                    return forbidden(exchange.getResponse(), 
                            "Insufficient permissions to access this resource");
                }
                
                log.debug("Permission check passed for user {} on path: {}", 
                        authContext.getUsername(), path);
                break;
            }
        }
        
        // 权限验证通过，继续执行
        return chain.filter(exchange);
    }
    
    /**
     * 路径匹配
     * 支持通配符 * 和 **
     */
    private boolean pathMatches(String path, String pattern) {
        // 简单的通配符匹配实现
        String regex = pattern.replace("**", ".*")
                              .replace("*", "[^/]*");
        return path.matches(regex);
    }
    
    /**
     * 返回禁止访问响应
     */
    private Mono<Void> forbidden(ServerHttpResponse response, String message) {
        response.setStatusCode(HttpStatus.FORBIDDEN);
        response.getHeaders().add(HttpHeaders.CONTENT_TYPE, "application/json;charset=UTF-8");
        
        String body = String.format(
                "{\"code\":403,\"message\":\"%s\",\"timestamp\":%d}",
                message,
                System.currentTimeMillis()
        );
        
        return response.writeWith(
                Mono.just(response.bufferFactory().wrap(body.getBytes()))
        );
    }
    
    @Override
    public int getOrder() {
        // 在认证过滤器之后执行
        return -90;
    }
}