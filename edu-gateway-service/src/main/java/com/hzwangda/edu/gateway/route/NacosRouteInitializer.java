package com.hzwangda.edu.gateway.route;

import com.alibaba.cloud.nacos.NacosConfigManager;
import com.alibaba.nacos.api.config.listener.Listener;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.concurrent.Executor;

/**
 * Nacos路由初始化器
 * 从Nacos配置中心加载路由配置，并支持动态刷新
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class NacosRouteInitializer implements CommandLineRunner {

    private final NacosConfigManager nacosConfigManager;
    private final DynamicRouteService dynamicRouteService;
    private final ObjectMapper objectMapper;

    @Value("${spring.cloud.nacos.config.group:EDU_PLATFORM}")
    private String group;

    private static final String ROUTE_DATA_ID = "gateway-dynamic-routes.json";

    public NacosRouteInitializer(NacosConfigManager nacosConfigManager,
                                DynamicRouteService dynamicRouteService,
                                ObjectMapper objectMapper) {
        this.nacosConfigManager = nacosConfigManager;
        this.dynamicRouteService = dynamicRouteService;
        this.objectMapper = objectMapper;
    }

    @Override
    public void run(String... args) throws Exception {
        // 加载初始路由配置
        loadRoutes();
        
        // 添加配置监听器
        nacosConfigManager.getConfigService().addListener(
                ROUTE_DATA_ID,
                group,
                new Listener() {
                    @Override
                    public void receiveConfigInfo(String configInfo) {
                        log.info("Route configuration changed, reloading routes...");
                        if (StringUtils.hasText(configInfo)) {
                            updateRoutes(configInfo);
                        }
                    }

                    @Override
                    public Executor getExecutor() {
                        return null;
                    }
                }
        );
    }

    /**
     * 加载路由配置
     */
    private void loadRoutes() {
        try {
            String configInfo = nacosConfigManager.getConfigService()
                    .getConfig(ROUTE_DATA_ID, group, 5000);
            
            if (StringUtils.hasText(configInfo)) {
                updateRoutes(configInfo);
            } else {
                log.info("No dynamic routes found in Nacos");
            }
        } catch (Exception e) {
            log.error("Failed to load routes from Nacos", e);
        }
    }

    /**
     * 更新路由配置
     */
    private void updateRoutes(String configInfo) {
        try {
            List<RouteDefinition> routes = objectMapper.readValue(
                    configInfo,
                    new TypeReference<List<RouteDefinition>>() {}
            );
            
            // 清除现有路由并添加新路由
            dynamicRouteService.getRoutes()
                    .map(RouteDefinition::getId)
                    .flatMap(dynamicRouteService::delete)
                    .then(dynamicRouteService.addBatch(routes))
                    .subscribe(
                            null,
                            error -> log.error("Failed to update routes", error),
                            () -> log.info("Successfully updated {} routes", routes.size())
                    );
        } catch (Exception e) {
            log.error("Failed to parse route configuration", e);
        }
    }
}