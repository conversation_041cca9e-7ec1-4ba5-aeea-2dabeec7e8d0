package com.hzwangda.edu.gateway.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * 灰度发布配置类
 * 支持多种灰度策略配置
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
@Configuration
@ConfigurationProperties(prefix = "gray-release")
public class GrayReleaseConfig {
    
    /**
     * 是否启用灰度发布
     */
    private boolean enabled = false;
    
    /**
     * 灰度策略类型
     * - weight: 基于权重的灰度
     * - user: 基于用户的灰度
     * - header: 基于请求头的灰度
     * - ip: 基于IP的灰度
     * - percentage: 基于百分比的灰度
     */
    private String strategy = "weight";
    
    /**
     * 服务灰度配置
     * key: 服务名称
     * value: 灰度规则配置
     */
    private Map<String, GrayRule> rules = new HashMap<>();
    
    /**
     * 默认灰度版本标签
     */
    private String defaultVersion = "stable";
    
    /**
     * 灰度版本标签
     */
    private String grayVersion = "gray";
    
    /**
     * 灰度规则类
     */
    public static class GrayRule {
        /**
         * 是否启用该服务的灰度
         */
        private boolean enabled = true;
        
        /**
         * 灰度权重（0-100）
         */
        private int weight = 10;
        
        /**
         * 灰度用户白名单
         */
        private String[] userWhitelist = new String[0];
        
        /**
         * 灰度IP白名单
         */
        private String[] ipWhitelist = new String[0];
        
        /**
         * 灰度请求头匹配规则
         */
        private Map<String, String> headerRules = new HashMap<>();
        
        /**
         * 灰度版本号
         */
        private String version;
        
        /**
         * 灰度实例地址列表
         */
        private String[] grayInstances = new String[0];
        
        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }
        
        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
        
        public int getWeight() {
            return weight;
        }
        
        public void setWeight(int weight) {
            this.weight = weight;
        }
        
        public String[] getUserWhitelist() {
            return userWhitelist;
        }
        
        public void setUserWhitelist(String[] userWhitelist) {
            this.userWhitelist = userWhitelist;
        }
        
        public String[] getIpWhitelist() {
            return ipWhitelist;
        }
        
        public void setIpWhitelist(String[] ipWhitelist) {
            this.ipWhitelist = ipWhitelist;
        }
        
        public Map<String, String> getHeaderRules() {
            return headerRules;
        }
        
        public void setHeaderRules(Map<String, String> headerRules) {
            this.headerRules = headerRules;
        }
        
        public String getVersion() {
            return version;
        }
        
        public void setVersion(String version) {
            this.version = version;
        }
        
        public String[] getGrayInstances() {
            return grayInstances;
        }
        
        public void setGrayInstances(String[] grayInstances) {
            this.grayInstances = grayInstances;
        }
    }
    
    // Getters and Setters
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public String getStrategy() {
        return strategy;
    }
    
    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }
    
    public Map<String, GrayRule> getRules() {
        return rules;
    }
    
    public void setRules(Map<String, GrayRule> rules) {
        this.rules = rules;
    }
    
    public String getDefaultVersion() {
        return defaultVersion;
    }
    
    public void setDefaultVersion(String defaultVersion) {
        this.defaultVersion = defaultVersion;
    }
    
    public String getGrayVersion() {
        return grayVersion;
    }
    
    public void setGrayVersion(String grayVersion) {
        this.grayVersion = grayVersion;
    }
}