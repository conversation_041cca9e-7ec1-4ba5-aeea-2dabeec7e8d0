package com.hzwangda.edu.gateway.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * JWT工具类
 * 提供JWT令牌的生成、解析、验证功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class JwtUtil {
    
    @Value("${gateway.auth.jwt.secret:mySecretKeyForJWTSigning12345678}")
    private String secret;
    
    @Value("${gateway.auth.jwt.expiration:86400}")
    private long expiration;
    
    @Value("${gateway.auth.jwt.issuer:edu-platform}")
    private String issuer;
    
    /**
     * 获取签名密钥
     */
    private SecretKey getSigningKey() {
        byte[] keyBytes = secret.getBytes(StandardCharsets.UTF_8);
        return Keys.hmacShaKeyFor(keyBytes);
    }
    
    /**
     * 生成JWT令牌
     *
     * @param subject 主题（通常是用户ID）
     * @param claims 自定义声明
     * @return JWT令牌
     */
    public String generateToken(String subject, Map<String, Object> claims) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration * 1000);
        
        return Jwts.builder()
                .setSubject(subject)
                .setIssuer(issuer)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .addClaims(claims)
                .signWith(getSigningKey(), SignatureAlgorithm.HS256)
                .compact();
    }
    
    /**
     * 解析JWT令牌
     *
     * @param token JWT令牌
     * @return 声明信息
     */
    public Claims parseToken(String token) {
        try {
            return Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (Exception e) {
            log.error("Failed to parse JWT token: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 验证JWT令牌
     *
     * @param token JWT令牌
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            Claims claims = parseToken(token);
            if (claims == null) {
                return false;
            }
            
            // 检查是否过期
            Date expiration = claims.getExpiration();
            if (expiration != null && expiration.before(new Date())) {
                log.debug("Token has expired");
                return false;
            }
            
            // 检查发行者
            String tokenIssuer = claims.getIssuer();
            if (!issuer.equals(tokenIssuer)) {
                log.debug("Invalid token issuer: {}", tokenIssuer);
                return false;
            }
            
            return true;
        } catch (Exception e) {
            log.error("Token validation failed: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取用户ID
     *
     * @param token JWT令牌
     * @return 用户ID
     */
    public String getUserId(String token) {
        Claims claims = parseToken(token);
        return claims != null ? claims.getSubject() : null;
    }
    
    /**
     * 获取用户名
     *
     * @param token JWT令牌
     * @return 用户名
     */
    public String getUsername(String token) {
        Claims claims = parseToken(token);
        return claims != null ? claims.get("username", String.class) : null;
    }
    
    /**
     * 获取用户角色
     *
     * @param token JWT令牌
     * @return 角色列表
     */
    @SuppressWarnings("unchecked")
    public List<String> getRoles(String token) {
        Claims claims = parseToken(token);
        return claims != null ? claims.get("roles", List.class) : new ArrayList<>();
    }
    
    /**
     * 获取租户ID
     *
     * @param token JWT令牌
     * @return 租户ID
     */
    public String getTenantId(String token) {
        Claims claims = parseToken(token);
        return claims != null ? claims.get("tenantId", String.class) : null;
    }
    
    /**
     * 检查令牌是否即将过期
     *
     * @param token JWT令牌
     * @param threshold 阈值（秒）
     * @return 是否即将过期
     */
    public boolean isTokenExpiringSoon(String token, long threshold) {
        Claims claims = parseToken(token);
        if (claims == null) {
            return true;
        }
        
        Date expiration = claims.getExpiration();
        if (expiration == null) {
            return false;
        }
        
        long timeUntilExpiration = expiration.getTime() - System.currentTimeMillis();
        return timeUntilExpiration < threshold * 1000;
    }
    
    /**
     * 刷新令牌
     *
     * @param token 原令牌
     * @return 新令牌
     */
    public String refreshToken(String token) {
        Claims claims = parseToken(token);
        if (claims == null) {
            return null;
        }
        
        // 保留原有的claims，更新时间
        Map<String, Object> newClaims = new HashMap<>();
        claims.forEach((key, value) -> {
            if (!key.equals("sub") && !key.equals("iat") && !key.equals("exp") && !key.equals("iss")) {
                newClaims.put(key, value);
            }
        });
        
        return generateToken(claims.getSubject(), newClaims);
    }
}