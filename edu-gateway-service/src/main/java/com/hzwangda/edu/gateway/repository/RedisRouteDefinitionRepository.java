package com.hzwangda.edu.gateway.repository;

import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.cloud.gateway.route.RouteDefinitionRepository;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Redis路由定义存储库
 * 实现路由的持久化存储
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RedisRouteDefinitionRepository implements RouteDefinitionRepository {
    
    /**
     * Redis路由键前缀
     */
    private static final String ROUTE_KEY_PREFIX = "gateway:routes:";
    
    private final ReactiveRedisTemplate<String, String> redisTemplate;
    
    /**
     * 获取所有路由定义
     */
    @Override
    public Flux<RouteDefinition> getRouteDefinitions() {
        return redisTemplate.keys(ROUTE_KEY_PREFIX + "*")
                .flatMap(key -> redisTemplate.opsForValue().get(key))
                .map(value -> {
                    try {
                        return JSON.parseObject(value, RouteDefinition.class);
                    } catch (Exception e) {
                        log.error("Failed to parse route definition: {}", value, e);
                        return null;
                    }
                })
                .filter(route -> route != null)
                .doOnNext(route -> log.debug("Loaded route from Redis: {}", route.getId()));
    }
    
    /**
     * 保存路由定义
     */
    @Override
    public Mono<Void> save(Mono<RouteDefinition> route) {
        return route
                .flatMap(routeDefinition -> {
                    String key = ROUTE_KEY_PREFIX + routeDefinition.getId();
                    String value = JSON.toJSONString(routeDefinition);
                    
                    return redisTemplate.opsForValue()
                            .set(key, value)
                            .doOnSuccess(success -> {
                                if (Boolean.TRUE.equals(success)) {
                                    log.info("Saved route to Redis: {}", routeDefinition.getId());
                                } else {
                                    log.error("Failed to save route to Redis: {}", routeDefinition.getId());
                                }
                            });
                })
                .then();
    }
    
    /**
     * 删除路由定义
     */
    @Override
    public Mono<Void> delete(Mono<String> routeId) {
        return routeId
                .flatMap(id -> {
                    String key = ROUTE_KEY_PREFIX + id;
                    return redisTemplate.delete(key)
                            .doOnSuccess(count -> {
                                if (count > 0) {
                                    log.info("Deleted route from Redis: {}", id);
                                } else {
                                    log.warn("Route not found in Redis: {}", id);
                                }
                            });
                })
                .then();
    }
    
    /**
     * 检查路由是否存在
     */
    public Mono<Boolean> exists(String routeId) {
        String key = ROUTE_KEY_PREFIX + routeId;
        return redisTemplate.hasKey(key);
    }
    
    /**
     * 获取单个路由定义
     */
    public Mono<RouteDefinition> get(String routeId) {
        String key = ROUTE_KEY_PREFIX + routeId;
        return redisTemplate.opsForValue().get(key)
                .map(value -> {
                    try {
                        return JSON.parseObject(value, RouteDefinition.class);
                    } catch (Exception e) {
                        log.error("Failed to parse route definition for id: {}", routeId, e);
                        return null;
                    }
                })
                .filter(route -> route != null);
    }
    
    /**
     * 清空所有路由
     */
    public Mono<Void> deleteAll() {
        return redisTemplate.keys(ROUTE_KEY_PREFIX + "*")
                .flatMap(key -> redisTemplate.delete(key))
                .doOnComplete(() -> log.info("Deleted all routes from Redis"))
                .then();
    }
    
    /**
     * 批量保存路由
     */
    public Flux<Void> saveAll(Flux<RouteDefinition> routes) {
        return routes
                .flatMap(route -> save(Mono.just(route)));
    }
}