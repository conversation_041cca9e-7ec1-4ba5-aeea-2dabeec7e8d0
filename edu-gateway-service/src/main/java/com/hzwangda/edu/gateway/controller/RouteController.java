package com.hzwangda.edu.gateway.controller;

import com.hzwangda.edu.gateway.dto.RouteDefinitionDTO;
import com.hzwangda.edu.gateway.route.DynamicRouteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 路由管理控制器
 * 提供动态路由的CRUD操作接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/gateway/routes")
@RequiredArgsConstructor
public class RouteController {

    private final DynamicRouteService dynamicRouteService;

    /**
     * 获取所有路由
     */
    @GetMapping
    public Flux<RouteDefinition> getRoutes() {
        return dynamicRouteService.getRoutes();
    }

    /**
     * 添加路由
     */
    @PostMapping
    public Mono<ResponseEntity<String>> addRoute(@RequestBody RouteDefinitionDTO routeDTO) {
        try {
            RouteDefinition routeDefinition = routeDTO.toRouteDefinition();
            return dynamicRouteService.add(routeDefinition)
                    .then(Mono.just(ResponseEntity.ok("Route added successfully: " + routeDefinition.getId())));
        } catch (Exception e) {
            log.error("Failed to add route", e);
            return Mono.just(ResponseEntity.badRequest().body("Failed to add route: " + e.getMessage()));
        }
    }

    /**
     * 批量添加路由
     */
    @PostMapping("/batch")
    public Mono<ResponseEntity<String>> addRoutes(@RequestBody List<RouteDefinitionDTO> routeDTOs) {
        try {
            List<RouteDefinition> routeDefinitions = routeDTOs.stream()
                    .map(RouteDefinitionDTO::toRouteDefinition)
                    .toList();
            return dynamicRouteService.addBatch(routeDefinitions)
                    .then(Mono.just(ResponseEntity.ok("Added " + routeDefinitions.size() + " routes successfully")));
        } catch (Exception e) {
            log.error("Failed to add routes", e);
            return Mono.just(ResponseEntity.badRequest().body("Failed to add routes: " + e.getMessage()));
        }
    }

    /**
     * 更新路由
     */
    @PutMapping("/{id}")
    public Mono<ResponseEntity<String>> updateRoute(@PathVariable String id, 
                                                   @RequestBody RouteDefinitionDTO routeDTO) {
        try {
            RouteDefinition routeDefinition = routeDTO.toRouteDefinition();
            routeDefinition.setId(id);
            return dynamicRouteService.update(routeDefinition)
                    .then(Mono.just(ResponseEntity.ok("Route updated successfully: " + id)));
        } catch (Exception e) {
            log.error("Failed to update route", e);
            return Mono.just(ResponseEntity.badRequest().body("Failed to update route: " + e.getMessage()));
        }
    }

    /**
     * 删除路由
     */
    @DeleteMapping("/{id}")
    public Mono<ResponseEntity<String>> deleteRoute(@PathVariable String id) {
        return dynamicRouteService.delete(id)
                .then(Mono.just(ResponseEntity.ok("Route deleted successfully: " + id)))
                .onErrorResume(e -> {
                    log.error("Failed to delete route", e);
                    return Mono.just(ResponseEntity.badRequest().body("Failed to delete route: " + e.getMessage()));
                });
    }

    /**
     * 刷新路由缓存
     */
    @PostMapping("/refresh")
    public Mono<ResponseEntity<String>> refreshRoutes() {
        return dynamicRouteService.refreshRoutes()
                .then(Mono.just(ResponseEntity.ok("Routes refreshed successfully")));
    }
}