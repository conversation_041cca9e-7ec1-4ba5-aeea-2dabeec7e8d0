package com.hzwangda.edu.gateway.strategy.impl;

import com.hzwangda.edu.gateway.config.GrayReleaseConfig;
import com.hzwangda.edu.gateway.strategy.GrayReleaseStrategy;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;

import java.util.Map;

/**
 * 基于请求头的灰度策略
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
@Component("headerGrayStrategy")
public class HeaderGrayStrategy implements GrayReleaseStrategy {
    
    @Override
    public boolean shouldRouteToGray(ServerWebExchange exchange, String serviceName, GrayReleaseConfig.GrayRule grayRule) {
        if (grayRule == null || !grayRule.isEnabled()) {
            return false;
        }
        
        Map<String, String> headerRules = grayRule.getHeaderRules();
        if (headerRules == null || headerRules.isEmpty()) {
            return false;
        }
        
        HttpHeaders headers = exchange.getRequest().getHeaders();
        
        // 检查所有请求头规则是否匹配
        for (Map.Entry<String, String> rule : headerRules.entrySet()) {
            String headerName = rule.getKey();
            String expectedValue = rule.getValue();
            String actualValue = headers.getFirst(headerName);
            
            if (actualValue == null || !actualValue.equals(expectedValue)) {
                return false;
            }
        }
        
        return true;
    }
    
    @Override
    public String getStrategyName() {
        return "header";
    }
}