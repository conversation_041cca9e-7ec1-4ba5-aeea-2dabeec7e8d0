package com.hzwangda.edu.gateway.strategy.impl;

import com.hzwangda.edu.gateway.config.GrayReleaseConfig;
import com.hzwangda.edu.gateway.strategy.GrayReleaseStrategy;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;

import java.util.concurrent.ThreadLocalRandom;

/**
 * 基于权重的灰度策略
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
@Component("weightGrayStrategy")
public class WeightGrayStrategy implements GrayReleaseStrategy {
    
    @Override
    public boolean shouldRouteToGray(ServerWebExchange exchange, String serviceName, GrayReleaseConfig.GrayRule grayRule) {
        if (grayRule == null || !grayRule.isEnabled()) {
            return false;
        }
        
        int weight = grayRule.getWeight();
        if (weight <= 0) {
            return false;
        }
        if (weight >= 100) {
            return true;
        }
        
        // 生成随机数判断是否进入灰度
        int random = ThreadLocalRandom.current().nextInt(100);
        return random < weight;
    }
    
    @Override
    public String getStrategyName() {
        return "weight";
    }
}