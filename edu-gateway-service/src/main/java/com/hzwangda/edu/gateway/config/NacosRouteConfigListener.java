package com.hzwangda.edu.gateway.config;

import com.alibaba.cloud.nacos.NacosConfigManager;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.nacos.api.config.listener.Listener;
import com.hzwangda.edu.gateway.dto.RouteDefinitionDTO;
import com.hzwangda.edu.gateway.route.DynamicRouteService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * Nacos路由配置监听器
 * 监听Nacos配置中心的路由配置变更，实现动态路由更新
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class NacosRouteConfigListener {
    
    private final NacosConfigManager nacosConfigManager;
    private final DynamicRouteService dynamicRouteService;
    
    @Value("${gateway.route.config.data-id:gateway-routes}")
    private String routeDataId;
    
    @Value("${gateway.route.config.group:DEFAULT_GROUP}")
    private String routeGroup;
    
    @Value("${gateway.route.config.enabled:true}")
    private boolean enabled;
    
    /**
     * 初始化监听器
     */
    @PostConstruct
    public void init() {
        if (!enabled) {
            log.info("Nacos route config listener is disabled");
            return;
        }
        
        try {
            // 加载初始配置
            loadRoutes();
            
            // 添加配置监听器
            nacosConfigManager.getConfigService().addListener(
                    routeDataId, 
                    routeGroup, 
                    new RouteConfigListener()
            );
            
            log.info("Nacos route config listener initialized, dataId: {}, group: {}", 
                    routeDataId, routeGroup);
        } catch (Exception e) {
            log.error("Failed to initialize Nacos route config listener", e);
        }
    }
    
    /**
     * 加载路由配置
     */
    private void loadRoutes() {
        try {
            String config = nacosConfigManager.getConfigService()
                    .getConfig(routeDataId, routeGroup, 5000);
            
            if (config != null && !config.isEmpty()) {
                updateRoutes(config);
            }
        } catch (Exception e) {
            log.error("Failed to load routes from Nacos", e);
        }
    }
    
    /**
     * 更新路由
     */
    private void updateRoutes(String config) {
        try {
            log.info("Updating routes from Nacos config");
            
            // 解析路由配置
            List<RouteDefinitionDTO> routeDTOs = parseRouteConfig(config);
            
            // 转换为RouteDefinition
            List<RouteDefinition> routes = routeDTOs.stream()
                    .map(dto -> {
                        dto.validate();
                        return dto.toRouteDefinition();
                    })
                    .collect(Collectors.toList());
            
            // 批量添加路由
            dynamicRouteService.addBatch(routes)
                    .subscribe(
                            null,
                            error -> log.error("Failed to update routes", error),
                            () -> log.info("Successfully updated {} routes from Nacos", routes.size())
                    );
            
        } catch (Exception e) {
            log.error("Failed to update routes from config: {}", config, e);
        }
    }
    
    /**
     * 解析路由配置
     */
    private List<RouteDefinitionDTO> parseRouteConfig(String config) {
        try {
            // 支持JSON数组格式
            JSONArray jsonArray = JSON.parseArray(config);
            return jsonArray.toJavaList(RouteDefinitionDTO.class);
        } catch (Exception e) {
            log.error("Failed to parse route config as JSON array, trying single object", e);
            
            // 尝试解析为单个对象
            try {
                RouteDefinitionDTO route = JSON.parseObject(config, RouteDefinitionDTO.class);
                return List.of(route);
            } catch (Exception ex) {
                log.error("Failed to parse route config", ex);
                return List.of();
            }
        }
    }
    
    /**
     * 路由配置监听器内部类
     */
    private class RouteConfigListener implements Listener {
        
        @Override
        public Executor getExecutor() {
            return null; // 使用默认执行器
        }
        
        @Override
        public void receiveConfigInfo(String configInfo) {
            log.info("Received route config update from Nacos");
            
            if (configInfo == null || configInfo.isEmpty()) {
                log.warn("Received empty route config");
                return;
            }
            
            updateRoutes(configInfo);
        }
    }
}