package com.hzwangda.edu.gateway.filter;

import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 响应头过滤器
 * 为所有响应添加通用的响应头
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class ResponseHeaderFilter implements GlobalFilter, Ordered {

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        return chain.filter(exchange).then(Mono.fromRunnable(() -> {
            HttpHeaders headers = exchange.getResponse().getHeaders();
            
            // 添加安全相关响应头
            headers.add("X-Content-Type-Options", "nosniff");
            headers.add("X-Frame-Options", "DENY");
            headers.add("X-XSS-Protection", "1; mode=block");
            headers.add("Referrer-Policy", "no-referrer-when-downgrade");
            
            // 添加请求追踪ID
            String requestId = exchange.getRequest().getHeaders().getFirst("X-Request-Id");
            if (requestId != null) {
                headers.add("X-Request-Id", requestId);
            }
            
            // 添加服务器信息
            headers.add("X-Powered-By", "EDU-Gateway/1.0");
        }));
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}