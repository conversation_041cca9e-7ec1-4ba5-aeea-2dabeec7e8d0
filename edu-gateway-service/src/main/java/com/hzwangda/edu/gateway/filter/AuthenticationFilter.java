package com.hzwangda.edu.gateway.filter;

import com.hzwangda.edu.gateway.context.AuthContext;
import com.hzwangda.edu.gateway.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.List;

import com.hzwangda.edu.gateway.service.AuthCacheService;

/**
 * 统一认证过滤器
 * 负责JWT令牌验证和用户认证信息传递
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AuthenticationFilter implements GlobalFilter, Ordered {
    
    private final JwtUtil jwtUtil;
    private final AuthCacheService authCacheService;
    private final AntPathMatcher pathMatcher = new AntPathMatcher();
    
    @Value("${gateway.auth.enabled:true}")
    private boolean authEnabled;
    
    @Value("${gateway.auth.excludePaths}")
    private List<String> excludePaths;
    
    @Value("${gateway.auth.header:Authorization}")
    private String authHeader;
    
    @Value("${gateway.auth.prefix:Bearer }")
    private String tokenPrefix;
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        
        // 如果认证未启用，直接放行
        if (!authEnabled) {
            log.debug("Authentication is disabled, skipping auth filter");
            return chain.filter(exchange);
        }
        
        // 检查是否是白名单路径
        String path = request.getURI().getPath();
        if (isExcludedPath(path)) {
            log.debug("Path {} is excluded from authentication", path);
            return chain.filter(exchange);
        }
        
        // 获取令牌
        String token = extractToken(request);
        if (!StringUtils.hasText(token)) {
            log.warn("No token found in request headers for path: {}", path);
            return unauthorized(exchange.getResponse(), "Missing authentication token");
        }
        
        // 检查黑名单
        return authCacheService.isBlacklisted(token)
                .flatMap(blacklisted -> {
                    if (blacklisted) {
                        log.warn("Blacklisted token for path: {}", path);
                        return unauthorized(exchange.getResponse(), "Token has been revoked");
                    }
                    
                    // 验证令牌
                    if (!jwtUtil.validateToken(token)) {
                        log.warn("Invalid token for path: {}", path);
                        return unauthorized(exchange.getResponse(), "Invalid or expired token");
                    }
                    
                    return continueWithAuth(exchange, chain, token, request, path);
                });
    }
    
    /**
     * 提取令牌
     */
    private String extractToken(ServerHttpRequest request) {
        String bearerToken = request.getHeaders().getFirst(authHeader);
        
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith(tokenPrefix)) {
            return bearerToken.substring(tokenPrefix.length());
        }
        
        // 尝试从查询参数获取（某些场景如WebSocket）
        String queryToken = request.getQueryParams().getFirst("token");
        if (StringUtils.hasText(queryToken)) {
            return queryToken;
        }
        
        return null;
    }
    
    /**
     * 检查是否是排除路径
     */
    private boolean isExcludedPath(String path) {
        if (excludePaths == null || excludePaths.isEmpty()) {
            return false;
        }
        
        return excludePaths.stream()
                .anyMatch(pattern -> pathMatcher.match(pattern, path));
    }
    
    /**
     * 构建认证上下文
     */
    private AuthContext buildAuthContext(String token, ServerHttpRequest request) {
        String userId = jwtUtil.getUserId(token);
        String username = jwtUtil.getUsername(token);
        String tenantId = jwtUtil.getTenantId(token);
        List<String> roles = jwtUtil.getRoles(token);
        
        // 获取客户端IP
        String clientIp = getClientIp(request);
        
        // 生成请求ID
        String requestId = request.getId();
        
        return AuthContext.builder()
                .userId(userId)
                .username(username)
                .tenantId(tenantId)
                .roles(roles)
                .token(token)
                .clientIp(clientIp)
                .requestId(requestId)
                .authenticated(true)
                .authTime(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 获取客户端IP
     */
    private String getClientIp(ServerHttpRequest request) {
        // 尝试从各种header中获取真实IP
        String[] headers = {
                "X-Forwarded-For",
                "X-Real-IP",
                "Proxy-Client-IP",
                "WL-Proxy-Client-IP"
        };
        
        for (String header : headers) {
            String ip = request.getHeaders().getFirst(header);
            if (StringUtils.hasText(ip) && !"unknown".equalsIgnoreCase(ip)) {
                // 如果有多个IP，取第一个
                int index = ip.indexOf(',');
                if (index > 0) {
                    return ip.substring(0, index);
                }
                return ip;
            }
        }
        
        // 如果都没有，返回远程地址
        if (request.getRemoteAddress() != null) {
            return request.getRemoteAddress().getAddress().getHostAddress();
        }
        
        return "unknown";
    }
    
    /**
     * 返回未授权响应
     */
    private Mono<Void> unauthorized(ServerHttpResponse response, String message) {
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add(HttpHeaders.CONTENT_TYPE, "application/json;charset=UTF-8");
        
        String body = String.format(
                "{\"code\":401,\"message\":\"%s\",\"timestamp\":%d}",
                message,
                System.currentTimeMillis()
        );
        
        return response.writeWith(
                Mono.just(response.bufferFactory().wrap(body.getBytes()))
        );
    }
    
    /**
     * 继续处理认证请求
     */
    private Mono<Void> continueWithAuth(ServerWebExchange exchange, GatewayFilterChain chain,
                                       String token, ServerHttpRequest request, String path) {
        // 构建认证上下文
        AuthContext authContext = buildAuthContext(token, request);
        
        // 将认证信息添加到请求头，传递给下游服务
        ServerHttpRequest modifiedRequest = request.mutate()
                .header("X-User-Id", authContext.getUserId())
                .header("X-Username", authContext.getUsername())
                .header("X-Tenant-Id", authContext.getTenantId() != null ? authContext.getTenantId() : "")
                .header("X-Request-Id", authContext.getRequestId())
                .header("X-Auth-Token", token)
                .build();
        
        // 添加角色信息
        if (authContext.getRoles() != null && !authContext.getRoles().isEmpty()) {
            modifiedRequest = modifiedRequest.mutate()
                    .header("X-User-Roles", String.join(",", authContext.getRoles()))
                    .build();
        }
        
        log.debug("Authentication successful for user: {} on path: {}", 
                authContext.getUsername(), path);
        
        // 存储认证上下文到Exchange属性中
        exchange.getAttributes().put("authContext", authContext);
        
        // 缓存认证结果
        return authCacheService.cacheAuth(token, authContext)
                .then(chain.filter(exchange.mutate().request(modifiedRequest).build()));
    }
    
    @Override
    public int getOrder() {
        // 设置较高优先级，在日志过滤器之后执行
        return -100;
    }
}