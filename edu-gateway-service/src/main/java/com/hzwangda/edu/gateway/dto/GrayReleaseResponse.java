package com.hzwangda.edu.gateway.dto;

import com.hzwangda.edu.gateway.config.GrayReleaseConfig;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 灰度发布响应DTO
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
public class GrayReleaseResponse {
    
    /**
     * 操作是否成功
     */
    private boolean success = true;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 灰度发布是否启用
     */
    private boolean enabled;
    
    /**
     * 当前灰度策略
     */
    private String strategy;
    
    /**
     * 灰度规则配置
     */
    private Map<String, GrayReleaseConfig.GrayRule> rules;
    
    /**
     * 响应时间
     */
    private LocalDateTime timestamp = LocalDateTime.now();
    
    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public String getStrategy() {
        return strategy;
    }
    
    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }
    
    public Map<String, GrayReleaseConfig.GrayRule> getRules() {
        return rules;
    }
    
    public void setRules(Map<String, GrayReleaseConfig.GrayRule> rules) {
        this.rules = rules;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
}