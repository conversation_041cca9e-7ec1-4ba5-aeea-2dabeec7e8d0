package com.hzwangda.edu.gateway.dto;

import com.hzwangda.edu.gateway.config.GrayReleaseConfig;

/**
 * 灰度发布请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
public class GrayReleaseRequest {
    
    /**
     * 灰度规则
     */
    private GrayReleaseConfig.GrayRule grayRule;
    
    /**
     * 操作类型
     * - enable: 启用
     * - disable: 禁用
     * - update: 更新
     * - rollback: 回滚
     */
    private String operation;
    
    /**
     * 操作描述
     */
    private String description;
    
    /**
     * 操作人
     */
    private String operator;
    
    // Getters and Setters
    public GrayReleaseConfig.GrayRule getGrayRule() {
        return grayRule;
    }
    
    public void setGrayRule(GrayReleaseConfig.GrayRule grayRule) {
        this.grayRule = grayRule;
    }
    
    public String getOperation() {
        return operation;
    }
    
    public void setOperation(String operation) {
        this.operation = operation;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getOperator() {
        return operator;
    }
    
    public void setOperator(String operator) {
        this.operator = operator;
    }
}