package com.hzwangda.edu.gateway.config;

import com.hzwangda.edu.gateway.context.AuthContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.cloud.gateway.filter.ratelimit.RedisRateLimiter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import reactor.core.publisher.Mono;

/**
 * 限流配置
 * 配置多种限流策略和键解析器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
public class RateLimiterConfig {
    
    /**
     * 用户限流键解析器（默认）
     * 基于用户ID进行限流
     */
    @Bean
    @Primary
    public KeyResolver userKeyResolver() {
        return exchange -> {
            // 从认证上下文获取用户ID
            AuthContext authContext = exchange.getAttribute("authContext");
            if (authContext != null && authContext.getUserId() != null) {
                log.debug("Rate limiting by user: {}", authContext.getUserId());
                return Mono.just(authContext.getUserId());
            }
            
            // 如果没有认证信息，使用IP地址
            return ipKeyResolver().resolve(exchange);
        };
    }
    
    /**
     * IP限流键解析器
     * 基于客户端IP地址进行限流
     */
    @Bean
    public KeyResolver ipKeyResolver() {
        return exchange -> {
            String ip = getClientIp(exchange);
            log.debug("Rate limiting by IP: {}", ip);
            return Mono.just(ip);
        };
    }
    
    /**
     * API限流键解析器
     * 基于请求路径进行限流
     */
    @Bean
    public KeyResolver apiKeyResolver() {
        return exchange -> {
            String path = exchange.getRequest().getPath().value();
            log.debug("Rate limiting by API: {}", path);
            return Mono.just(path);
        };
    }
    
    /**
     * 租户限流键解析器
     * 基于租户ID进行限流
     */
    @Bean
    public KeyResolver tenantKeyResolver() {
        return exchange -> {
            AuthContext authContext = exchange.getAttribute("authContext");
            if (authContext != null && authContext.getTenantId() != null) {
                log.debug("Rate limiting by tenant: {}", authContext.getTenantId());
                return Mono.just(authContext.getTenantId());
            }
            
            // 默认租户
            return Mono.just("default");
        };
    }
    
    /**
     * 组合限流键解析器
     * 结合用户ID和API路径进行限流
     */
    @Bean
    public KeyResolver combinedKeyResolver() {
        return exchange -> {
            AuthContext authContext = exchange.getAttribute("authContext");
            String path = exchange.getRequest().getPath().value();
            
            String key;
            if (authContext != null && authContext.getUserId() != null) {
                key = authContext.getUserId() + ":" + path;
            } else {
                String ip = getClientIp(exchange);
                key = ip + ":" + path;
            }
            
            log.debug("Rate limiting by combined key: {}", key);
            return Mono.just(key);
        };
    }
    
    /**
     * 默认Redis限流器
     * 令牌桶算法
     */
    @Bean
    @Primary
    public RedisRateLimiter defaultRedisRateLimiter() {
        return new RedisRateLimiter(100, 200, 1);
    }
    
    /**
     * 严格限流器
     * 用于敏感接口
     */
    @Bean
    public RedisRateLimiter strictRedisRateLimiter() {
        return new RedisRateLimiter(10, 20, 1);
    }
    
    /**
     * 宽松限流器
     * 用于查询接口
     */
    @Bean
    public RedisRateLimiter relaxedRedisRateLimiter() {
        return new RedisRateLimiter(1000, 2000, 1);
    }
    
    /**
     * 获取客户端IP
     */
    private String getClientIp(org.springframework.web.server.ServerWebExchange exchange) {
        var request = exchange.getRequest();
        
        // 尝试从各种header中获取真实IP
        String[] headers = {
                "X-Forwarded-For",
                "X-Real-IP",
                "Proxy-Client-IP",
                "WL-Proxy-Client-IP"
        };
        
        for (String header : headers) {
            String ip = request.getHeaders().getFirst(header);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                // 如果有多个IP，取第一个
                int index = ip.indexOf(',');
                if (index > 0) {
                    return ip.substring(0, index).trim();
                }
                return ip;
            }
        }
        
        // 如果都没有，返回远程地址
        if (request.getRemoteAddress() != null) {
            return request.getRemoteAddress().getAddress().getHostAddress();
        }
        
        return "unknown";
    }
}