package com.hzwangda.edu.gateway.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.UUID;

/**
 * 请求日志记录过滤器
 * 记录所有经过网关的请求和响应信息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class RequestLogFilter implements GlobalFilter, Ordered {

    @Value("${gateway.logging.enabled:true}")
    private boolean loggingEnabled;

    @Value("${gateway.logging.log-headers:true}")
    private boolean logHeaders;

    @Value("${gateway.logging.log-body:false}")
    private boolean logBody;

    private static final String REQUEST_ID = "X-Request-Id";
    private static final String REQUEST_TIME = "X-Request-Time";

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        if (!loggingEnabled) {
            return chain.filter(exchange);
        }

        ServerHttpRequest request = exchange.getRequest();
        
        // 生成请求ID
        String requestId = UUID.randomUUID().toString();
        long startTime = System.currentTimeMillis();
        
        // 添加请求ID到header
        ServerHttpRequest mutatedRequest = request.mutate()
                .header(REQUEST_ID, requestId)
                .header(REQUEST_TIME, String.valueOf(startTime))
                .build();
        
        ServerWebExchange mutatedExchange = exchange.mutate()
                .request(mutatedRequest)
                .build();
        
        // 记录请求信息
        logRequest(mutatedRequest, requestId);
        
        return chain.filter(mutatedExchange)
                .then(Mono.fromRunnable(() -> {
                    // 记录响应信息
                    ServerHttpResponse response = exchange.getResponse();
                    long duration = System.currentTimeMillis() - startTime;
                    logResponse(mutatedRequest, response, requestId, duration);
                }));
    }

    /**
     * 记录请求信息
     */
    private void logRequest(ServerHttpRequest request, String requestId) {
        StringBuilder logBuilder = new StringBuilder("\n========== Gateway Request ==========\n");
        logBuilder.append("Request ID: ").append(requestId).append("\n");
        logBuilder.append("Method: ").append(request.getMethod()).append("\n");
        logBuilder.append("URI: ").append(request.getURI()).append("\n");
        logBuilder.append("Path: ").append(request.getPath()).append("\n");
        
        // 记录查询参数
        MultiValueMap<String, String> queryParams = request.getQueryParams();
        if (!queryParams.isEmpty()) {
            logBuilder.append("Query Params: ").append(queryParams).append("\n");
        }
        
        // 记录请求头
        if (logHeaders) {
            HttpHeaders headers = request.getHeaders();
            logBuilder.append("Headers: \n");
            headers.forEach((name, values) -> {
                // 过滤敏感信息
                if (isSensitiveHeader(name)) {
                    logBuilder.append("  ").append(name).append(": ").append("[MASKED]").append("\n");
                } else {
                    logBuilder.append("  ").append(name).append(": ").append(values).append("\n");
                }
            });
        }
        
        logBuilder.append("=====================================");
        log.info(logBuilder.toString());
    }

    /**
     * 记录响应信息
     */
    private void logResponse(ServerHttpRequest request, ServerHttpResponse response, 
                           String requestId, long duration) {
        StringBuilder logBuilder = new StringBuilder("\n========== Gateway Response ==========\n");
        logBuilder.append("Request ID: ").append(requestId).append("\n");
        logBuilder.append("Status: ").append(response.getStatusCode()).append("\n");
        logBuilder.append("Duration: ").append(duration).append("ms\n");
        logBuilder.append("Path: ").append(request.getPath()).append("\n");
        
        if (logHeaders) {
            HttpHeaders headers = response.getHeaders();
            logBuilder.append("Response Headers: \n");
            headers.forEach((name, values) -> {
                logBuilder.append("  ").append(name).append(": ").append(values).append("\n");
            });
        }
        
        logBuilder.append("======================================");
        log.info(logBuilder.toString());
    }

    /**
     * 判断是否为敏感请求头
     */
    private boolean isSensitiveHeader(String headerName) {
        return headerName.toLowerCase().contains("authorization") ||
               headerName.toLowerCase().contains("cookie") ||
               headerName.toLowerCase().contains("token");
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }
}