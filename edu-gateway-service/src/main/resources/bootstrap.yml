spring:
  application:
    name: edu-gateway
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  cloud:
    nacos:
      config:
        enabled: true
        server-addr: ${NACOS_SERVER:192.168.1.99:80}
        namespace: ${NACOS_NAMESPACE:public}
        group: ${NACOS_GROUP:EDU_PLATFORM}
        file-extension: yml
        prefix: ${spring.application.name}
        refresh-enabled: true
        # 共享配置
        shared-configs:
          - data-id: common-config.yml
            group: ${NACOS_GROUP:EDU_PLATFORM}
            refresh: true
          - data-id: gateway-routes.yml
            group: ${NACOS_GROUP:EDU_PLATFORM}
            refresh: true
        # 扩展配置
        extension-configs:
          - data-id: ${spring.application.name}-ext.yml
            group: ${NACOS_GROUP:EDU_PLATFORM}
            refresh: true
      discovery:
        server-addr: ${NACOS_SERVER:192.168.1.99:80}
        namespace: ${NACOS_NAMESPACE:public}
        group: ${NACOS_GROUP:EDU_PLATFORM}
        metadata:
          version: ${project.version:1.0.0}
          region: ${REGION:default}