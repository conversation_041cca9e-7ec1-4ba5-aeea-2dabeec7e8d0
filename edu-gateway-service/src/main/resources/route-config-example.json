[{"id": "auth-service-route", "uri": "lb://edu-auth-service", "order": 1, "predicates": [{"name": "Path", "args": {"pattern": "/api/auth/**"}}], "filters": [{"name": "StripPrefix", "args": {"parts": "2"}}, {"name": "RequestRateLimiter", "args": {"redis-rate-limiter.replenishRate": "100", "redis-rate-limiter.burstCapacity": "200", "redis-rate-limiter.requestedTokens": "1"}}], "metadata": {"description": "认证服务路由", "version": "1.0"}}, {"id": "file-service-route", "uri": "lb://edu-file-service", "order": 2, "predicates": [{"name": "Path", "args": {"pattern": "/api/file/**"}}, {"name": "Method", "args": {"methods": "GET,POST,PUT,DELETE"}}], "filters": [{"name": "StripPrefix", "args": {"parts": "2"}}, {"name": "RequestSize", "args": {"maxSize": "10MB"}}], "metadata": {"description": "文件服务路由", "version": "1.0"}}, {"id": "workflow-service-route", "uri": "lb://edu-workflow-service", "order": 3, "predicates": [{"name": "Path", "args": {"pattern": "/api/workflow/**"}}], "filters": [{"name": "StripPrefix", "args": {"parts": "2"}}, {"name": "CircuitBreaker", "args": {"name": "workflowServiceCircuitBreaker", "fallbackUri": "forward:/fallback/workflow"}}], "metadata": {"description": "工作流服务路由", "version": "1.0"}}]