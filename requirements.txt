1、edu-common-services目录是一个通用支撑服务的最外层管理目录，里面包含多个服务，例如edu-auth-service文件夹下的Auth Service (认证授权服务)等。
2、帮我edu-auth-service文件夹下，用jdk17为开发语言，基础框架用springboot3.4.7，数据库用postgress，持久层用jpa，搭建个微服务架构的认证授权服务，做好项目结构合理分层，包含采用CAS协议跟外部认证中心对接进行认证集成，用spring security进行系统自身的权限框架。

# edu-file-service
3.	配置 MinIO Client:
      在 application.yml 中配置 MinIO 连接信息:
      YAML
      minio:
      endpoint: http://edu-minio.edu-infra.svc.cluster.local:9000
      accessKey: <MinIO_Access_Key> # 通常是rootUser
      secretKey: <MinIO_Secret_Key> # 通常是rootPassword
      bucketName: edu-system-files
      ○
      ○	创建 MinIOClient 的 Spring Bean。
4.	实现文件上传接口:
      ○	创建一个 @RestController，包含 POST /files/upload 接口。
      ○	接口接收 MultipartFile。
      ○	使用 MinioClient.putObject() 方法将文件上传到 MinIO。
      ○	构造 MinIO Object Name (例如：module_code/biz_entity_id/UUID.ext)。
      ○	在数据库中模拟存储文件元数据（文件名、文件大小、MinIO Object Name、上传时间等）。
      ○	（可选）实现预签名上传 PoC:
      ■	实现 GET /files/upload/presigned-url 接口，调用 MinioClient.getPresignedObjectUrl() 方法生成上传 URL。
      ■	实现 POST /files/upload/confirm 接口，接收前端上传完成通知。
5.	实现文件下载接口:
      ○	创建一个 GET /files/download/{fileId} 接口。
      ○	根据 fileId 从模拟数据库中获取 MinIO Object Name。
      ○	使用 MinioClient.getObject() 方法从 MinIO 下载文件流，并写入到 HttpServletResponse。
      ○	（可选）实现预签名下载 PoC:
      ■	实现 GET /files/download/presigned-url/{fileId} 接口，调用 MinioClient.getPresignedObjectUrl() 方法生成下载 URL。
6.	实现大文件分片上传 PoC (难点/可选):
      ○	创建一个 POST /files/upload/multipart/init 接口，调用 MinioClient.createMultipartUpload() 初始化分片上传。
      ○	创建一个 PUT /files/upload/multipart/part 接口，接收每个分片，调用 MinioClient.uploadPart()。
      ○	创建一个 POST /files/upload/multipart/complete 接口，调用 MinioClient.completeMultipartUpload() 合并分片。
      ○	前端需要配合进行分片和断点续传逻辑模拟。
7.	运行并验证:
      ○	启动 Spring Boot 应用。
      ○	通过 Postman 或模拟前端进行文件上传和下载测试。
      ○	通过 MinIO Console 验证文件是否正确存储、命名。
      ○	测试大文件上传和下载的性能。
