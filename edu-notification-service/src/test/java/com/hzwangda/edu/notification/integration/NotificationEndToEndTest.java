package com.hzwangda.edu.notification.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hzwangda.edu.notification.dto.NotificationSendRequest;
import com.hzwangda.edu.notification.enums.ChannelType;
import com.hzwangda.edu.notification.enums.MessageStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 通知服务端到端集成测试
 * 测试多渠道通知发送、模板管理、统计分析等完整功能
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("通知服务端到端集成测试")
class NotificationEndToEndTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    // 测试数据
    private static String sentMessageId;
    private static String templateId;

    @Test
    @Order(1)
    @DisplayName("1. 多渠道通知发送测试")
    void testMultiChannelNotificationSending() throws Exception {
        // 1. 发送邮件通知
        NotificationSendRequest emailRequest = createEmailNotificationRequest();

        MvcResult emailResult = mockMvc.perform(post("/api/v1/notifications/send")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(emailRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.messageId").isString())
                .andExpect(jsonPath("$.data.status").value(MessageStatus.SUCCESS.getCode()))
                .andReturn();

        String emailMessageId = objectMapper.readTree(emailResult.getResponse().getContentAsString())
                .get("data").get("messageId").asText();
        assertNotNull(emailMessageId);

        // 2. 发送短信通知
        NotificationSendRequest smsRequest = createSmsNotificationRequest();

        mockMvc.perform(post("/api/v1/notifications/send")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(smsRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.messageId").isString());

        // 3. 发送站内信通知
        NotificationSendRequest internalRequest = createInternalNotificationRequest();

        MvcResult internalResult = mockMvc.perform(post("/api/v1/notifications/send")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(internalRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.messageId").isString())
                .andReturn();

        sentMessageId = objectMapper.readTree(internalResult.getResponse().getContentAsString())
                .get("data").get("messageId").asText();

        // 4. 发送微信通知
        NotificationSendRequest wechatRequest = createWeChatNotificationRequest();

        mockMvc.perform(post("/api/v1/notifications/send")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(wechatRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 5. 发送钉钉通知
        NotificationSendRequest dingtalkRequest = createDingTalkNotificationRequest();

        mockMvc.perform(post("/api/v1/notifications/send")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dingtalkRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @Order(2)
    @DisplayName("2. 异步和批量发送测试")
    void testAsyncAndBatchSending() throws Exception {
        // 1. 异步发送测试
        NotificationSendRequest asyncRequest = createEmailNotificationRequest();
        asyncRequest.setAsync(true);

        mockMvc.perform(post("/api/v1/notifications/send/async")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(asyncRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isString()); // 返回消息ID

        // 2. 批量发送测试
        NotificationSendRequest batchRequest = createBatchNotificationRequest();

        mockMvc.perform(post("/api/v1/notifications/send/batch")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(batchRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());

        // 3. 定时发送测试
        NotificationSendRequest scheduledRequest = createEmailNotificationRequest();
        LocalDateTime scheduledTime = LocalDateTime.now().plusMinutes(5);

        mockMvc.perform(post("/api/v1/notifications/send/scheduled")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(scheduledRequest))
                        .param("scheduledTime", scheduledTime.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @Order(3)
    @DisplayName("3. 消息查询和管理测试")
    void testMessageQueryAndManagement() throws Exception {
        assertNotNull(sentMessageId, "需要先执行消息发送测试");

        // 1. 根据ID查询消息
        mockMvc.perform(get("/api/v1/notifications/{messageId}", sentMessageId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.messageId").value(sentMessageId));

        // 2. 分页查询消息
        mockMvc.perform(post("/api/v1/notifications/query")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("""
                                {
                                    "page": 1,
                                    "size": 10,
                                    "channelType": "INTERNAL",
                                    "status": "SUCCESS"
                                }
                                """))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.records").isArray())
                .andExpect(jsonPath("$.data.total").isNumber());

        // 3. 查询用户消息
        mockMvc.perform(get("/api/v1/notifications/user/test-user")
                        .param("page", "1")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());

        // 4. 标记消息为已读
        mockMvc.perform(put("/api/v1/notifications/{messageId}/read", sentMessageId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 5. 删除消息
        mockMvc.perform(delete("/api/v1/notifications/{messageId}", sentMessageId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @Order(4)
    @DisplayName("4. 消息模板管理测试")
    void testMessageTemplateManagement() throws Exception {
        // 1. 创建消息模板
        String templateRequest = """
                {
                    "name": "请假审批通知模板",
                    "code": "LEAVE_APPROVAL_NOTICE",
                    "channelType": "EMAIL",
                    "category": "WORKFLOW",
                    "subject": "请假审批结果通知",
                    "content": "尊敬的${userName}，您的请假申请已${result}。申请时间：${applyTime}，审批时间：${approvalTime}。",
                    "variables": ["userName", "result", "applyTime", "approvalTime"],
                    "enabled": true
                }
                """;

        MvcResult createResult = mockMvc.perform(post("/api/v1/notifications/templates")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(templateRequest))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").isString())
                .andReturn();

        templateId = objectMapper.readTree(createResult.getResponse().getContentAsString())
                .get("data").get("id").asText();

        // 2. 查询模板
        mockMvc.perform(get("/api/v1/notifications/templates/{templateId}", templateId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.code").value("LEAVE_APPROVAL_NOTICE"));

        // 3. 分页查询模板
        mockMvc.perform(post("/api/v1/notifications/templates/query")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("""
                                {
                                    "page": 1,
                                    "size": 10,
                                    "channelType": "EMAIL",
                                    "category": "WORKFLOW"
                                }
                                """))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.records").isArray());

        // 4. 更新模板
        String updateRequest = """
                {
                    "name": "请假审批通知模板（更新）",
                    "subject": "请假审批结果通知（更新）",
                    "content": "尊敬的${userName}，您的请假申请已${result}。详情请查看系统。",
                    "enabled": true
                }
                """;

        mockMvc.perform(put("/api/v1/notifications/templates/{templateId}", templateId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(updateRequest))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 5. 使用模板发送消息
        NotificationSendRequest templateRequest2 = new NotificationSendRequest();
        templateRequest2.setChannelType(ChannelType.EMAIL.name());
        templateRequest2.setRecipients(Arrays.asList("<EMAIL>"));
        templateRequest2.setTemplateId(templateId);

        Map<String, Object> variables = new HashMap<>();
        variables.put("userName", "张三");
        variables.put("result", "通过");
        variables.put("applyTime", "2024-01-15 09:00:00");
        variables.put("approvalTime", "2024-01-15 14:30:00");
        templateRequest2.setVariables(variables);

        mockMvc.perform(post("/api/v1/notifications/send")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(templateRequest2)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @Order(5)
    @DisplayName("5. 统计分析功能测试")
    void testStatisticsAndAnalytics() throws Exception {
        // 1. 获取发送统计
        mockMvc.perform(get("/api/v1/notifications/statistics/send")
                        .param("startDate", "2024-01-01")
                        .param("endDate", "2024-12-31"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalSent").isNumber())
                .andExpect(jsonPath("$.data.successCount").isNumber())
                .andExpect(jsonPath("$.data.failureCount").isNumber());

        // 2. 获取渠道统计
        mockMvc.perform(get("/api/v1/notifications/statistics/channels")
                        .param("startDate", "2024-01-01")
                        .param("endDate", "2024-12-31"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());

        // 3. 获取用户活跃度统计
        mockMvc.perform(get("/api/v1/notifications/statistics/user-activity")
                        .param("startDate", "2024-01-01")
                        .param("endDate", "2024-12-31"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());

        // 4. 获取趋势分析
        mockMvc.perform(get("/api/v1/notifications/statistics/trends")
                        .param("startDate", "2024-01-01")
                        .param("endDate", "2024-12-31")
                        .param("granularity", "day"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    @Order(6)
    @DisplayName("6. 系统管理功能测试")
    void testSystemManagementFeatures() throws Exception {
        // 1. 发送系统通知
        mockMvc.perform(post("/api/v1/notifications/system/send")
                        .param("title", "系统维护通知")
                        .param("content", "系统将于今晚22:00-24:00进行维护，请提前保存工作。")
                        .param("recipients", "all-users")
                        .param("channelTypes", "INTERNAL,EMAIL"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 2. 获取渠道状态
        mockMvc.perform(get("/api/v1/notifications/channels/status"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());

        // 3. 测试渠道连接
        mockMvc.perform(post("/api/v1/notifications/channels/EMAIL/test"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 4. 获取系统配置
        mockMvc.perform(get("/api/v1/notifications/config"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isMap());

        // 5. 健康检查
        mockMvc.perform(get("/api/v1/notifications/health"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.status").value("UP"));
    }

    @Test
    @Order(7)
    @DisplayName("7. 清理测试数据")
    void testCleanupTestData() throws Exception {
        if (templateId != null) {
            // 删除测试模板
            mockMvc.perform(delete("/api/v1/notifications/templates/{templateId}", templateId))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true));
        }
    }

    // ==================== 辅助方法 ====================

    private NotificationSendRequest createEmailNotificationRequest() {
        NotificationSendRequest request = new NotificationSendRequest();
        request.setChannelType(ChannelType.EMAIL.name());
        request.setRecipients(Arrays.asList("<EMAIL>"));
        request.setTitle("测试邮件通知");
        request.setContent("这是一条测试邮件通知内容。");
        request.setPriority(50);
        return request;
    }

    private NotificationSendRequest createSmsNotificationRequest() {
        NotificationSendRequest request = new NotificationSendRequest();
        request.setChannelType(ChannelType.SMS.name());
        request.setRecipients(Arrays.asList("13800138000"));
        request.setTitle("测试短信");
        request.setContent("这是一条测试短信内容。");
        request.setPriority(80);
        return request;
    }

    private NotificationSendRequest createInternalNotificationRequest() {
        NotificationSendRequest request = new NotificationSendRequest();
        request.setChannelType(ChannelType.INTERNAL.name());
        request.setRecipients(Arrays.asList("test-user"));
        request.setTitle("测试站内信");
        request.setContent("这是一条测试站内信内容。");
        request.setPriority(30);
        return request;
    }

    private NotificationSendRequest createWeChatNotificationRequest() {
        NotificationSendRequest request = new NotificationSendRequest();
        request.setChannelType(ChannelType.WECHAT.name());
        request.setRecipients(Arrays.asList("wechat-user-id"));
        request.setTitle("测试微信通知");
        request.setContent("这是一条测试微信通知内容。");
        request.setPriority(60);
        return request;
    }

    private NotificationSendRequest createDingTalkNotificationRequest() {
        NotificationSendRequest request = new NotificationSendRequest();
        request.setChannelType(ChannelType.DINGTALK.name());
        request.setRecipients(Arrays.asList("dingtalk-user-id"));
        request.setTitle("测试钉钉通知");
        request.setContent("这是一条测试钉钉通知内容。");
        request.setPriority(60);
        return request;
    }

    private NotificationSendRequest createBatchNotificationRequest() {
        NotificationSendRequest request = new NotificationSendRequest();
        request.setChannelType(ChannelType.EMAIL.name());
        request.setRecipients(Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>"));
        request.setTitle("批量测试通知");
        request.setContent("这是一条批量测试通知内容。");
        request.setBatch(true);
        request.setPriority(40);
        return request;
    }
}
