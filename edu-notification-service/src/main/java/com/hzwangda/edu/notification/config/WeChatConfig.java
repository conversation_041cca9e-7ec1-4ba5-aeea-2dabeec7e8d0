package com.hzwangda.edu.notification.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 微信配置类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
@Component
@ConfigurationProperties(prefix = "hky.notification.wechat")
public class WeChatConfig {

    /**
     * 是否启用微信功能
     */
    private boolean enabled = false;

    /**
     * 微信类型（work, service, mini）
     */
    private String type = "work";

    /**
     * 企业微信配置
     */
    private WeChatWorkConfig work = new WeChatWorkConfig();

    /**
     * 微信服务号配置
     */
    private WeChatServiceConfig service = new WeChatServiceConfig();

    /**
     * 微信小程序配置
     */
    private WeChatMiniConfig mini = new WeChatMiniConfig();

    /**
     * 企业微信配置
     */
    @Data
    public static class WeChatWorkConfig {
        /**
         * 企业ID
         */
        private String corpId;

        /**
         * 应用Secret
         */
        private String corpSecret;

        /**
         * 应用ID
         */
        private String agentId;

        /**
         * API地址
         */
        private String apiUrl = "https://qyapi.weixin.qq.com";

        /**
         * 获取Token的URL
         */
        private String tokenUrl = "/cgi-bin/gettoken";

        /**
         * 发送消息的URL
         */
        private String sendMessageUrl = "/cgi-bin/message/send";

        /**
         * 上传媒体文件的URL
         */
        private String uploadMediaUrl = "/cgi-bin/media/upload";

        /**
         * 获取用户信息的URL
         */
        private String getUserInfoUrl = "/cgi-bin/user/get";
    }

    /**
     * 微信服务号配置
     */
    @Data
    public static class WeChatServiceConfig {
        /**
         * AppID
         */
        private String appId;

        /**
         * AppSecret
         */
        private String appSecret;

        /**
         * API地址
         */
        private String apiUrl = "https://api.weixin.qq.com";

        /**
         * 获取Token的URL
         */
        private String tokenUrl = "/cgi-bin/token";

        /**
         * 发送模板消息的URL
         */
        private String sendTemplateUrl = "/cgi-bin/message/template/send";

        /**
         * 获取用户信息的URL
         */
        private String getUserInfoUrl = "/cgi-bin/user/info";
    }

    /**
     * 微信小程序配置
     */
    @Data
    public static class WeChatMiniConfig {
        /**
         * AppID
         */
        private String appId;

        /**
         * AppSecret
         */
        private String appSecret;

        /**
         * API地址
         */
        private String apiUrl = "https://api.weixin.qq.com";

        /**
         * 获取Token的URL
         */
        private String tokenUrl = "/cgi-bin/token";

        /**
         * 发送订阅消息的URL
         */
        private String sendSubscribeUrl = "/cgi-bin/message/subscribe/send";
    }

    /**
     * 消息发送限制配置
     */
    @Data
    public static class WeChatLimitConfig {
        /**
         * 每分钟最大发送数量
         */
        private int maxPerMinute = 20;

        /**
         * 每小时最大发送数量
         */
        private int maxPerHour = 200;

        /**
         * 每天最大发送数量
         */
        private int maxPerDay = 2000;
    }

    /**
     * 消息发送限制
     */
    private WeChatLimitConfig limit = new WeChatLimitConfig();
}
