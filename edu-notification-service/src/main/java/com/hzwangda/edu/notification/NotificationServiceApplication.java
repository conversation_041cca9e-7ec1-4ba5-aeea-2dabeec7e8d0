package com.hzwangda.edu.notification;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 消息通知服务启动类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = {"com.hzwangda.edu.notification", "com.hzwangda.edu.common"})
@EnableJpaAuditing
@EnableJpaRepositories(basePackages = "com.hzwangda.edu.notification.repository")
@EntityScan(basePackages = {"com.hzwangda.edu.notification.entity"})
@EnableAsync
@EnableScheduling
public class NotificationServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(NotificationServiceApplication.class, args);
    }
}
