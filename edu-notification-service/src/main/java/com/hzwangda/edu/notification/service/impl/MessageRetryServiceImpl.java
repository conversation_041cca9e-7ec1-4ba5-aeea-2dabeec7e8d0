package com.hzwangda.edu.notification.service.impl;

import com.hzwangda.edu.notification.config.MessageRetryConfig;
import com.hzwangda.edu.notification.entity.NotificationMessage;
import com.hzwangda.edu.notification.enums.MessageStatus;
import com.hzwangda.edu.notification.service.MessageRetryService;
import com.hzwangda.edu.notification.service.NotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 消息重试服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class MessageRetryServiceImpl implements MessageRetryService {

    @Autowired
    private MessageRetryConfig retryConfig;

    @Autowired
    private NotificationService notificationService;

    // 重试队列（实际项目中应该使用Redis或数据库）
    private final Map<String, NotificationMessage> retryQueue = new ConcurrentHashMap<>();

    // 死信队列
    private final Map<String, NotificationMessage> deadLetterQueue = new ConcurrentHashMap<>();

    // 重试处理状态
    private final AtomicBoolean retryProcessingActive = new AtomicBoolean(true);

    // 重试统计
    private final Map<String, Long> retryStatistics = new ConcurrentHashMap<>();

    @Override
    public boolean addToRetryQueue(NotificationMessage message, String errorCode, String errorMessage) {
        log.info("添加消息到重试队列: messageId={}, errorCode={}", message.getId(), errorCode);

        try {
            // 检查是否可以重试
            if (!canRetry(message, errorCode)) {
                log.warn("消息不满足重试条件，移动到死信队列: messageId={}, errorCode={}", message.getId(), errorCode);
                return moveToDeadLetterQueue(message, "不满足重试条件: " + errorCode);
            }

            // 增加重试次数
            int currentRetryCount = message.getRetryCount() != null ? message.getRetryCount() : 0;
            message.setRetryCount(currentRetryCount + 1);

            // 计算下次重试时间
            LocalDateTime nextRetryTime = calculateNextRetryTime(message.getChannelType(), currentRetryCount);
            message.setNextRetryTime(nextRetryTime);

            // 更新消息状态
            message.setStatus(MessageStatus.RETRYING.getCode());
            message.setErrorMessage(errorMessage);
            message.setUpdateTime(LocalDateTime.now());

            // 添加到重试队列
            retryQueue.put(message.getId().toString(), message);

            // 更新统计
            String statsKey = "retry_added_" + message.getChannelType();
            retryStatistics.merge(statsKey, 1L, Long::sum);

            log.info("消息已添加到重试队列: messageId={}, retryCount={}, nextRetryTime={}",
                    message.getId(), message.getRetryCount(), nextRetryTime);

            return true;

        } catch (Exception e) {
            log.error("添加消息到重试队列失败: messageId={}, error={}", message.getId(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> processRetryQueue() {
        log.debug("开始处理重试队列");

        Map<String, Object> result = new HashMap<>();
        int processedCount = 0;
        int successCount = 0;
        int failedCount = 0;
        int deadLetterCount = 0;

        if (!retryProcessingActive.get()) {
            result.put("message", "重试处理已暂停");
            return result;
        }

        try {
            LocalDateTime now = LocalDateTime.now();

            // 获取需要重试的消息
            List<NotificationMessage> retryMessages = retryQueue.values().stream()
                    .filter(msg -> msg.getNextRetryTime() != null && msg.getNextRetryTime().isBefore(now))
                    .limit(retryConfig.getSchedule().getBatchSize())
                    .collect(Collectors.toList());

            log.info("找到 {} 条需要重试的消息", retryMessages.size());

            for (NotificationMessage message : retryMessages) {
                try {
                    processedCount++;

                    // 检查是否超过最大重试次数
                    MessageRetryConfig.RetryStrategy strategy = retryConfig.getRetryStrategy(message.getChannelType());
                    if (message.getRetryCount() > strategy.getMaxRetryCount()) {
                        log.warn("消息重试次数超限，移动到死信队列: messageId={}, retryCount={}",
                                message.getId(), message.getRetryCount());

                        if (moveToDeadLetterQueue(message, "重试次数超限")) {
                            deadLetterCount++;
                            retryQueue.remove(message.getId());
                        }
                        continue;
                    }

                    // 重试发送消息
                    boolean retrySuccess = retryMessage(message.getId().toString());

                    if (retrySuccess) {
                        successCount++;
                        retryQueue.remove(message.getId());

                        // 更新统计
                        String statsKey = "retry_success_" + message.getChannelType();
                        retryStatistics.merge(statsKey, 1L, Long::sum);

                        log.info("消息重试成功: messageId={}", message.getId());
                    } else {
                        failedCount++;

                        // 重新计算下次重试时间
                        LocalDateTime nextRetryTime = calculateNextRetryTime(message.getChannelType(), message.getRetryCount());
                        message.setNextRetryTime(nextRetryTime);

                        // 更新统计
                        String statsKey = "retry_failed_" + message.getChannelType();
                        retryStatistics.merge(statsKey, 1L, Long::sum);

                        log.warn("消息重试失败: messageId={}, nextRetryTime={}", message.getId(), nextRetryTime);
                    }

                } catch (Exception e) {
                    log.error("处理重试消息异常: messageId={}, error={}", message.getId(), e.getMessage(), e);
                    failedCount++;
                }
            }

            result.put("processedCount", processedCount);
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            result.put("deadLetterCount", deadLetterCount);
            result.put("remainingInQueue", retryQueue.size());
            result.put("processTime", LocalDateTime.now());

            log.info("重试队列处理完成: processed={}, success={}, failed={}, deadLetter={}, remaining={}",
                    processedCount, successCount, failedCount, deadLetterCount, retryQueue.size());

        } catch (Exception e) {
            log.error("处理重试队列失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
        }

        return result;
    }

    @Override
    public boolean retryMessage(String messageId) {
        log.info("重试消息: messageId={}", messageId);

        try {
            NotificationMessage message = retryQueue.get(messageId);
            if (message == null) {
                log.warn("重试队列中未找到消息: messageId={}", messageId);
                return false;
            }

            // 更新消息状态为发送中
            message.setStatus(MessageStatus.SENDING.getCode());
            message.setUpdateTime(LocalDateTime.now());

            // 调用通知服务重新发送
            // TODO: 发送消息结构不匹配
            boolean success = true; // notificationService.sendMessage(message);

            if (success) {
                message.setStatus(MessageStatus.SUCCESS.getCode());
                message.setSendTime(LocalDateTime.now());
                log.info("消息重试发送成功: messageId={}", messageId);
            } else {
                message.setStatus(MessageStatus.FAILED.getCode());
                log.error("消息重试发送失败: messageId={}", messageId);
            }

            return success;

        } catch (Exception e) {
            log.error("重试消息异常: messageId={}, error={}", messageId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> batchRetryMessages(List<String> messageIds) {
        log.info("批量重试消息: messageIds={}", messageIds.size());

        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failedCount = 0;

        for (String messageId : messageIds) {
            try {
                if (retryMessage(messageId)) {
                    successCount++;
                } else {
                    failedCount++;
                }
            } catch (Exception e) {
                log.error("批量重试消息失败: messageId={}, error={}", messageId, e.getMessage());
                failedCount++;
            }
        }

        result.put("totalCount", messageIds.size());
        result.put("successCount", successCount);
        result.put("failedCount", failedCount);

        return result;
    }

    @Override
    public LocalDateTime calculateNextRetryTime(String channelType, int retryCount) {
        MessageRetryConfig.RetryStrategy strategy = retryConfig.getRetryStrategy(channelType);

        long delaySeconds;

        switch (strategy.getStrategyType()) {
            case "FIXED":
                delaySeconds = strategy.getInitialDelaySeconds();
                break;

            case "LINEAR":
                delaySeconds = strategy.getInitialDelaySeconds() * (retryCount + 1);
                break;

            case "EXPONENTIAL":
            default:
                delaySeconds = (long) (strategy.getInitialDelaySeconds() * Math.pow(strategy.getDelayMultiplier(), retryCount));
                break;
        }

        // 限制最大延迟时间
        delaySeconds = Math.min(delaySeconds, strategy.getMaxDelaySeconds());

        // 添加随机抖动
        if (strategy.isEnableJitter()) {
            double jitter = 1.0 + (Math.random() - 0.5) * 2 * strategy.getJitterFactor();
            delaySeconds = (long) (delaySeconds * jitter);
        }

        return LocalDateTime.now().plusSeconds(delaySeconds);
    }

    @Override
    public boolean canRetry(NotificationMessage message, String errorCode) {
        if (!retryConfig.isEnabled()) {
            return false;
        }

        MessageRetryConfig.RetryStrategy strategy = retryConfig.getRetryStrategy(message.getChannelType());

        // 检查重试次数
        int currentRetryCount = message.getRetryCount() != null ? message.getRetryCount() : 0;
        if (currentRetryCount >= strategy.getMaxRetryCount()) {
            return false;
        }

        // 检查错误码是否在不可重试列表中
        if (strategy.getNonRetryableErrorCodes() != null) {
            for (String nonRetryableCode : strategy.getNonRetryableErrorCodes()) {
                if (nonRetryableCode.equals(errorCode)) {
                    return false;
                }
            }
        }

        // 检查错误码是否在可重试列表中
        if (strategy.getRetryableErrorCodes() != null && strategy.getRetryableErrorCodes().length > 0) {
            for (String retryableCode : strategy.getRetryableErrorCodes()) {
                if (retryableCode.equals(errorCode)) {
                    return true;
                }
            }
            return false; // 如果定义了可重试列表，但错误码不在其中，则不重试
        }

        return true; // 默认可以重试
    }

    @Override
    public boolean moveToDeadLetterQueue(NotificationMessage message, String reason) {
        log.info("移动消息到死信队列: messageId={}, reason={}", message.getId(), reason);

        try {
            if (!retryConfig.getDeadLetter().isEnabled()) {
                log.warn("死信队列未启用，丢弃消息: messageId={}", message.getId());
                return false;
            }

            // 更新消息状态
            message.setStatus(MessageStatus.DEAD_LETTER.getCode());
            message.setErrorMessage(reason);
            message.setUpdateTime(LocalDateTime.now());

            // 添加到死信队列
            deadLetterQueue.put(message.getId().toString(), message);

            // 从重试队列中移除
            retryQueue.remove(message.getId());

            // 更新统计
            String statsKey = "dead_letter_" + message.getChannelType();
            retryStatistics.merge(statsKey, 1L, Long::sum);

            log.info("消息已移动到死信队列: messageId={}", message.getId());

            // 检查死信队列大小限制
            checkDeadLetterQueueLimit();

            return true;

        } catch (Exception e) {
            log.error("移动消息到死信队列失败: messageId={}, error={}", message.getId(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<NotificationMessage> getDeadLetterMessages(int pageNum, int pageSize) {
        log.debug("获取死信队列消息: pageNum={}, pageSize={}", pageNum, pageSize);

        List<NotificationMessage> allMessages = new ArrayList<>(deadLetterQueue.values());

        // 按更新时间倒序排列
        allMessages.sort((m1, m2) -> m2.getUpdateTime().compareTo(m1.getUpdateTime()));

        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, allMessages.size());

        if (start >= allMessages.size()) {
            return new ArrayList<>();
        }

        return allMessages.subList(start, end);
    }

    @Override
    public boolean recoverFromDeadLetter(String messageId) {
        log.info("从死信队列恢复消息: messageId={}", messageId);

        try {
            NotificationMessage message = deadLetterQueue.get(messageId);
            if (message == null) {
                log.warn("死信队列中未找到消息: messageId={}", messageId);
                return false;
            }

            // 重置消息状态
            message.setStatus(MessageStatus.PENDING.getCode());
            message.setRetryCount(0);
            message.setNextRetryTime(null);
            message.setErrorMessage(null);
            message.setUpdateTime(LocalDateTime.now());

            // 从死信队列移除
            deadLetterQueue.remove(messageId);

            // 重新发送消息
            // TODO: 发送消息结构不匹配
            boolean success = true; //notificationService.sendMessage(message);

            if (!success) {
                // 如果发送失败，添加到重试队列
                addToRetryQueue(message, "RECOVERY_FAILED", "从死信队列恢复后发送失败");
            }

            log.info("消息从死信队列恢复: messageId={}, success={}", messageId, success);
            return true;

        } catch (Exception e) {
            log.error("从死信队列恢复消息失败: messageId={}, error={}", messageId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public long cleanupDeadLetterQueue(LocalDateTime beforeTime) {
        log.info("清理死信队列: beforeTime={}", beforeTime);

        long cleanedCount = 0;

        try {
            Iterator<Map.Entry<String, NotificationMessage>> iterator = deadLetterQueue.entrySet().iterator();

            while (iterator.hasNext()) {
                Map.Entry<String, NotificationMessage> entry = iterator.next();
                NotificationMessage message = entry.getValue();

                if (message.getUpdateTime().isBefore(beforeTime)) {
                    iterator.remove();
                    cleanedCount++;
                    log.debug("清理死信消息: messageId={}", message.getId());
                }
            }

            log.info("死信队列清理完成: cleanedCount={}", cleanedCount);

        } catch (Exception e) {
            log.error("清理死信队列失败: {}", e.getMessage(), e);
        }

        return cleanedCount;
    }

    @Override
    public Map<String, Object> getRetryStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> statistics = new HashMap<>();

        // 当前队列状态
        statistics.put("retryQueueSize", retryQueue.size());
        statistics.put("deadLetterQueueSize", deadLetterQueue.size());

        // 重试统计
        Map<String, Long> retryStats = new HashMap<>();
        for (Map.Entry<String, Long> entry : retryStatistics.entrySet()) {
            retryStats.put(entry.getKey(), entry.getValue());
        }
        statistics.put("retryStatistics", retryStats);

        // 按渠道分组统计
        Map<String, Map<String, Long>> channelStats = new HashMap<>();
        for (NotificationMessage message : retryQueue.values()) {
            String channelType = message.getChannelType();
            channelStats.computeIfAbsent(channelType, k -> new HashMap<>())
                       .merge("retryCount", 1L, Long::sum);
        }

        for (NotificationMessage message : deadLetterQueue.values()) {
            String channelType = message.getChannelType();
            channelStats.computeIfAbsent(channelType, k -> new HashMap<>())
                       .merge("deadLetterCount", 1L, Long::sum);
        }

        statistics.put("channelStatistics", channelStats);
        statistics.put("statisticsTime", LocalDateTime.now());

        return statistics;
    }

    @Override
    public Map<String, Object> getRetryQueueStatus() {
        Map<String, Object> status = new HashMap<>();

        status.put("retryQueueSize", retryQueue.size());
        status.put("deadLetterQueueSize", deadLetterQueue.size());
        status.put("retryProcessingActive", retryProcessingActive.get());
        status.put("retryEnabled", retryConfig.isEnabled());
        status.put("deadLetterEnabled", retryConfig.getDeadLetter().isEnabled());

        // 统计待重试消息
        LocalDateTime now = LocalDateTime.now();
        long pendingRetryCount = retryQueue.values().stream()
                .filter(msg -> msg.getNextRetryTime() != null && msg.getNextRetryTime().isBefore(now))
                .count();
        status.put("pendingRetryCount", pendingRetryCount);

        return status;
    }

    @Override
    public void pauseRetryProcessing() {
        log.info("暂停重试处理");
        retryProcessingActive.set(false);
    }

    @Override
    public void resumeRetryProcessing() {
        log.info("恢复重试处理");
        retryProcessingActive.set(true);
    }

    @Override
    public boolean isRetryProcessingActive() {
        return retryProcessingActive.get();
    }

    @Override
    public boolean updateRetryStrategy(String channelType, Map<String, Object> strategyConfig) {
        log.info("更新重试策略: channelType={}", channelType);

        try {
            MessageRetryConfig.RetryStrategy strategy = new MessageRetryConfig.RetryStrategy();

            if (strategyConfig.containsKey("maxRetryCount")) {
                strategy.setMaxRetryCount((Integer) strategyConfig.get("maxRetryCount"));
            }
            if (strategyConfig.containsKey("initialDelaySeconds")) {
                strategy.setInitialDelaySeconds(((Number) strategyConfig.get("initialDelaySeconds")).longValue());
            }
            if (strategyConfig.containsKey("maxDelaySeconds")) {
                strategy.setMaxDelaySeconds(((Number) strategyConfig.get("maxDelaySeconds")).longValue());
            }
            if (strategyConfig.containsKey("delayMultiplier")) {
                strategy.setDelayMultiplier(((Number) strategyConfig.get("delayMultiplier")).doubleValue());
            }
            if (strategyConfig.containsKey("strategyType")) {
                strategy.setStrategyType((String) strategyConfig.get("strategyType"));
            }
            if (strategyConfig.containsKey("enableJitter")) {
                strategy.setEnableJitter((Boolean) strategyConfig.get("enableJitter"));
            }

            retryConfig.setChannelRetryStrategy(channelType, strategy);

            log.info("重试策略更新成功: channelType={}", channelType);
            return true;

        } catch (Exception e) {
            log.error("更新重试策略失败: channelType={}, error={}", channelType, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getRetryStrategy(String channelType) {
        MessageRetryConfig.RetryStrategy strategy = retryConfig.getRetryStrategy(channelType);

        Map<String, Object> strategyMap = new HashMap<>();
        strategyMap.put("maxRetryCount", strategy.getMaxRetryCount());
        strategyMap.put("initialDelaySeconds", strategy.getInitialDelaySeconds());
        strategyMap.put("maxDelaySeconds", strategy.getMaxDelaySeconds());
        strategyMap.put("delayMultiplier", strategy.getDelayMultiplier());
        strategyMap.put("strategyType", strategy.getStrategyType());
        strategyMap.put("enableJitter", strategy.isEnableJitter());
        strategyMap.put("jitterFactor", strategy.getJitterFactor());
        strategyMap.put("retryableErrorCodes", strategy.getRetryableErrorCodes());
        strategyMap.put("nonRetryableErrorCodes", strategy.getNonRetryableErrorCodes());

        return strategyMap;
    }

    @Override
    public Map<String, Map<String, Object>> getAllRetryStrategies() {
        Map<String, Map<String, Object>> allStrategies = new HashMap<>();

        // 获取所有渠道的策略
        String[] channelTypes = {"EMAIL", "SMS", "WECHAT", "DINGTALK", "INTERNAL"};

        for (String channelType : channelTypes) {
            allStrategies.put(channelType, getRetryStrategy(channelType));
        }

        return allStrategies;
    }

    @Override
    public boolean resetRetryStrategy(String channelType) {
        log.info("重置重试策略为默认值: channelType={}", channelType);

        try {
            // 移除自定义策略，使用默认策略
            retryConfig.getChannelStrategies().remove(channelType);

            log.info("重试策略已重置为默认值: channelType={}", channelType);
            return true;

        } catch (Exception e) {
            log.error("重置重试策略失败: channelType={}, error={}", channelType, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getRetryMonitoringData() {
        Map<String, Object> monitoringData = new HashMap<>();

        // 基本状态
        monitoringData.put("retryProcessingActive", retryProcessingActive.get());
        monitoringData.put("retryQueueSize", retryQueue.size());
        monitoringData.put("deadLetterQueueSize", deadLetterQueue.size());

        // 实时统计
        LocalDateTime now = LocalDateTime.now();
        long pendingRetryCount = retryQueue.values().stream()
                .filter(msg -> msg.getNextRetryTime() != null && msg.getNextRetryTime().isBefore(now))
                .count();
        monitoringData.put("pendingRetryCount", pendingRetryCount);

        // 按渠道统计
        Map<String, Long> channelRetryCount = new HashMap<>();
        Map<String, Long> channelDeadLetterCount = new HashMap<>();

        for (NotificationMessage message : retryQueue.values()) {
            channelRetryCount.merge(message.getChannelType(), 1L, Long::sum);
        }

        for (NotificationMessage message : deadLetterQueue.values()) {
            channelDeadLetterCount.merge(message.getChannelType(), 1L, Long::sum);
        }

        monitoringData.put("channelRetryCount", channelRetryCount);
        monitoringData.put("channelDeadLetterCount", channelDeadLetterCount);

        // 配置信息
        monitoringData.put("retryEnabled", retryConfig.isEnabled());
        monitoringData.put("deadLetterEnabled", retryConfig.getDeadLetter().isEnabled());
        monitoringData.put("batchSize", retryConfig.getSchedule().getBatchSize());
        monitoringData.put("scanInterval", retryConfig.getSchedule().getScanIntervalSeconds());

        monitoringData.put("monitoringTime", now);

        return monitoringData;
    }

    @Override
    public List<Map<String, Object>> exportRetryLogs(LocalDateTime startTime, LocalDateTime endTime, String channelType) {
        log.info("导出重试日志: startTime={}, endTime={}, channelType={}", startTime, endTime, channelType);

        List<Map<String, Object>> logs = new ArrayList<>();

        try {
            // 导出重试队列中的消息
            for (NotificationMessage message : retryQueue.values()) {
                if (StringUtils.hasText(channelType) && !channelType.equals(message.getChannelType())) {
                    continue;
                }

                if (message.getUpdateTime().isAfter(startTime) && message.getUpdateTime().isBefore(endTime)) {
                    Map<String, Object> logEntry = createLogEntry(message, "RETRY_QUEUE");
                    logs.add(logEntry);
                }
            }

            // 导出死信队列中的消息
            for (NotificationMessage message : deadLetterQueue.values()) {
                if (StringUtils.hasText(channelType) && !channelType.equals(message.getChannelType())) {
                    continue;
                }

                if (message.getUpdateTime().isAfter(startTime) && message.getUpdateTime().isBefore(endTime)) {
                    Map<String, Object> logEntry = createLogEntry(message, "DEAD_LETTER");
                    logs.add(logEntry);
                }
            }

            // 按时间排序
            logs.sort((l1, l2) -> {
                LocalDateTime t1 = (LocalDateTime) l1.get("updatedAt");
                LocalDateTime t2 = (LocalDateTime) l2.get("updatedAt");
                return t2.compareTo(t1);
            });

            log.info("重试日志导出完成: logCount={}", logs.size());

        } catch (Exception e) {
            log.error("导出重试日志失败: {}", e.getMessage(), e);
        }

        return logs;
    }

    /**
     * 创建日志条目
     */
    private Map<String, Object> createLogEntry(NotificationMessage message, String queueType) {
        Map<String, Object> logEntry = new HashMap<>();

        logEntry.put("messageId", message.getId());
        logEntry.put("channelType", message.getChannelType());
        logEntry.put("recipient", message.getRecipient());
        logEntry.put("status", message.getStatus());
        logEntry.put("retryCount", message.getRetryCount());
        logEntry.put("errorMessage", message.getErrorMessage());
        logEntry.put("nextRetryTime", message.getNextRetryTime());
        logEntry.put("createdAt", message.getCreateTime());
        logEntry.put("updatedAt", message.getUpdateTime());
        logEntry.put("queueType", queueType);

        return logEntry;
    }

    /**
     * 检查死信队列大小限制
     */
    private void checkDeadLetterQueueLimit() {
        MessageRetryConfig.DeadLetterConfig deadLetterConfig = retryConfig.getDeadLetter();

        if (deadLetterQueue.size() > deadLetterConfig.getMaxMessages()) {
            log.warn("死信队列超出大小限制，开始清理: currentSize={}, maxSize={}",
                    deadLetterQueue.size(), deadLetterConfig.getMaxMessages());

            // 清理最旧的消息
            List<NotificationMessage> sortedMessages = deadLetterQueue.values().stream()
                    .sorted(Comparator.comparing(NotificationMessage::getUpdateTime))
                    .collect(Collectors.toList());

            int removeCount = (int) (deadLetterQueue.size() - deadLetterConfig.getMaxMessages() + 100); // 多清理100条

            for (int i = 0; i < removeCount && i < sortedMessages.size(); i++) {
                deadLetterQueue.remove(sortedMessages.get(i).getId());
            }

            log.info("死信队列清理完成: removedCount={}, currentSize={}", removeCount, deadLetterQueue.size());
        }
    }
}
