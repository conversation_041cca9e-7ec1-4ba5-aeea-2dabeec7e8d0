package com.hzwangda.edu.notification.service.channel.handler;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hzwangda.edu.notification.dto.NotificationSendRequest;
import com.hzwangda.edu.notification.entity.NotificationMessage;
import com.hzwangda.edu.notification.enums.ChannelType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import jakarta.mail.internet.MimeMessage;
import java.util.List;

/**
 * 邮件渠道处理器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("emailChannelHandler")
public class EmailChannelHandler implements ChannelHandler {

    private static final Logger logger = LoggerFactory.getLogger(EmailChannelHandler.class);

    @Autowired
    private JavaMailSender mailSender;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${spring.mail.username:<EMAIL>}")
    private String fromEmail;

    @Value("${spring.mail.personal:业务管理系统}")
    private String fromPersonal;

    @Override
    public boolean send(NotificationMessage message) {
        logger.debug("发送邮件: 消息ID={}, 接收人={}", message.getMessageId(), message.getRecipient());

        try {
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");

            // 设置发件人
            helper.setFrom(fromEmail, fromPersonal);

            // 设置收件人
            helper.setTo(message.getRecipient());

            // 设置主题
            helper.setSubject(message.getMessageTitle());

            // 设置内容
            helper.setText(message.getMessageContent(), true);

            // 处理附件
            addAttachments(helper, message);

            // 发送邮件
            mailSender.send(mimeMessage);

            logger.info("邮件发送成功: 消息ID={}, 接收人={}", message.getMessageId(), message.getRecipient());
            return true;

        } catch (Exception e) {
            logger.error("邮件发送失败: 消息ID={}, 接收人={}, 错误={}",
                        message.getMessageId(), message.getRecipient(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Async("notificationExecutor")
    public void sendAsync(NotificationMessage message) {
        logger.debug("异步发送邮件: 消息ID={}, 接收人={}", message.getMessageId(), message.getRecipient());

        boolean success = send(message);

        // 这里可以添加发送结果的回调处理
        if (success) {
            logger.debug("异步邮件发送成功: 消息ID={}", message.getMessageId());
        } else {
            logger.error("异步邮件发送失败: 消息ID={}", message.getMessageId());
        }
    }

    @Override
    public boolean isAvailable() {
        try {
            // 检查邮件服务器连接
            mailSender.createMimeMessage();
            return true;
        } catch (Exception e) {
            logger.error("邮件服务不可用: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public String getStatus() {
        return isAvailable() ? "AVAILABLE" : "UNAVAILABLE";
    }

    @Override
    public String getSupportedChannelType() {
        return ChannelType.EMAIL.getCode();
    }

    /**
     * 添加附件
     */
    private void addAttachments(MimeMessageHelper helper, NotificationMessage message) {
        try {
            if (message.getAttachments() != null) {
                List<NotificationSendRequest.AttachmentInfo> attachments = objectMapper.readValue(
                    message.getAttachments(),
                    new TypeReference<List<NotificationSendRequest.AttachmentInfo>>() {}
                );

                for (NotificationSendRequest.AttachmentInfo attachment : attachments) {
                    // 这里可以根据实际需求添加附件处理逻辑
                    // 例如从文件服务下载文件并添加到邮件中
                    logger.debug("处理邮件附件: {}", attachment.getFileName());
                }
            }
        } catch (Exception e) {
            logger.warn("处理邮件附件失败: {}", e.getMessage());
        }
    }
}
