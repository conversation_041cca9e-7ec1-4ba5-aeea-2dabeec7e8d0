package com.hzwangda.edu.notification.entity;

import com.hzwangda.edu.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 消息订阅实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "notification_subscription", indexes = {
    @Index(name = "idx_user_id", columnList = "user_id"),
    @Index(name = "idx_template_type", columnList = "template_type"),
    @Index(name = "idx_ns_channel_type", columnList = "channel_type"),
    @Index(name = "idx_ns_status", columnList = "status")
})
@Schema(description = "消息订阅")
public class NotificationSubscription extends BaseEntity {

    @NotNull(message = "用户ID不能为空")
    @Column(name = "user_id", nullable = false)
    @Schema(description = "用户ID", example = "1")
    private Long userId;

    @NotBlank(message = "用户名不能为空")
    @Size(max = 64, message = "用户名长度不能超过64个字符")
    @Column(name = "username", length = 64, nullable = false)
    @Schema(description = "用户名", example = "zhangsan")
    private String username;

    @Size(max = 64, message = "用户姓名长度不能超过64个字符")
    @Column(name = "user_name", length = 64)
    @Schema(description = "用户姓名", example = "张三")
    private String userName;

    @NotBlank(message = "模板类型不能为空")
    @Size(max = 32, message = "模板类型长度不能超过32个字符")
    @Column(name = "template_type", length = 32, nullable = false)
    @Schema(description = "模板类型", example = "WORKFLOW")
    private String templateType;

    @Size(max = 64, message = "模板编码长度不能超过64个字符")
    @Column(name = "template_code", length = 64)
    @Schema(description = "模板编码", example = "LEAVE_APPROVAL_NOTIFY")
    private String templateCode;

    @NotBlank(message = "通知渠道不能为空")
    @Size(max = 32, message = "通知渠道长度不能超过32个字符")
    @Column(name = "channel_type", length = 32, nullable = false)
    @Schema(description = "通知渠道", example = "EMAIL")
    private String channelType;

    @Size(max = 255, message = "接收地址长度不能超过255个字符")
    @Column(name = "recipient_address", length = 255)
    @Schema(description = "接收地址", example = "<EMAIL>")
    private String recipientAddress;

    @NotBlank(message = "订阅状态不能为空")
    @Size(max = 20, message = "订阅状态长度不能超过20个字符")
    @Column(name = "status", length = 20, nullable = false)
    @Schema(description = "订阅状态", example = "ACTIVE")
    private String status = "ACTIVE";

    @Column(name = "is_enabled", nullable = false)
    @Schema(description = "是否启用", example = "true")
    private Boolean isEnabled = true;

    @Column(name = "priority")
    @Schema(description = "优先级", example = "50")
    private Integer priority = 50;

    @Column(name = "notification_settings", columnDefinition = "TEXT")
    @Schema(description = "通知设置JSON")
    private String notificationSettings;

    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Column(name = "remark", length = 500)
    @Schema(description = "备注")
    private String remark;

    // 构造函数
    public NotificationSubscription() {
    }

    public NotificationSubscription(Long userId, String username, String templateType, String channelType) {
        this.userId = userId;
        this.username = username;
        this.templateType = templateType;
        this.channelType = channelType;
    }

    // Getter and Setter methods
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getTemplateType() {
        return templateType;
    }

    public void setTemplateType(String templateType) {
        this.templateType = templateType;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getRecipientAddress() {
        return recipientAddress;
    }

    public void setRecipientAddress(String recipientAddress) {
        this.recipientAddress = recipientAddress;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getIsEnabled() {
        return isEnabled;
    }

    public void setIsEnabled(Boolean isEnabled) {
        this.isEnabled = isEnabled;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getNotificationSettings() {
        return notificationSettings;
    }

    public void setNotificationSettings(String notificationSettings) {
        this.notificationSettings = notificationSettings;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
