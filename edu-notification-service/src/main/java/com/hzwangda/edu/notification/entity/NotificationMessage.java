package com.hzwangda.edu.notification.entity;

import com.hzwangda.edu.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 消息记录实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Setter
@Getter
@Entity
@Table(name = "notification_message", indexes = {
    @Index(name = "idx_message_id", columnList = "message_id"),
    @Index(name = "idx_template_code", columnList = "template_code"),
    @Index(name = "idx_channel_type", columnList = "channel_type"),
    @Index(name = "idx_recipient", columnList = "recipient"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_send_time", columnList = "send_time"),
    @Index(name = "idx_business_type", columnList = "business_type"),
    @Index(name = "idx_business_id", columnList = "business_id")
})
@Schema(description = "消息记录")
public class NotificationMessage extends BaseEntity {

    @NotBlank(message = "消息ID不能为空")
    @Size(max = 64, message = "消息ID长度不能超过64个字符")
    @Column(name = "message_id", length = 64, nullable = false, unique = true)
    @Schema(description = "消息ID", example = "MSG_20241219_001")
    private String messageId;

    @Size(max = 64, message = "外部消息ID长度不能超过64个字符")
    @Column(name = "external_id", length = 64)
    @Schema(description = "外部消息ID", example = "EXT_MSG_001")
    private String externalId;

    @Size(max = 64, message = "模板编码长度不能超过64个字符")
    @Column(name = "template_code", length = 64)
    @Schema(description = "模板编码", example = "LEAVE_APPROVAL_NOTIFY")
    private String templateCode;

    @NotBlank(message = "通知渠道不能为空")
    @Size(max = 32, message = "通知渠道长度不能超过32个字符")
    @Column(name = "channel_type", length = 32, nullable = false)
    @Schema(description = "通知渠道", example = "EMAIL")
    private String channelType;

    @NotBlank(message = "接收人不能为空")
    @Size(max = 255, message = "接收人长度不能超过255个字符")
    @Column(name = "recipient", length = 255, nullable = false)
    @Schema(description = "接收人", example = "<EMAIL>")
    private String recipient;

    @Size(max = 64, message = "接收人姓名长度不能超过64个字符")
    @Column(name = "recipient_name", length = 64)
    @Schema(description = "接收人姓名", example = "张三")
    private String recipientName;

    @Column(name = "recipient_id")
    @Schema(description = "接收人ID", example = "1")
    private Long recipientId;

    @Size(max = 255, message = "消息标题长度不能超过255个字符")
    @Column(name = "message_title", length = 255)
    @Schema(description = "消息标题", example = "【业务系统】请假审批通知")
    private String messageTitle;

    @Column(name = "message_content", columnDefinition = "TEXT")
    @Schema(description = "消息内容")
    private String messageContent;

    @Column(name = "message_variables", columnDefinition = "TEXT")
    @Schema(description = "消息变量JSON")
    private String messageVariables;

    @NotBlank(message = "消息状态不能为空")
    @Size(max = 20, message = "消息状态长度不能超过20个字符")
    @Column(name = "status", length = 20, nullable = false)
    @Schema(description = "消息状态", example = "PENDING")
    private String status;

    @Column(name = "priority")
    @Schema(description = "优先级", example = "50")
    private Integer priority = 50;

    @Column(name = "send_time")
    @Schema(description = "发送时间")
    private LocalDateTime sendTime;

    @Column(name = "sent_time")
    @Schema(description = "发送成功时间")
    private LocalDateTime sentTime;

    @Column(name = "failed_time")
    @Schema(description = "发送失败时间")
    private LocalDateTime failedTime;

    @Column(name = "scheduled_time")
    @Schema(description = "计划发送时间")
    private LocalDateTime scheduledTime;

    @Column(name = "delivered_time")
    @Schema(description = "送达时间")
    private LocalDateTime deliveredTime;

    @Column(name = "read_time")
    @Schema(description = "阅读时间")
    private LocalDateTime readTime;

    @Column(name = "retry_count")
    @Schema(description = "重试次数", example = "0")
    private Integer retryCount = 0;

    @Column(name = "max_retry_count")
    @Schema(description = "最大重试次数", example = "3")
    private Integer maxRetryCount = 3;

    @Size(max = 500, message = "错误信息长度不能超过500个字符")
    @Column(name = "error_message", length = 500)
    @Schema(description = "错误信息")
    private String errorMessage;

    @Size(max = 64, message = "业务类型长度不能超过64个字符")
    @Column(name = "business_type", length = 64)
    @Schema(description = "业务类型", example = "WORKFLOW")
    private String businessType;

    @Size(max = 64, message = "业务ID长度不能超过64个字符")
    @Column(name = "business_id", length = 64)
    @Schema(description = "业务ID", example = "LEAVE_20241219_001")
    private String businessId;

    @Size(max = 64, message = "发送人长度不能超过64个字符")
    @Column(name = "sender", length = 64)
    @Schema(description = "发送人", example = "system")
    private String sender;

    @Column(name = "sender_id")
    @Schema(description = "发送人ID", example = "0")
    private Long senderId;

    @Size(max = 255, message = "附件信息长度不能超过255个字符")
    @Column(name = "attachments", length = 255)
    @Schema(description = "附件信息JSON")
    private String attachments;

    @Column(name = "is_read", nullable = false)
    @Schema(description = "是否已读", example = "false")
    private Boolean isRead = false;

    @Column(name = "next_retry_time")
    @Schema(description = "下次重试时间")
    private LocalDateTime nextRetryTime;

    @Column(name = "expire_time")
    @Schema(description = "过期时间")
    private LocalDateTime expireTime;

    // 构造函数
    public NotificationMessage() {
    }

    public NotificationMessage(String messageId, String channelType, String recipient) {
        this.messageId = messageId;
        this.channelType = channelType;
        this.recipient = recipient;
        this.status = "PENDING";
    }
}
