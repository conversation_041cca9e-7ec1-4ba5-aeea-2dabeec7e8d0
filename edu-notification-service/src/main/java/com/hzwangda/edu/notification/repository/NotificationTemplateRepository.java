package com.hzwangda.edu.notification.repository;

import com.hzwangda.edu.notification.entity.NotificationTemplate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 消息模板Repository接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface NotificationTemplateRepository extends JpaRepository<NotificationTemplate, Long>, JpaSpecificationExecutor<NotificationTemplate> {

    /**
     * 根据模板编码查询模板
     */
    Optional<NotificationTemplate> findByTemplateCodeAndDeletedFalse(String templateCode);

    /**
     * 根据模板编码和渠道类型查询模板
     */
    Optional<NotificationTemplate> findByTemplateCodeAndChannelTypeAndDeletedFalse(String templateCode, String channelType);

    /**
     * 根据模板类型查询模板
     */
    List<NotificationTemplate> findByTemplateTypeAndDeletedFalseOrderBySortOrderAsc(String templateType);

    /**
     * 根据渠道类型查询模板
     */
    List<NotificationTemplate> findByChannelTypeAndDeletedFalseOrderBySortOrderAsc(String channelType);

    /**
     * 根据状态查询模板
     */
    Page<NotificationTemplate> findByStatusAndDeletedFalse(String status, Pageable pageable);

    /**
     * 查询所有激活的模板
     */
    @Query("SELECT t FROM NotificationTemplate t WHERE t.status = 'ACTIVE' AND t.deleted = false ORDER BY t.sortOrder ASC")
    List<NotificationTemplate> findAllActiveTemplates();

    /**
     * 查询指定类型和渠道的激活模板
     */
    @Query("SELECT t FROM NotificationTemplate t WHERE t.templateType = :templateType AND t.channelType = :channelType " +
           "AND t.status = 'ACTIVE' AND t.deleted = false ORDER BY t.sortOrder ASC")
    List<NotificationTemplate> findActiveTemplatesByTypeAndChannel(@Param("templateType") String templateType,
                                                                  @Param("channelType") String channelType);

    /**
     * 查询默认模板
     */
    @Query("SELECT t FROM NotificationTemplate t WHERE t.templateType = :templateType AND t.channelType = :channelType " +
           "AND t.isDefault = true AND t.status = 'ACTIVE' AND t.deleted = false")
    Optional<NotificationTemplate> findDefaultTemplate(@Param("templateType") String templateType,
                                                       @Param("channelType") String channelType);

    /**
     * 根据模板名称模糊查询
     */
    @Query("SELECT t FROM NotificationTemplate t WHERE t.templateName LIKE %:templateName% AND t.deleted = false")
    Page<NotificationTemplate> findByTemplateNameContaining(@Param("templateName") String templateName, Pageable pageable);

    /**
     * 统计各类型下的模板数量
     */
    @Query("SELECT t.templateType, COUNT(t) FROM NotificationTemplate t WHERE t.deleted = false GROUP BY t.templateType")
    List<Object[]> countByTemplateType();

    /**
     * 统计各渠道下的模板数量
     */
    @Query("SELECT t.channelType, COUNT(t) FROM NotificationTemplate t WHERE t.deleted = false GROUP BY t.channelType")
    List<Object[]> countByChannelType();

    /**
     * 统计各状态下的模板数量
     */
    @Query("SELECT t.status, COUNT(t) FROM NotificationTemplate t WHERE t.deleted = false GROUP BY t.status")
    List<Object[]> countByStatus();

    /**
     * 检查模板编码是否存在
     */
    boolean existsByTemplateCodeAndDeletedFalse(String templateCode);

    /**
     * 查询需要更新默认标记的模板
     */
    @Query("SELECT t FROM NotificationTemplate t WHERE t.templateType = :templateType AND t.channelType = :channelType " +
           "AND t.isDefault = true AND t.deleted = false")
    List<NotificationTemplate> findDefaultTemplatesByTypeAndChannel(@Param("templateType") String templateType,
                                                                   @Param("channelType") String channelType);
}
