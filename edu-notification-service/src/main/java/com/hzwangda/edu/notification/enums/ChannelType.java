package com.hzwangda.edu.notification.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通知渠道类型枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum ChannelType {

    // 邮件通知
    EMAIL("email", "邮件通知", 1),

    // 短信通知
    SMS("sms", "短信通知", 2),

    // 站内信通知
    INTERNAL("internal", "站内信", 3),

    // 微信通知
    WECHAT("wechat", "微信通知", 4),

    // 钉钉通知
    DINGTALK("dingtalk", "钉钉通知", 5),

    // WebSocket实时通知
    WEBSOCKET("websocket", "WebSocket推送", 6),

    // 移动端推送通知
    PUSH("push", "移动端推送通知", 7),

    // 语音通知
    VOICE("voice", "语音电话通知", 8),

    // 飞书通知
    FEISHU("feishu", "飞书通知", 9),

    // 系统通知
    SYSTEM("system", "系统通知", 10);

    /**
     * 渠道编码
     */
    private final String code;

    /**
     * 渠道名称
     */
    private final String name;

    /**
     * 默认优先级
     */
    private final int defaultPriority;

    /**
     * 根据代码获取枚举
     */
    public static ChannelType fromCode(String code) {
        for (ChannelType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的渠道类型: " + code);
    }


    /**
     * 判断是否为实时通知类型
     */
    public boolean isRealTime() {
        return this == WECHAT || this == DINGTALK || this == FEISHU || this == WEBSOCKET || this == PUSH || this == INTERNAL;
    }

    /**
     * 判断是否需要外部服务
     */
    public boolean needsExternalService() {
        return this == EMAIL || this == SMS || this == WECHAT || this == DINGTALK ||
               this == FEISHU || this == VOICE || this == PUSH;
    }

    /**
     * 判断是否支持富文本
     */
    public boolean supportsRichText() {
        return this == EMAIL || this == INTERNAL || this == WECHAT || this == DINGTALK || this == FEISHU;
    }

    /**
     * 判断是否支持附件
     */
    public boolean supportsAttachment() {
        return this == EMAIL || this == INTERNAL;
    }
}
