package com.hzwangda.edu.notification.factory;


import com.hzwangda.edu.notification.adapter.NotificationChannelAdapter;
import com.hzwangda.edu.notification.enums.ChannelType;

import java.util.List;

/**
 * 通知渠道工厂接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public interface NotificationChannelFactory {

    /**
     * 创建通知渠道
     *
     * @param type 渠道类型
     * @return 通知渠道
     */
    NotificationChannelAdapter createChannel(ChannelType type);

    /**
     * 获取所有可用的渠道
     *
     * @return 可用渠道列表
     */
    List<NotificationChannelAdapter> getAvailableChannels();

    /**
     * 获取指定类型的渠道
     *
     * @param types 渠道类型列表
     * @return 渠道列表
     */
    List<NotificationChannelAdapter> getChannels(List<ChannelType> types);

    /**
     * 检查渠道是否支持
     *
     * @param type 渠道类型
     * @return 是否支持
     */
    boolean isSupported(ChannelType type);

    /**
     * 注册渠道
     *
     * @param channel 通知渠道
     */
    void registerChannel(NotificationChannelAdapter channel);

    /**
     * 注销渠道
     *
     * @param type 渠道类型
     */
    void unregisterChannel(ChannelType type);

    /**
     * 获取默认渠道
     *
     * @return 默认渠道
     */
    NotificationChannelAdapter getDefaultChannel();

}
