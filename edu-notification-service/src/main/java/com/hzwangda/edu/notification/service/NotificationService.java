package com.hzwangda.edu.notification.service;

import com.hzwangda.edu.notification.dto.*;
import com.hzwangda.edu.common.dto.PageResult;
import com.hzwangda.edu.notification.dto.NotificationMessageResponse;
import com.hzwangda.edu.notification.dto.NotificationSendRequest;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 消息通知服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface NotificationService {

    /**
     * 发送单个消息
     *
     * @param request 消息发送请求
     * @return 消息响应
     */
    NotificationMessageResponse sendMessage(NotificationSendRequest request);

    /**
     * 批量发送消息
     *
     * @param requests 消息发送请求列表
     * @return 发送结果列表
     */
    List<NotificationMessageResponse> batchSendMessages(List<NotificationSendRequest> requests);

    /**
     * 异步发送消息
     *
     * @param request 消息发送请求
     * @return 消息ID
     */
    String sendMessageAsync(NotificationSendRequest request);

    /**
     * 定时发送消息
     *
     * @param request 消息发送请求
     * @param scheduledTime 计划发送时间
     * @return 消息响应
     */
    NotificationMessageResponse scheduleMessage(NotificationSendRequest request, LocalDateTime scheduledTime);

    /**
     * 根据模板发送消息
     *
     * @param templateCode 模板编码
     * @param channelType 通知渠道
     * @param recipients 接收人列表
     * @param variables 模板变量
     * @return 消息响应列表
     */
    List<NotificationMessageResponse> sendByTemplate(String templateCode, String channelType,
                                                     List<String> recipients, Map<String, Object> variables);

    /**
     * 取消消息发送
     *
     * @param messageId 消息ID
     * @return 是否成功
     */
    Boolean cancelMessage(String messageId);

    /**
     * 重试发送消息
     *
     * @param messageId 消息ID
     * @return 消息响应
     */
    NotificationMessageResponse retryMessage(String messageId);

    /**
     * 根据ID查询消息
     *
     * @param messageId 消息ID
     * @return 消息响应
     */
    NotificationMessageResponse getMessage(String messageId);

    /**
     * 分页查询消息
     *
     * @param recipient 接收人
     * @param channelType 通知渠道
     * @param status 消息状态
     * @param page 页码
     * @param size 每页大小
     * @return 分页消息列表
     */
    PageResult<NotificationMessageResponse> getMessages(String recipient, String channelType, String status,
                                                        Integer page, Integer size);

    /**
     * 查询用户的未读消息
     *
     * @param recipientId 接收人ID
     * @return 未读消息列表
     */
    List<NotificationMessageResponse> getUnreadMessages(Long recipientId);

    /**
     * 查询用户的未读消息数量
     *
     * @param recipientId 接收人ID
     * @return 未读消息数量
     */
    Long getUnreadMessageCount(Long recipientId);

    /**
     * 标记消息为已读
     *
     * @param messageId 消息ID
     * @param recipientId 接收人ID
     * @return 是否成功
     */
    Boolean markAsRead(String messageId, Long recipientId);

    /**
     * 批量标记消息为已读
     *
     * @param messageIds 消息ID列表
     * @param recipientId 接收人ID
     * @return 成功数量
     */
    Integer batchMarkAsRead(List<String> messageIds, Long recipientId);

    /**
     * 查询业务相关消息
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 消息列表
     */
    List<NotificationMessageResponse> getBusinessMessages(String businessType, String businessId);

    /**
     * 获取消息统计信息
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    Map<String, Object> getMessageStatistics(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 处理待发送消息
     */
    void processPendingMessages();

    /**
     * 处理重试消息
     */
    void processRetryMessages();

    /**
     * 清理过期消息
     */
    void cleanupExpiredMessages();

    /**
     * 发送工作流通知
     *
     * @param processInstanceId 流程实例ID
     * @param taskId 任务ID
     * @param eventType 事件类型
     * @param variables 变量
     */
    void sendWorkflowNotification(String processInstanceId, String taskId, String eventType, Map<String, Object> variables);

    /**
     * 发送系统通知
     *
     * @param title 标题
     * @param content 内容
     * @param recipients 接收人列表
     * @param channelTypes 通知渠道列表
     */
    void sendSystemNotification(String title, String content, List<String> recipients, List<String> channelTypes);
}
