package com.hzwangda.edu.notification.entity;

import com.hzwangda.edu.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 消息模板实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "notification_template", indexes = {
    @Index(name = "idx_nt_template_code", columnList = "template_code"),
    @Index(name = "idx_nt_template_type", columnList = "template_type"),
    @Index(name = "idx_nt_channel_type", columnList = "channel_type"),
    @Index(name = "idx_nt_status", columnList = "status")
})
@Schema(description = "消息模板")
public class NotificationTemplate extends BaseEntity {

    @NotBlank(message = "模板编码不能为空")
    @Size(max = 64, message = "模板编码长度不能超过64个字符")
    @Column(name = "template_code", length = 64, nullable = false, unique = true)
    @Schema(description = "模板编码", example = "LEAVE_APPROVAL_NOTIFY")
    private String templateCode;

    @NotBlank(message = "模板名称不能为空")
    @Size(max = 128, message = "模板名称长度不能超过128个字符")
    @Column(name = "template_name", length = 128, nullable = false)
    @Schema(description = "模板名称", example = "请假审批通知")
    private String templateName;

    @NotBlank(message = "模板类型不能为空")
    @Size(max = 32, message = "模板类型长度不能超过32个字符")
    @Column(name = "template_type", length = 32, nullable = false)
    @Schema(description = "模板类型", example = "WORKFLOW")
    private String templateType;

    @NotBlank(message = "通知渠道不能为空")
    @Size(max = 32, message = "通知渠道长度不能超过32个字符")
    @Column(name = "channel_type", length = 32, nullable = false)
    @Schema(description = "通知渠道", example = "EMAIL")
    private String channelType;

    @Size(max = 255, message = "模板标题长度不能超过255个字符")
    @Column(name = "template_title", length = 255)
    @Schema(description = "模板标题", example = "【业务系统】请假审批通知")
    private String templateTitle;

    @Column(name = "template_content", columnDefinition = "TEXT")
    @Schema(description = "模板内容")
    private String templateContent;

    @Size(max = 500, message = "模板描述长度不能超过500个字符")
    @Column(name = "template_description", length = 500)
    @Schema(description = "模板描述")
    private String templateDescription;

    @Column(name = "template_variables", columnDefinition = "TEXT")
    @Schema(description = "模板变量JSON")
    private String templateVariables;

    @Size(max = 20, message = "状态长度不能超过20个字符")
    @Column(name = "status", length = 20, nullable = false)
    @Schema(description = "状态", example = "ACTIVE")
    private String status = "ACTIVE";

    @Column(name = "priority")
    @Schema(description = "优先级", example = "50")
    private Integer priority = 50;

    @Column(name = "is_default", nullable = false)
    @Schema(description = "是否默认模板", example = "false")
    private Boolean isDefault = false;

    @Column(name = "sort_order")
    @Schema(description = "排序顺序", example = "1")
    private Integer sortOrder = 0;

    // 构造函数
    public NotificationTemplate() {
    }

    public NotificationTemplate(String templateCode, String templateName, String templateType, String channelType) {
        this.templateCode = templateCode;
        this.templateName = templateName;
        this.templateType = templateType;
        this.channelType = channelType;
    }

    // Getter and Setter methods
    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getTemplateType() {
        return templateType;
    }

    public void setTemplateType(String templateType) {
        this.templateType = templateType;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getTemplateTitle() {
        return templateTitle;
    }

    public void setTemplateTitle(String templateTitle) {
        this.templateTitle = templateTitle;
    }

    public String getTemplateContent() {
        return templateContent;
    }

    public void setTemplateContent(String templateContent) {
        this.templateContent = templateContent;
    }

    public String getTemplateDescription() {
        return templateDescription;
    }

    public void setTemplateDescription(String templateDescription) {
        this.templateDescription = templateDescription;
    }

    public String getTemplateVariables() {
        return templateVariables;
    }

    public void setTemplateVariables(String templateVariables) {
        this.templateVariables = templateVariables;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
}
