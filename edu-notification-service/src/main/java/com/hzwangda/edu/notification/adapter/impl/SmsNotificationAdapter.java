package com.hzwangda.edu.notification.adapter.impl;

import com.hzwangda.edu.notification.adapter.NotificationChannelAdapter;
import com.hzwangda.edu.notification.dto.NotificationSendRequest;
import com.hzwangda.edu.notification.dto.NotificationSendResult;
import com.hzwangda.edu.notification.enums.ChannelType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/**
 * SMS短信通知适配器（阿里云短信服务）
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class SmsNotificationAdapter implements NotificationChannelAdapter {

    @Value("${notification.sms.accessKeyId:}")
    private String accessKeyId;

    @Value("${notification.sms.accessKeySecret:}")
    private String accessKeySecret;

    @Value("${notification.sms.signName:杭州望达业务系统}")
    private String signName;

    @Value("${notification.sms.templateCode:}")
    private String defaultTemplateCode;

    @Value("${notification.sms.endpoint:dysmsapi.aliyuncs.com}")
    private String endpoint;

    @Value("${notification.sms.enabled:true}")
    private boolean enabled;

    @Value("${notification.sms.rateLimitPerMinute:100}")
    private int rateLimitPerMinute;

    // 手机号正则表达式
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    // 发送统计
    private final Map<String, Object> statistics = new ConcurrentHashMap<>();

    // 发送记录（用于状态查询）
    private final Map<String, NotificationSendResult> sendRecords = new ConcurrentHashMap<>();

    // 速率限制计数器
    private final Map<String, Integer> rateLimitCounter = new ConcurrentHashMap<>();

    private String lastError;

    @Override
    public ChannelType getSupportedChannelType() {
        return ChannelType.SMS;
    }

    @Override
    public boolean isAvailable() {
        return enabled &&
               accessKeyId != null && !accessKeyId.isEmpty() &&
               accessKeySecret != null && !accessKeySecret.isEmpty();
    }

    @Override
    public NotificationSendResult sendMessage(NotificationSendRequest request) {
        log.info("发送SMS消息: recipient={}, content={}", request.getRecipient(), request.getMessageContent());

        try {
            // 验证
            if (!validateRequest(request)) {
                return createFailureResult(request, "无效的手机号格式");
            }

            // 检查速率限制
            if (!checkRateLimit(request.getRecipient())) {
                return createFailureResult(request, "发送频率超限");
            }

            // 构建发送参数
            Map<String, Object> params = buildSendParams(request);

            // 调用阿里云SMS API
            NotificationSendResult result = callAliyunSmsApi(params);

            // 记录发送结果
            sendRecords.put(result.getMessageId(), result);
            updateStatistics(result);

            return result;

        } catch (Exception e) {
            log.error("SMS发送失败", e);
            lastError = e.getMessage();
            return createFailureResult(request, "SMS发送异常: " + e.getMessage());
        }
    }

    @Override
    public List<NotificationSendResult> batchSendMessages(List<NotificationSendRequest> requests) {
        List<NotificationSendResult> results = new ArrayList<>();

        for (NotificationSendRequest request : requests) {
            results.add(sendMessage(request));
        }

        return results;
    }

    @Override
    public boolean validateRequest(NotificationSendRequest notificationSendRequest) {
        return notificationSendRequest != null && PHONE_PATTERN.matcher(notificationSendRequest.getRecipient()).matches();
    }

    @Override
    public Map<String, Object> getAdapterConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("accessKeyId", accessKeyId);
        config.put("signName", signName);
        config.put("defaultTemplateCode", defaultTemplateCode);
        config.put("endpoint", endpoint);
        config.put("enabled", enabled);
        config.put("rateLimitPerMinute", rateLimitPerMinute);
        return config;
    }

    @Override
    public void updateAdapterConfig(Map<String, Object> config) {
        if (config.containsKey("signName")) {
            this.signName = (String) config.get("signName");
        }
        if (config.containsKey("defaultTemplateCode")) {
            this.defaultTemplateCode = (String) config.get("defaultTemplateCode");
        }
        if (config.containsKey("enabled")) {
            this.enabled = (Boolean) config.get("enabled");
        }
        if (config.containsKey("rateLimitPerMinute")) {
            this.rateLimitPerMinute = (Integer) config.get("rateLimitPerMinute");
        }
    }

    @Override
    public String getMessageStatus(String messageId) {
        NotificationSendResult result = sendRecords.get(messageId);
        return result != null ? result.getErrorCode() : "UNKNOWN";
    }

    @Override
    public Map<String, Object> getStatistics() {
        return new HashMap<>(statistics);
    }

    @Override
    public boolean testConnection() {
        try {
            // 这里可以调用阿里云SMS的测试接口
            return isAvailable();
        } catch (Exception e) {
            log.error("SMS连接测试失败", e);
            lastError = e.getMessage();
            return false;
        }
    }

    @Override
    public String getLastError() {
        return lastError;
    }

    @Override
    public void reset() {
        statistics.clear();
        sendRecords.clear();
        rateLimitCounter.clear();
        lastError = null;
    }

    // ==================== 私有方法 ====================

    /**
     * 检查发送速率限制
     */
    private boolean checkRateLimit(String recipient) {
        String key = getCurrentMinuteKey();
        int currentCount = rateLimitCounter.getOrDefault(key, 0);

        if (currentCount >= rateLimitPerMinute) {
            return false;
        }

        rateLimitCounter.put(key, currentCount + 1);
        return true;
    }

    /**
     * 获取当前分钟的键
     */
    private String getCurrentMinuteKey() {
        LocalDateTime now = LocalDateTime.now();
        return String.format("%04d%02d%02d%02d%02d",
            now.getYear(), now.getMonthValue(), now.getDayOfMonth(),
            now.getHour(), now.getMinute());
    }

    /**
     * 构建发送参数
     */
    private Map<String, Object> buildSendParams(NotificationSendRequest request) {
        Map<String, Object> params = new HashMap<>();
        params.put("PhoneNumbers", request.getRecipient());
        params.put("SignName", signName);
        params.put("TemplateCode", getTemplateCode(request));
        params.put("TemplateParam", buildTemplateParam(request));
        return params;
    }

    /**
     * 获取模板代码
     */
    private String getTemplateCode(NotificationSendRequest request) {
        // 优先使用请求中的模板代码
        if (request.getTemplateCode() != null && !request.getTemplateCode().isEmpty()) {
            return request.getTemplateCode();
        }
        return defaultTemplateCode;
    }

    /**
     * 构建模板参数
     */
    private String buildTemplateParam(NotificationSendRequest request) {
        Map<String, Object> templateParam = new HashMap<>();

        // 如果有模板变量，使用模板变量
        if (request.getVariables() != null && !request.getVariables().isEmpty()) {
            templateParam.putAll(request.getVariables());
        } else {
            // 否则使用内容作为默认参数
            templateParam.put("content", request.getMessageContent());
        }

        // 转换为JSON字符串
        return convertToJson(templateParam);
    }

    /**
     * 调用阿里云SMS API
     */
    private NotificationSendResult callAliyunSmsApi(Map<String, Object> params) {
        // 这里应该调用真实的阿里云SMS API
        // 为了演示，这里模拟API调用

        String messageId = UUID.randomUUID().toString();
        boolean success = Math.random() > 0.1; // 90%成功率模拟

        NotificationSendResult result = new NotificationSendResult();
        result.setMessageId(messageId);
        result.setChannelType(ChannelType.SMS.getCode());
        result.setRecipient((String) params.get("PhoneNumbers"));
        result.setSuccess(success);
        result.setErrorCode(success ? "SENT" : "FAILED");
        result.setSendTime(LocalDateTime.now());

        if (!success) {
            result.setErrorMessage("模拟发送失败");
        }

        return result;
    }

    /**
     * 创建失败结果
     */
    private NotificationSendResult createFailureResult(NotificationSendRequest request, String errorMessage) {
        NotificationSendResult result = new NotificationSendResult();
        result.setMessageId(UUID.randomUUID().toString());
        result.setChannelType(ChannelType.SMS.getCode());
        result.setRecipient(request.getRecipient());
        result.setSuccess(false);
        result.setErrorCode("FAILED");
        result.setErrorMessage(errorMessage);
        result.setSendTime(LocalDateTime.now());
        return result;
    }

    /**
     * 更新统计信息
     */
    private void updateStatistics(NotificationSendResult result) {
        String today = LocalDateTime.now().toLocalDate().toString();

        // 总发送数
        statistics.merge("totalSent", 1, (old, val) -> (Integer) old + (Integer) val);

        // 今日发送数
        statistics.merge("todaySent_" + today, 1, (old, val) -> (Integer) old + (Integer) val);

        // 成功数
        if (result.isSuccess()) {
            statistics.merge("successCount", 1, (old, val) -> (Integer) old + (Integer) val);
        } else {
            statistics.merge("failureCount", 1, (old, val) -> (Integer) old + (Integer) val);
        }

        // 最后发送时间
        statistics.put("lastSendTime", result.getSendTime());
    }

    /**
     * 转换为JSON字符串
     */
    private String convertToJson(Map<String, Object> map) {
        // 简单的JSON转换，实际项目中应该使用Jackson或Gson
        StringBuilder json = new StringBuilder("{");
        boolean first = true;
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (!first) {
                json.append(",");
            }
            json.append("\"").append(entry.getKey()).append("\":\"")
                .append(entry.getValue()).append("\"");
            first = false;
        }
        json.append("}");
        return json.toString();
    }
}
