package com.hzwangda.edu.notification.adapter;

import com.hzwangda.edu.notification.dto.NotificationSendRequest;
import com.hzwangda.edu.notification.dto.NotificationSendResult;
import com.hzwangda.edu.notification.enums.ChannelType;

import java.util.List;
import java.util.Map;

/**
 * 通知渠道适配器接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface NotificationChannelAdapter {

    /**
     * 获取支持的渠道类型
     *
     * @return 渠道类型
     */
    ChannelType getSupportedChannelType();

    /**
     * 检查适配器是否可用
     *
     * @return 是否可用
     */
    boolean isAvailable();

    /**
     * 发送单条消息
     *
     * @param request 发送请求
     * @return 发送结果
     */
    NotificationSendResult sendMessage(NotificationSendRequest request);

    /**
     * 批量发送消息
     *
     * @param requests 发送请求列表
     * @return 发送结果列表
     */
    List<NotificationSendResult> batchSendMessages(List<NotificationSendRequest> requests);

    /**
     * 获取发送状态
     *
     * @param messageId 消息ID
     * @return 发送状态
     */
    String getMessageStatus(String messageId);

    /**
     * 验证请求参数
     *
     * @param request 通知请求
     * @return 验证结果
     */
    default boolean validateRequest(NotificationSendRequest request) {
        return request != null &&
                request.getRecipients() != null &&
                !request.getRecipients().isEmpty();
    }

    /**
     * 获取适配器配置
     *
     * @return 配置信息
     */
    Map<String, Object> getAdapterConfig();

    /**
     * 更新适配器配置
     *
     * @param config 配置信息
     */
    void updateAdapterConfig(Map<String, Object> config);

    /**
     * 获取适配器统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getStatistics();

    /**
     * 测试连接
     *
     * @return 测试结果
     */
    boolean testConnection();

    /**
     * 获取错误信息
     *
     * @return 错误信息
     */
    String getLastError();

    /**
     * 重置适配器状态
     */
    void reset();
}
