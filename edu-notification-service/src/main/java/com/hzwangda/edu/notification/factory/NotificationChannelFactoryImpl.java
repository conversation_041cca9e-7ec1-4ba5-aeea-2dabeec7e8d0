package com.hzwangda.edu.notification.factory;

import com.hzwangda.edu.notification.adapter.NotificationChannelAdapter;
import com.hzwangda.edu.notification.enums.ChannelType;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 通知渠道工厂实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@Component
public class NotificationChannelFactoryImpl implements NotificationChannelFactory {

    /**
     * 渠道注册表
     */
    private final Map<ChannelType, NotificationChannelAdapter> channelRegistry = new ConcurrentHashMap<>();

    /**
     * 自动注入所有通知渠道实现
     */
    @Autowired
    private List<NotificationChannelAdapter> channels;

    /**
     * 初始化渠道注册表
     */
    @PostConstruct
    public void initChannels() {
        if (channels != null) {
            for (NotificationChannelAdapter channel : channels) {
                registerChannel(channel);
            }
        }
        log.info("通知渠道工厂初始化完成，注册渠道数量: {}", channelRegistry.size());
    }

    @Override
    public NotificationChannelAdapter createChannel(ChannelType type) {
        NotificationChannelAdapter channel = channelRegistry.get(type);
        if (channel == null) {
            throw new IllegalArgumentException("不支持的通知渠道类型: " + type);
        }
        if (!channel.isAvailable()) {
            throw new RuntimeException("通知渠道不可用: " + type);
        }
        return channel;
    }

    @Override
    public List<NotificationChannelAdapter> getAvailableChannels() {
        return channelRegistry.values().stream()
                .filter(NotificationChannelAdapter::isAvailable)
                .collect(Collectors.toList());
    }

    @Override
    public List<NotificationChannelAdapter> getChannels(List<ChannelType> types) {
        return types.stream()
                .map(channelRegistry::get)
                .filter(Objects::nonNull)
                .filter(NotificationChannelAdapter::isAvailable)
                .collect(Collectors.toList());
    }

    @Override
    public boolean isSupported(ChannelType type) {
        return channelRegistry.containsKey(type);
    }

    @Override
    public void registerChannel(NotificationChannelAdapter channel) {
        if (channel != null) {
            channelRegistry.put(channel.getSupportedChannelType(), channel);
            log.info("注册通知渠道: {}", channel.getSupportedChannelType());
        }
    }

    @Override
    public void unregisterChannel(ChannelType type) {
        NotificationChannelAdapter removed = channelRegistry.remove(type);
        if (removed != null) {
            log.info("注销通知渠道: {}", type);
        }
    }

    @Override
    public NotificationChannelAdapter getDefaultChannel() {
        // 默认使用站内信作为默认渠道
        NotificationChannelAdapter defaultChannel = channelRegistry.get(ChannelType.INTERNAL);
        if (defaultChannel != null && defaultChannel.isAvailable()) {
            return defaultChannel;
        }

        // 如果站内信不可用，返回第一个可用的渠道
        return getAvailableChannels().stream()
                .findFirst()
                .orElseThrow(() -> new RuntimeException("没有可用的通知渠道"));
    }

    /**
     * 获取渠道统计信息
     *
     * @return 统计信息
     */
    public Map<String, Object> getChannelStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalChannels", channelRegistry.size());
        stats.put("availableChannels", getAvailableChannels().size());

        Map<String, Boolean> channelStatus = new HashMap<>();
        channelRegistry.forEach((type, channel) ->
            channelStatus.put(type.getCode(), channel.isAvailable()));
        stats.put("channelStatus", channelStatus);

        return stats;
    }
}
