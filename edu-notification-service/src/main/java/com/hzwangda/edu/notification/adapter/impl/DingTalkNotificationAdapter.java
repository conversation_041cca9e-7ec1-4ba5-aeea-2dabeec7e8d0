package com.hzwangda.edu.notification.adapter.impl;

import com.hzwangda.edu.notification.adapter.NotificationChannelAdapter;
import com.hzwangda.edu.notification.dto.NotificationSendRequest;
import com.hzwangda.edu.notification.dto.NotificationSendResult;
import com.hzwangda.edu.notification.enums.ChannelType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 钉钉工作通知适配器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class DingTalkNotificationAdapter implements NotificationChannelAdapter {

    @Value("${notification.dingtalk.appKey:}")
    private String appKey;

    @Value("${notification.dingtalk.appSecret:}")
    private String appSecret;

    @Value("${notification.dingtalk.agentId:}")
    private String agentId;

    @Value("${notification.dingtalk.apiUrl:https://oapi.dingtalk.com}")
    private String apiUrl;

    @Value("${notification.dingtalk.enabled:true}")
    private boolean enabled;

    @Value("${notification.dingtalk.tokenExpireMinutes:110}")
    private int tokenExpireMinutes;

    // Access Token缓存
    private String accessToken;
    private LocalDateTime tokenExpireTime;

    // 发送统计
    private final Map<String, Object> statistics = new ConcurrentHashMap<>();

    // 发送记录
    private final Map<String, NotificationSendResult> sendRecords = new ConcurrentHashMap<>();

    private String lastError;

    @Override
    public ChannelType getSupportedChannelType() {
        return ChannelType.DINGTALK;
    }

    @Override
    public boolean isAvailable() {
        return enabled &&
               appKey != null && !appKey.isEmpty() &&
               appSecret != null && !appSecret.isEmpty() &&
               agentId != null && !agentId.isEmpty();
    }

    @Override
    public NotificationSendResult sendMessage(NotificationSendRequest request) {
        log.info("发送钉钉工作通知: recipient={}, content={}", request.getRecipient(), request.getMessageContent());

        try {
            // 验证接收人
            if (!validateRequest(request)) {
                return createFailureResult(request, "无效的钉钉用户ID格式");
            }

            // 获取Access Token
            String token = getAccessToken();
            if (token == null) {
                return createFailureResult(request, "获取钉钉Access Token失败");
            }

            // 构建消息体
            Map<String, Object> messageBody = buildMessageBody(request);

            // 发送消息
            NotificationSendResult result = sendDingTalkMessage(token, messageBody);

            // 记录发送结果
            sendRecords.put(result.getMessageId(), result);
            updateStatistics(result);

            return result;

        } catch (Exception e) {
            log.error("钉钉工作通知发送失败", e);
            lastError = e.getMessage();
            return createFailureResult(request, "钉钉消息发送异常: " + e.getMessage());
        }
    }

    @Override
    public List<NotificationSendResult> batchSendMessages(List<NotificationSendRequest> requests) {
        List<NotificationSendResult> results = new ArrayList<>();

        // 钉钉支持批量发送
        if (requests.size() > 1) {
            return batchSendDingTalkMessages(requests);
        }

        for (NotificationSendRequest request : requests) {
            results.add(sendMessage(request));
        }

        return results;
    }

    @Override
    public Map<String, Object> getAdapterConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("appKey", appKey);
        config.put("agentId", agentId);
        config.put("apiUrl", apiUrl);
        config.put("enabled", enabled);
        config.put("tokenExpireMinutes", tokenExpireMinutes);
        return config;
    }

    @Override
    public void updateAdapterConfig(Map<String, Object> config) {
        if (config.containsKey("agentId")) {
            this.agentId = (String) config.get("agentId");
        }
        if (config.containsKey("enabled")) {
            this.enabled = (Boolean) config.get("enabled");
        }
        if (config.containsKey("tokenExpireMinutes")) {
            this.tokenExpireMinutes = (Integer) config.get("tokenExpireMinutes");
        }
    }

    @Override
    public String getMessageStatus(String messageId) {
        NotificationSendResult result = sendRecords.get(messageId);
        return result != null ? result.getErrorCode() : "UNKNOWN";
    }

    @Override
    public Map<String, Object> getStatistics() {
        return new HashMap<>(statistics);
    }

    @Override
    public boolean testConnection() {
        try {
            String token = getAccessToken();
            return token != null && !token.isEmpty();
        } catch (Exception e) {
            log.error("钉钉连接测试失败", e);
            lastError = e.getMessage();
            return false;
        }
    }

    @Override
    public String getLastError() {
        return lastError;
    }

    @Override
    public void reset() {
        statistics.clear();
        sendRecords.clear();
        accessToken = null;
        tokenExpireTime = null;
        lastError = null;
    }

    // ==================== 私有方法 ====================

    /**
     * 获取Access Token
     */
    private String getAccessToken() {
        // 检查token是否过期
        if (accessToken != null && tokenExpireTime != null &&
            LocalDateTime.now().isBefore(tokenExpireTime)) {
            return accessToken;
        }

        try {
            // 调用钉钉API获取token
            Map<String, Object> tokenResponse = callDingTalkTokenApi();

            if (tokenResponse.containsKey("access_token")) {
                accessToken = (String) tokenResponse.get("access_token");
                tokenExpireTime = LocalDateTime.now().plusMinutes(tokenExpireMinutes);
                return accessToken;
            } else {
                lastError = "获取Access Token失败: " + tokenResponse.get("errmsg");
                return null;
            }

        } catch (Exception e) {
            log.error("获取钉钉Access Token异常", e);
            lastError = e.getMessage();
            return null;
        }
    }

    /**
     * 调用钉钉Token API
     */
    private Map<String, Object> callDingTalkTokenApi() {
        // 模拟钉钉API调用
        Map<String, Object> response = new HashMap<>();

        if (Math.random() > 0.1) { // 90%成功率
            response.put("errcode", 0);
            response.put("errmsg", "ok");
            response.put("access_token", "mock_dingtalk_token_" + System.currentTimeMillis());
            response.put("expires_in", 7200);
        } else {
            response.put("errcode", 40001);
            response.put("errmsg", "invalid credential");
        }

        return response;
    }

    /**
     * 构建消息体
     */
    private Map<String, Object> buildMessageBody(NotificationSendRequest request) {
        Map<String, Object> messageBody = new HashMap<>();
        messageBody.put("agent_id", agentId);
        messageBody.put("userid_list", request.getRecipient());

        // 构建消息内容
        Map<String, Object> msg = new HashMap<>();
        msg.put("msgtype", "text");

        // 文本消息
        Map<String, Object> text = new HashMap<>();
        String content = request.getMessageContent();

        // 如果有标题，添加到内容前面
        if (request.getMessageTitle() != null && !request.getMessageTitle().isEmpty()) {
            content = "【" + request.getMessageTitle() + "】\n" + content;
        }

        text.put("content", content);
        msg.put("text", text);

        messageBody.put("msg", msg);

        return messageBody;
    }

    /**
     * 发送钉钉消息
     */
    private NotificationSendResult sendDingTalkMessage(String token, Map<String, Object> messageBody) {
        // 模拟钉钉API调用
        String messageId = UUID.randomUUID().toString();
        boolean success = Math.random() > 0.05; // 95%成功率

        NotificationSendResult result = new NotificationSendResult();
        result.setMessageId(messageId);
        result.setChannelType(ChannelType.DINGTALK.getCode());
        result.setRecipient((String) messageBody.get("userid_list"));
        result.setSuccess(success);
        result.setErrorCode(success ? "SENT" : "FAILED");
        result.setSendTime(LocalDateTime.now());

        if (!success) {
            result.setErrorMessage("模拟钉钉发送失败");
        }

        return result;
    }

    /**
     * 批量发送钉钉消息
     */
    private List<NotificationSendResult> batchSendDingTalkMessages(List<NotificationSendRequest> requests) {
        List<NotificationSendResult> results = new ArrayList<>();

        try {
            String token = getAccessToken();
            if (token == null) {
                // 如果获取token失败，所有消息都失败
                for (NotificationSendRequest request : requests) {
                    results.add(createFailureResult(request, "获取钉钉Access Token失败"));
                }
                return results;
            }

            // 构建批量消息体
            Map<String, Object> batchMessageBody = buildBatchMessageBody(requests);

            // 发送批量消息
            Map<String, Object> batchResult = sendBatchDingTalkMessage(token, batchMessageBody);

            // 解析批量结果
            results = parseBatchResult(requests, batchResult);

        } catch (Exception e) {
            log.error("批量发送钉钉消息失败", e);
            for (NotificationSendRequest request : requests) {
                results.add(createFailureResult(request, "批量发送异常: " + e.getMessage()));
            }
        }

        return results;
    }

    /**
     * 构建批量消息体
     */
    private Map<String, Object> buildBatchMessageBody(List<NotificationSendRequest> requests) {
        Map<String, Object> messageBody = new HashMap<>();
        messageBody.put("agent_id", agentId);

        // 收集所有接收人
        List<String> userIds = new ArrayList<>();
        for (NotificationSendRequest request : requests) {
            userIds.add(request.getRecipient());
        }

        messageBody.put("userid_list", String.join(",", userIds));

        // 构建消息内容（使用第一个请求的内容）
        if (!requests.isEmpty()) {
            NotificationSendRequest firstRequest = requests.get(0);
            Map<String, Object> msg = new HashMap<>();
            msg.put("msgtype", "text");

            Map<String, Object> text = new HashMap<>();
            String content = firstRequest.getMessageContent();

            if (firstRequest.getMessageTitle() != null && !firstRequest.getMessageTitle().isEmpty()) {
                content = "【" + firstRequest.getMessageTitle() + "】\n" + content;
            }

            text.put("content", content);
            msg.put("text", text);
            messageBody.put("msg", msg);
        }

        return messageBody;
    }

    /**
     * 发送批量钉钉消息
     */
    private Map<String, Object> sendBatchDingTalkMessage(String token, Map<String, Object> messageBody) {
        // 模拟批量发送API调用
        Map<String, Object> response = new HashMap<>();
        response.put("errcode", 0);
        response.put("errmsg", "ok");
        response.put("task_id", UUID.randomUUID().toString());
        return response;
    }

    /**
     * 解析批量结果
     */
    private List<NotificationSendResult> parseBatchResult(
            List<NotificationSendRequest> requests, Map<String, Object> batchResult) {

        List<NotificationSendResult> results = new ArrayList<>();
        boolean success = (Integer) batchResult.get("errcode") == 0;
        String taskId = (String) batchResult.get("task_id");

        for (NotificationSendRequest request : requests) {
            NotificationSendResult result = new NotificationSendResult();
            result.setMessageId(taskId + "_" + request.getRecipient());
            result.setChannelType(ChannelType.DINGTALK.getCode());
            result.setRecipient(request.getRecipient());
            result.setSuccess(success);
            result.setErrorCode(success ? "SENT" : "FAILED");
            result.setSendTime(LocalDateTime.now());

            if (!success) {
                result.setErrorMessage((String) batchResult.get("errmsg"));
            }

            results.add(result);

            // 记录发送结果
            sendRecords.put(result.getMessageId(), result);
            updateStatistics(result);
        }

        return results;
    }

    /**
     * 创建失败结果
     */
    private NotificationSendResult createFailureResult(NotificationSendRequest request, String errorMessage) {
        NotificationSendResult result = new NotificationSendResult();
        result.setMessageId(UUID.randomUUID().toString());
        result.setChannelType(ChannelType.DINGTALK.getCode());
        result.setRecipient(request.getRecipient());
        result.setSuccess(false);
        result.setErrorCode("FAILED");
        result.setErrorMessage(errorMessage);
        result.setSendTime(LocalDateTime.now());
        return result;
    }

    /**
     * 更新统计信息
     */
    private void updateStatistics(NotificationSendResult result) {
        String today = LocalDateTime.now().toLocalDate().toString();

        // 总发送数
        statistics.merge("totalSent", 1, (old, val) -> (Integer) old + (Integer) val);

        // 今日发送数
        statistics.merge("todaySent_" + today, 1, (old, val) -> (Integer) old + (Integer) val);

        // 成功数
        if (result.isSuccess()) {
            statistics.merge("successCount", 1, (old, val) -> (Integer) old + (Integer) val);
        } else {
            statistics.merge("failureCount", 1, (old, val) -> (Integer) old + (Integer) val);
        }

        // 最后发送时间
        statistics.put("lastSendTime", result.getSendTime());
    }
}
