# 服务配置
server:
  port: 8005
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true

# Spring配置
spring:
  application:
    name: edu-notification-service
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_SERVER_ADDR:127.0.0.1:8848}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:nacos}
        namespace: ${NACOS_NAMESPACE:hky-hr}

  # 数据源配置
  datasource:
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:************}:${DB_PORT:31252}/${DB_NAME:hky_hr_db}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${DB_USER:sasa}
    password: ${DB_PWD:RApubone95}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariCP-NotificationService
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    show-sql: ${SPRING_JPA_SHOW_SQL:false}
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
        batch_versioned_data: true

  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:31000}
      password: ${REDIS_PWD:sjyt_cywKZHAl}
      database: ${REDIS_DB:6}
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 20
          max-wait: -1ms
          max-idle: 10
          min-idle: 5

  # 邮件配置
  mail:
    host: smtp.hky.edu.cn
    port: 587
    username: <EMAIL>
    password: your_email_password
    personal: 业务管理系统
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          ssl:
            trust: smtp.hky.edu.cn
    default-encoding: UTF-8

  # RabbitMQ配置
  rabbitmq:
    host: http://hky.ec.hzwangda.com/mq/
    port: 5672
    username: wdAdmin
    password: rapubone
    virtual-host: /
    connection-timeout: 15000
    publisher-confirm-type: correlated
    publisher-returns: true
    listener:
      simple:
        acknowledge-mode: manual
        retry:
          enabled: true
          initial-interval: 1000
          max-attempts: 3
          multiplier: 2

  # Jackson配置
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

  # Thymeleaf配置
  thymeleaf:
    mode: HTML
    encoding: UTF-8
    cache: false

  # FreeMarker配置
  freemarker:
    charset: UTF-8
    cache: false
    template-loader-path: classpath:/templates/

# Seata分布式事务配置（暂时禁用）
seata:
  enabled: false

# 日志配置
logging:
  level:
    com.hky.hr.notification: DEBUG
    org.springframework.mail: DEBUG
    org.springframework.amqp: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/notification-service.log

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,tracing
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
  tracing:
    sampling:
      probability: 1.0
  zipkin:
    tracing:
      endpoint: ${ZIPKIN_ENDPOINT:http://localhost:9411/api/v2/spans}

# SpringDoc配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  info:
    title: 业务管理系统 - 消息通知服务 API
    description: 消息通知服务，支持邮件、短信、站内信、微信、钉钉等多种通知方式
    version: 1.0.0
    contact:
      name: HKY-HR-System
      email: <EMAIL>

# 消息通知配置
hky:
  notification:
    # 默认发送配置
    default:
      priority: 50
      max-retry-count: 3
      expire-hours: 72

    # 批量发送配置
    batch:
      size: 100
      delay-seconds: 1

    # 异步处理配置
    async:
      core-pool-size: 5
      max-pool-size: 20
      queue-capacity: 200
      keep-alive-seconds: 60

    # 缓存配置
    cache:
      expire-hours: 2
      max-size: 1000

    # 渠道配置
    channels:
      email:
        enabled: true
        timeout-seconds: 30
        retry-interval-seconds: 60
      sms:
        enabled: false
        provider: aliyun
        timeout-seconds: 10
        retry-interval-seconds: 30
      internal:
        enabled: true
        websocket-enabled: true
      wechat:
        enabled: false
        corp-id: your_corp_id
        corp-secret: your_corp_secret
      dingtalk:
        enabled: false
        app-key: your_app_key
        app-secret: your_app_secret

    # 模板配置
    template:
      cache-enabled: true
      validation-enabled: true
      default-engine: freemarker

    # 统计配置
    statistics:
      enabled: true
      retention-days: 90

# 第三方服务配置
third-party:
  # 阿里云短信配置
  aliyun:
    sms:
      access-key-id: your_access_key_id
      access-key-secret: your_access_key_secret
      region: cn-hangzhou
      sign-name: 杭州望达

  # 企业微信配置
  wechat:
    corp-id: your_corp_id
    corp-secret: your_corp_secret
    agent-id: your_agent_id

  # 钉钉配置
  dingtalk:
    app-key: your_app_key
    app-secret: your_app_secret
