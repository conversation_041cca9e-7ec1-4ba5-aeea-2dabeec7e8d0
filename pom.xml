<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.hzwangda.edu</groupId>
    <artifactId>edu-common-services</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>教育管理通用服务</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>

        <!-- Spring Boot版本 -->
        <spring-boot.version>3.4.7</spring-boot.version>
        <spring-cloud.version>2024.0.1</spring-cloud.version>

        <!-- 服务注册 -->
        <nacos.version>2023.0.3.3</nacos.version>

        <!-- OpenFeign -->
        <openfeign.version>4.3.0</openfeign.version>
        <resilience4j.version>3.3.0</resilience4j.version>

        <!-- 分布式事务 -->
        <seata.version>2.4.0</seata.version>

        <!-- 数据库相关 -->
        <postgresql.version>42.7.7</postgresql.version>

        <!-- 工具库版本 -->
        <hutool.version>5.8.37</hutool.version>
        <fastjson2.version>2.0.57</fastjson2.version>
        <mapstruct.version>1.6.3</mapstruct.version>
        <lombok.version>1.18.38</lombok.version>

        <!-- 工作流引擎 -->
        <flowable.version>7.1.0</flowable.version>

        <!-- 文档和测试 -->
        <springdoc.version>2.8.9</springdoc.version>

        <!-- SonarQube配置 -->
        <sonar.maven.plugin.version>5.1.0.4751</sonar.maven.plugin.version>
        <sonar.projectKey>edu-common-services</sonar.projectKey>
        <sonar.projectName>教育管理系统</sonar.projectName>
        <sonar.projectVersion>${project.version}</sonar.projectVersion>
        <sonar.sourceEncoding>UTF-8</sonar.sourceEncoding>
        <sonar.java.source>17</sonar.java.source>
        <sonar.coverage.jacoco.xmlReportPaths>${project.basedir}/target/site/jacoco/jacoco.xml</sonar.coverage.jacoco.xmlReportPaths>
        <sonar.junit.reportPaths>${project.basedir}/target/surefire-reports</sonar.junit.reportPaths>

        <!-- 代码质量工具版本 -->
        <jacoco.version>0.8.13</jacoco.version>
        <checkstyle.version>3.6.0</checkstyle.version>
        <spotbugs.version>*******</spotbugs.version>

        <!-- 链路追踪版本 -->
        <micrometer-tracing.version>1.5.1</micrometer-tracing.version>
        <zipkin-reporter.version>3.5.1</zipkin-reporter.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring Cloud BOM -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- nacos注册中心 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>${nacos.version}</version>
            </dependency>

            <!-- OpenFeign -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>${openfeign.version}</version>
            </dependency>

            <!-- 熔断器 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-circuitbreaker-resilience4j</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>

            <!-- 链路追踪 - Micrometer Tracing -->
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-tracing-bridge-brave</artifactId>
                <version>${micrometer-tracing.version}</version>
            </dependency>

            <!-- Zipkin Reporter -->
            <dependency>
                <groupId>io.zipkin.reporter2</groupId>
                <artifactId>zipkin-reporter-brave</artifactId>
                <version>${zipkin-reporter.version}</version>
            </dependency>

            <!-- 数据库驱动 -->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>

            <!-- 工具库 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <!-- MapStruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <!-- Lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <optional>true</optional>
            </dependency>

            <!-- Flowable工作流 -->
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter</artifactId>
                <version>${flowable.version}</version>
            </dependency>

            <!-- Seata分布式事务 -->
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-spring-boot-starter</artifactId>
                <version>${seata.version}</version>
            </dependency>

            <!-- API文档 -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>HzWangdaRepo</id>
            <url>https://mvnrepository.hzwangda.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>HzWangdaRepo</id>
            <url>https://mvnrepository.hzwangda.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>