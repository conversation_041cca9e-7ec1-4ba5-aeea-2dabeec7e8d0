# 服务配置
server:
  port: 8006
  servlet:
    context-path: /workflow-service
    encoding:
      charset: UTF-8
      enabled: true
      force: true

# Spring配置
spring:
  application:
    name: edu-workflow-service
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_SERVER_ADDR:127.0.0.1:8848}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:nacos}
        namespace: ${NACOS_NAMESPACE:hky-hr}
  main:
    allow-bean-definition-overriding: true

  # 数据源配置
  datasource:
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:************}:${DB_PORT:31252}/${DB_NAME:hky_hr_workflow_db}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${DB_USER:sasa}
    password: ${DB_PWD:RApubone95}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariCP-WorkflowService
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    show-sql: ${SPRING_JPA_SHOW_SQL:false}
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
        batch_versioned_data: true

  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:31000}
      password: ${REDIS_PWD:sjyt_cywKZHAl}
      database: ${REDIS_DB:6}
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 20
          max-wait: -1ms
          max-idle: 10
          min-idle: 5

  # Jackson配置
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# Flowable配置
flowable:
  # 数据库配置
  database-schema-update: true
  db-history-used: true
  history-level: full

  # 异步执行器配置
  async-executor-activate: true
  async-executor-default-async-job-acquire-wait-time: 10s
  async-executor-default-timer-job-acquire-wait-time: 10s

  # 邮件配置
  mail-server-host: localhost
  mail-server-port: 25
  mail-server-default-from: <EMAIL>

  # 流程验证配置
  process-definition-cache-limit: 100
  enable-safe-xml: true

  # UI配置
  modeler:
    app:
      deployment-api-url: http://localhost:8006/app-api
  admin:
    app:
      server-config:
        process-engine-name: default
        server-address: http://localhost
        port: 8006
        context-root: /
        rest-root: app-api
  task:
    app:
      server-config:
        process-engine-name: default
        server-address: http://localhost
        port: 8006
        context-root: /
        rest-root: app-api

# 日志配置
logging:
  level:
    com.hky.hr.workflow: DEBUG
    org.flowable: INFO
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/workflow-service.log

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,flowable,tracing
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
  tracing:
    sampling:
      probability: 1.0
  zipkin:
    tracing:
      endpoint: ${ZIPKIN_ENDPOINT:http://localhost:9411/api/v2/spans}

# SpringDoc配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  info:
    title: 业务管理系统 - 工作流服务 API
    description: 基于Flowable 7.1的工作流引擎服务，提供流程建模、执行、任务管理和监控功能
    version: 1.0.0
    contact:
      name: HKY-HR-System
      email: <EMAIL>

# 工作流配置
hky:
  workflow:
    # 流程定义缓存大小
    process-definition-cache-size: 100
    # 任务超时检查间隔（分钟）
    task-timeout-check-interval: 30
    # 流程实例超时时间（小时）
    process-instance-timeout-hours: 72
    # 任务默认超时时间（小时）
    task-default-timeout-hours: 24
    # 是否启用流程图生成
    enable-process-diagram: true
    # 流程图字体
    diagram-font: 宋体
    # 批量处理大小
    batch-size: 100
    # 缓存过期时间（小时）
    cache-expire-hours: 2

# Seata分布式事务配置（暂时禁用）
seata:
  enabled: false