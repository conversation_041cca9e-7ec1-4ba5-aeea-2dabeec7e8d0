package com.hzwangda.edu.workflow.enums;

/**
 * 审批结果枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum ApprovalResult {

    APPROVED("通过", "审批通过"),
    REJECTED("拒绝", "审批拒绝"),
    RETURNED("退回", "退回修改"),
    DELEGATED("委派", "委派给他人处理"),
    TRANSFERRED("转办", "转办给他人处理"),
    CANCELLED("取消", "取消审批"),
    PENDING("待定", "暂时挂起，待进一步确认");

    private final String description;
    private final String detail;

    ApprovalResult(String description, String detail) {
        this.description = description;
        this.detail = detail;
    }

    public String getDescription() {
        return description;
    }

    public String getDetail() {
        return detail;
    }

    public String getCode() {
        return this.name();
    }

    /**
     * 根据代码获取枚举
     */
    public static ApprovalResult fromCode(String code) {
        for (ApprovalResult result : values()) {
            if (result.getCode().equals(code)) {
                return result;
            }
        }
        return PENDING;
    }

    /**
     * 判断是否为最终结果
     */
    public boolean isFinalResult() {
        return this == APPROVED || this == REJECTED || this == CANCELLED;
    }

    /**
     * 判断是否为正面结果
     */
    public boolean isPositiveResult() {
        return this == APPROVED;
    }

    /**
     * 判断是否为负面结果
     */
    public boolean isNegativeResult() {
        return this == REJECTED || this == CANCELLED;
    }

    /**
     * 判断是否需要继续流转
     */
    public boolean needsContinue() {
        return this == DELEGATED || this == TRANSFERRED || this == RETURNED;
    }
}
