package com.hzwangda.edu.workflow.entity;

import com.hzwangda.edu.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 任务信息实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@Entity
@Table(name = "wf_task_info", indexes = {
    @Index(name = "idx_task_id", columnList = "flowable_task_id"),
    @Index(name = "idx_process_instance_id", columnList = "process_instance_id"),
    @Index(name = "idx_assignee", columnList = "assignee_id"),
    @Index(name = "idx_ti_status", columnList = "status"),
    @Index(name = "idx_create_time", columnList = "task_create_time"),
    @Index(name = "idx_due_date", columnList = "due_date")
})
@Schema(description = "任务信息")
public class TaskInfo extends BaseEntity {

    @NotBlank(message = "Flowable任务ID不能为空")
    @Size(max = 64, message = "Flowable任务ID长度不能超过64个字符")
    @Column(name = "flowable_task_id", length = 64, nullable = false, unique = true)
    @Schema(description = "Flowable任务ID")
    private String flowableTaskId;

    @NotBlank(message = "流程实例ID不能为空")
    @Size(max = 64, message = "流程实例ID长度不能超过64个字符")
    @Column(name = "process_instance_id", length = 64, nullable = false)
    @Schema(description = "流程实例ID")
    private String processInstanceId;

    @NotBlank(message = "流程键不能为空")
    @Size(max = 64, message = "流程键长度不能超过64个字符")
    @Column(name = "process_key", length = 64, nullable = false)
    @Schema(description = "流程键", example = "leave_approval")
    private String processKey;

    @NotBlank(message = "任务键不能为空")
    @Size(max = 64, message = "任务键长度不能超过64个字符")
    @Column(name = "task_key", length = 64, nullable = false)
    @Schema(description = "任务键", example = "manager_approval")
    private String taskKey;

    @NotBlank(message = "任务名称不能为空")
    @Size(max = 128, message = "任务名称长度不能超过128个字符")
    @Column(name = "task_name", length = 128, nullable = false)
    @Schema(description = "任务名称", example = "部门经理审批")
    private String taskName;

    @Size(max = 500, message = "任务描述长度不能超过500个字符")
    @Column(name = "task_description", length = 500)
    @Schema(description = "任务描述")
    private String taskDescription;

    @Column(name = "assignee_id")
    @Schema(description = "指派人ID", example = "1")
    private Long assigneeId;

    @Size(max = 64, message = "指派人用户名长度不能超过64个字符")
    @Column(name = "assignee_username", length = 64)
    @Schema(description = "指派人用户名", example = "manager")
    private String assigneeUsername;

    @Size(max = 64, message = "指派人姓名长度不能超过64个字符")
    @Column(name = "assignee_name", length = 64)
    @Schema(description = "指派人姓名", example = "李经理")
    private String assigneeName;

    @Size(max = 64, message = "候选组长度不能超过64个字符")
    @Column(name = "candidate_groups", length = 64)
    @Schema(description = "候选组", example = "dept_manager")
    private String candidateGroups;

    @Size(max = 255, message = "候选用户长度不能超过255个字符")
    @Column(name = "candidate_users", length = 255)
    @Schema(description = "候选用户", example = "user1,user2,user3")
    private String candidateUsers;

    @NotNull(message = "任务创建时间不能为空")
    @Column(name = "task_create_time", nullable = false)
    @Schema(description = "任务创建时间")
    private LocalDateTime taskCreateTime;

    @Column(name = "claim_time")
    @Schema(description = "认领时间")
    private LocalDateTime claimTime;

    @Column(name = "complete_time")
    @Schema(description = "完成时间")
    private LocalDateTime completeTime;

    @Column(name = "due_date")
    @Schema(description = "到期时间")
    private LocalDateTime dueDate;

    @NotBlank(message = "状态不能为空")
    @Size(max = 20, message = "状态长度不能超过20个字符")
    @Column(name = "status", length = 20, nullable = false)
    @Schema(description = "状态", example = "PENDING")
    private String status;

    @Column(name = "priority")
    @Schema(description = "优先级", example = "50")
    private Integer priority = 50;

    @Column(name = "duration")
    @Schema(description = "持续时间(毫秒)")
    private Long duration;

    @Size(max = 20, message = "审批结果长度不能超过20个字符")
    @Column(name = "approval_result", length = 20)
    @Schema(description = "审批结果", example = "APPROVED")
    private String approvalResult;

    @Size(max = 500, message = "审批意见长度不能超过500个字符")
    @Column(name = "approval_comment", length = 500)
    @Schema(description = "审批意见")
    private String approvalComment;

    @Column(name = "form_data", columnDefinition = "TEXT")
    @Schema(description = "表单数据JSON")
    private String formData;

    @Column(name = "variables", columnDefinition = "TEXT")
    @Schema(description = "任务变量JSON")
    private String variables;

    @Size(max = 64, message = "业务键长度不能超过64个字符")
    @Column(name = "business_key", length = 64)
    @Schema(description = "业务键", example = "LEAVE_20241219_001")
    private String businessKey;

    @Size(max = 64, message = "业务类型长度不能超过64个字符")
    @Column(name = "business_type", length = 64)
    @Schema(description = "业务类型", example = "LEAVE_APPLICATION")
    private String businessType;

    // 构造函数
    public TaskInfo() {
    }

    public TaskInfo(String flowableTaskId, String processInstanceId, String processKey,
                   String taskKey, String taskName) {
        this.flowableTaskId = flowableTaskId;
        this.processInstanceId = processInstanceId;
        this.processKey = processKey;
        this.taskKey = taskKey;
        this.taskName = taskName;
        this.taskCreateTime = LocalDateTime.now();
        this.status = "PENDING";
    }

}
