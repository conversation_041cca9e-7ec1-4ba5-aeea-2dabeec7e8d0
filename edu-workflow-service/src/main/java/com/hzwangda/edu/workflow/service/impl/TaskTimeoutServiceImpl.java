package com.hzwangda.edu.workflow.service.impl;

import com.hzwangda.edu.workflow.service.TaskTimeoutService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 任务超时处理服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class TaskTimeoutServiceImpl implements TaskTimeoutService {

    @Autowired
    private TaskService taskService;

    // 超时监控开关
    private final AtomicBoolean timeoutMonitoringEnabled = new AtomicBoolean(true);

    // 超时配置存储（实际项目中应该存储在数据库中）
    private final Map<String, Map<String, Object>> timeoutConfigs = new HashMap<>();

    @Override
    public Map<String, Object> checkAndHandleTimeoutTasks() {
        log.info("开始检查并处理超时任务");

        if (!timeoutMonitoringEnabled.get()) {
            log.info("任务超时监控已禁用");
            return Collections.singletonMap("message", "任务超时监控已禁用");
        }

        Map<String, Object> result = new HashMap<>();
        int processedCount = 0;
        int notificationCount = 0;
        int escalationCount = 0;
        int autoCompleteCount = 0;

        try {
            // 获取所有超时任务
            List<Task> timeoutTasks = taskService.createTaskQuery()
                    .taskDueBefore(new Date())
                    .list();

            log.info("发现 {} 个超时任务", timeoutTasks.size());

            for (Task task : timeoutTasks) {
                try {
                    String configKey = task.getProcessDefinitionId() + ":" + task.getTaskDefinitionKey();
                    Map<String, Object> config = timeoutConfigs.get(configKey);

                    if (config != null) {
                        String action = (String) config.get("timeoutAction");
                        boolean handled = false;

                        switch (action) {
                            case "NOTIFICATION":
                                handled = sendTimeoutNotification(task.getId(), "WARNING");
                                if (handled) notificationCount++;
                                break;
                            case "ESCALATION":
                                String escalateToUserId = (String) config.get("escalateToUserId");
                                handled = escalateTask(task.getId(), escalateToUserId);
                                if (handled) escalationCount++;
                                break;
                            case "AUTO_COMPLETE":
                                Map<String, Object> variables = (Map<String, Object>) config.get("autoCompleteVariables");
                                handled = autoCompleteTask(task.getId(), variables);
                                if (handled) autoCompleteCount++;
                                break;
                            default:
                                log.warn("未知的超时处理动作: {}", action);
                        }

                        if (handled) {
                            processedCount++;
                        }
                    } else {
                        // 默认发送通知
                        if (sendTimeoutNotification(task.getId(), "WARNING")) {
                            notificationCount++;
                            processedCount++;
                        }
                    }
                } catch (Exception e) {
                    log.error("处理超时任务失败: taskId={}, error={}", task.getId(), e.getMessage(), e);
                }
            }

            result.put("totalTimeoutTasks", timeoutTasks.size());
            result.put("processedCount", processedCount);
            result.put("notificationCount", notificationCount);
            result.put("escalationCount", escalationCount);
            result.put("autoCompleteCount", autoCompleteCount);
            result.put("processTime", LocalDateTime.now());

            log.info("超时任务处理完成: 总数={}, 处理数={}", timeoutTasks.size(), processedCount);

        } catch (Exception e) {
            log.error("检查处理超时任务失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
        }

        return result;
    }

    @Override
    public boolean handleTaskTimeout(String taskId) {
        log.info("处理指定任务超时: {}", taskId);

        try {
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                log.warn("任务不存在: {}", taskId);
                return false;
            }

            if (task.getDueDate() == null || task.getDueDate().after(new Date())) {
                log.warn("任务未超时: {}", taskId);
                return false;
            }

            String configKey = task.getProcessDefinitionId() + ":" + task.getTaskDefinitionKey();
            Map<String, Object> config = timeoutConfigs.get(configKey);

            if (config != null) {
                String action = (String) config.get("timeoutAction");
                switch (action) {
                    case "NOTIFICATION":
                        return sendTimeoutNotification(taskId, "WARNING");
                    case "ESCALATION":
                        String escalateToUserId = (String) config.get("escalateToUserId");
                        return escalateTask(taskId, escalateToUserId);
                    case "AUTO_COMPLETE":
                        Map<String, Object> variables = (Map<String, Object>) config.get("autoCompleteVariables");
                        return autoCompleteTask(taskId, variables);
                }
            }

            // 默认发送通知
            return sendTimeoutNotification(taskId, "WARNING");

        } catch (Exception e) {
            log.error("处理任务超时失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<Map<String, Object>> getTimeoutTasks() {
        log.debug("获取超时任务列表");

        List<Task> timeoutTasks = taskService.createTaskQuery()
                .taskDueBefore(new Date())
                .list();

        return timeoutTasks.stream().map(task -> {
            Map<String, Object> taskInfo = new HashMap<>();
            taskInfo.put("id", task.getId());
            taskInfo.put("name", task.getName());
            taskInfo.put("assignee", task.getAssignee());
            taskInfo.put("processInstanceId", task.getProcessInstanceId());
            taskInfo.put("processDefinitionId", task.getProcessDefinitionId());
            taskInfo.put("dueDate", task.getDueDate());
            taskInfo.put("createTime", task.getCreateTime());

            // 计算超时时长
            if (task.getDueDate() != null) {
                long overdueDuration = new Date().getTime() - task.getDueDate().getTime();
                taskInfo.put("overdueDurationMinutes", overdueDuration / (1000 * 60));
            }

            return taskInfo;
        }).collect(Collectors.toList());
    }

    @Override
    public boolean setTaskTimeoutConfig(String processDefinitionKey, String activityId,
                                      int timeoutMinutes, String timeoutAction) {
        log.info("设置任务超时配置: processDefinitionKey={}, activityId={}, timeoutMinutes={}, timeoutAction={}",
                processDefinitionKey, activityId, timeoutMinutes, timeoutAction);

        try {
            String configKey = processDefinitionKey + ":" + activityId;
            Map<String, Object> config = new HashMap<>();
            config.put("processDefinitionKey", processDefinitionKey);
            config.put("activityId", activityId);
            config.put("timeoutMinutes", timeoutMinutes);
            config.put("timeoutAction", timeoutAction);
            config.put("createTime", LocalDateTime.now());

            timeoutConfigs.put(configKey, config);
            return true;
        } catch (Exception e) {
            log.error("设置任务超时配置失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getTaskTimeoutConfig(String processDefinitionKey, String activityId) {
        log.debug("获取任务超时配置: processDefinitionKey={}, activityId={}", processDefinitionKey, activityId);

        String configKey = processDefinitionKey + ":" + activityId;
        return timeoutConfigs.get(configKey);
    }

    @Override
    public boolean deleteTaskTimeoutConfig(String processDefinitionKey, String activityId) {
        log.info("删除任务超时配置: processDefinitionKey={}, activityId={}", processDefinitionKey, activityId);

        String configKey = processDefinitionKey + ":" + activityId;
        return timeoutConfigs.remove(configKey) != null;
    }

    @Override
    public List<Map<String, Object>> getAllTimeoutConfigs() {
        log.debug("获取所有超时配置");

        return new ArrayList<>(timeoutConfigs.values());
    }

    @Override
    public boolean sendTimeoutNotification(String taskId, String notificationType) {
        log.info("发送超时通知: taskId={}, notificationType={}", taskId, notificationType);

        try {
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                log.warn("任务不存在: {}", taskId);
                return false;
            }

            // TODO: 集成通知服务发送超时通知
            // 这里应该调用NotificationClient发送通知
            log.info("模拟发送超时通知: 任务 {} 已超时，请及时处理", task.getName());

            return true;
        } catch (Exception e) {
            log.error("发送超时通知失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean escalateTask(String taskId, String escalateToUserId) {
        log.info("任务超时升级: taskId={}, escalateToUserId={}", taskId, escalateToUserId);

        try {
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                log.warn("任务不存在: {}", taskId);
                return false;
            }

            // 变更任务处理人
            taskService.setAssignee(taskId, escalateToUserId);

            // 添加备注
            taskService.addComment(taskId, task.getProcessInstanceId(),
                    "任务因超时自动升级到用户: " + escalateToUserId);

            log.info("任务升级成功: taskId={}, 新处理人={}", taskId, escalateToUserId);
            return true;
        } catch (Exception e) {
            log.error("任务升级失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean autoCompleteTask(String taskId, Map<String, Object> autoCompleteVariables) {
        log.info("任务超时自动完成: taskId={}", taskId);

        try {
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                log.warn("任务不存在: {}", taskId);
                return false;
            }

            // 添加备注
            taskService.addComment(taskId, task.getProcessInstanceId(), "任务因超时自动完成");

            // 完成任务
            if (autoCompleteVariables != null && !autoCompleteVariables.isEmpty()) {
                taskService.complete(taskId, autoCompleteVariables);
            } else {
                taskService.complete(taskId);
            }

            log.info("任务自动完成成功: taskId={}", taskId);
            return true;
        } catch (Exception e) {
            log.error("任务自动完成失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void enableTimeoutMonitoring() {
        log.info("启用任务超时监控");
        timeoutMonitoringEnabled.set(true);
    }

    @Override
    public void disableTimeoutMonitoring() {
        log.info("禁用任务超时监控");
        timeoutMonitoringEnabled.set(false);
    }

    @Override
    public boolean isTimeoutMonitoringEnabled() {
        return timeoutMonitoringEnabled.get();
    }
}
