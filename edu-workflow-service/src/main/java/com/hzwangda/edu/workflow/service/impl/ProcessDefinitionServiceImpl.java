package com.hzwangda.edu.workflow.service.impl;

import com.hzwangda.edu.common.exception.BusinessException;
import com.hzwangda.edu.common.dto.PageResult;
import com.hzwangda.edu.common.result.ResultCode;
import com.hzwangda.edu.workflow.dto.ProcessDefinitionDTO;
import com.hzwangda.edu.workflow.dto.ProcessDefinitionQueryRequest;
import com.hzwangda.edu.workflow.dto.ProcessDeploymentRequest;
import com.hzwangda.edu.workflow.dto.ProcessDefinitionMetadataUpdateRequest;
import com.hzwangda.edu.workflow.service.ProcessDefinitionService;
import org.springframework.web.multipart.MultipartFile;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.FlowNode;
import org.flowable.bpmn.model.SequenceFlow;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.*;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.flowable.image.ProcessDiagramGenerator;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.Process;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * 流程定义服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class ProcessDefinitionServiceImpl implements ProcessDefinitionService {

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private ProcessEngine processEngine;

    @Override
    public ProcessDefinitionDTO deployProcess(MultipartFile file, ProcessDeploymentRequest request) {
        try {
            return deployProcess(file.getInputStream(), file.getOriginalFilename(), request);
        } catch (Exception e) {
            log.error("部署流程定义失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "部署流程定义失败: " + e.getMessage());
        }
    }

    @Override
    public ProcessDefinitionDTO deployProcess(InputStream inputStream, String fileName, ProcessDeploymentRequest request) {
        try {
            log.info("开始部署流程定义: fileName={}, request={}", fileName, request);

            // 创建部署构建器
            var deploymentBuilder = repositoryService.createDeployment()
                    .name(request.getName())
                    .addInputStream(fileName, inputStream);

            // 设置分类
            if (StringUtils.hasText(request.getCategory())) {
                deploymentBuilder.category(request.getCategory());
            }

            // 设置租户ID
            if (StringUtils.hasText(request.getTenantId())) {
                deploymentBuilder.tenantId(request.getTenantId());
            }

            // 执行部署
            Deployment deployment = deploymentBuilder.deploy();

            // 获取部署的流程定义
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .deploymentId(deployment.getId())
                    .singleResult();

            if (processDefinition == null) {
                throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "部署后未找到流程定义");
            }

            // 如果需要激活
            if (request.getActivate() != null && request.getActivate()) {
                repositoryService.activateProcessDefinitionById(processDefinition.getId());
            }

            log.info("流程定义部署成功: id={}, key={}, name={}",
                    processDefinition.getId(), processDefinition.getKey(), processDefinition.getName());

            return convertToDTO(processDefinition, deployment);

        } catch (Exception e) {
            log.error("部署流程定义失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "部署流程定义失败: " + e.getMessage());
        }
    }

    @Override
    public PageResult<ProcessDefinitionDTO> queryProcessDefinitions(ProcessDefinitionQueryRequest request) {
        log.debug("分页查询流程定义: {}", request);

        ProcessDefinitionQuery query = repositoryService.createProcessDefinitionQuery();

        // 构建查询条件
        if (StringUtils.hasText(request.getKey())) {
            query.processDefinitionKey(request.getKey());
        }
        if (StringUtils.hasText(request.getName())) {
            query.processDefinitionNameLike("%" + request.getName() + "%");
        }
        if (StringUtils.hasText(request.getCategory())) {
            query.processDefinitionCategory(request.getCategory());
        }
        if (request.getSuspended() != null) {
            if (request.getSuspended()) {
                query.suspended();
            } else {
                query.active();
            }
        }
        if (StringUtils.hasText(request.getTenantId())) {
            query.processDefinitionTenantId(request.getTenantId());
        }

        // 排序
        if ("name".equals(request.getSortBy())) {
            query.orderByProcessDefinitionName();
        } else if ("key".equals(request.getSortBy())) {
            query.orderByProcessDefinitionKey();
        } else if ("version".equals(request.getSortBy())) {
            query.orderByProcessDefinitionVersion();
        } else {
            query.orderByDeploymentId();
        }

        if ("asc".equals(request.getSortDirection())) {
            query.asc();
        } else {
            query.desc();
        }

        // 分页查询
        long total = query.count();
        int offset = (request.getPageNum() - 1) * request.getPageSize();
        List<ProcessDefinition> processDefinitions = query.listPage(offset, request.getPageSize());

        // 转换为DTO
        List<ProcessDefinitionDTO> dtoList = processDefinitions.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        return PageResult.of(dtoList, total, request.getPageNum() - 1, request.getPageSize());
    }

    @Override
    public ProcessDefinitionDTO getProcessDefinitionById(String id) {
        log.debug("根据ID查询流程定义: {}", id);

        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(id)
                .singleResult();

        if (processDefinition == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "流程定义不存在: " + id);
        }

        return convertToDTO(processDefinition);
    }

    @Override
    public ProcessDefinitionDTO getLatestProcessDefinitionByKey(String key) {
        log.debug("根据Key查询最新版本的流程定义: {}", key);

        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .processDefinitionKey(key)
                .latestVersion()
                .singleResult();

        if (processDefinition == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "流程定义不存在: " + key);
        }

        return convertToDTO(processDefinition);
    }

    @Override
    public boolean deleteProcessDefinition(String deploymentId, boolean cascade) {
        log.info("删除流程定义: deploymentId={}, cascade={}", deploymentId, cascade);

        try {
            repositoryService.deleteDeployment(deploymentId, cascade);
            return true;
        } catch (Exception e) {
            log.error("删除流程定义失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "删除流程定义失败: " + e.getMessage());
        }
    }

    @Override
    public boolean activateProcessDefinition(String id) {
        log.info("激活流程定义: {}", id);

        try {
            repositoryService.activateProcessDefinitionById(id);
            return true;
        } catch (Exception e) {
            log.error("激活流程定义失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "激活流程定义失败: " + e.getMessage());
        }
    }

    @Override
    public boolean suspendProcessDefinition(String id) {
        log.info("挂起流程定义: {}", id);

        try {
            repositoryService.suspendProcessDefinitionById(id);
            return true;
        } catch (Exception e) {
            log.error("挂起流程定义失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "挂起流程定义失败: " + e.getMessage());
        }
    }

    @Override
    public String getProcessDefinitionXml(String id) {
        log.debug("获取流程定义XML: {}", id);

        try {
            ProcessDefinition processDefinition = repositoryService.getProcessDefinition(id);
            InputStream inputStream = repositoryService.getResourceAsStream(
                    processDefinition.getDeploymentId(), processDefinition.getResourceName());

            return new String(inputStream.readAllBytes());
        } catch (Exception e) {
            log.error("获取流程定义XML失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取流程定义XML失败: " + e.getMessage());
        }
    }

    @Override
    public byte[] generateProcessDiagram(String id) {
        log.debug("生成流程图: {}", id);

        try {
            ProcessDefinition processDefinition = repositoryService.getProcessDefinition(id);
            BpmnModel bpmnModel = repositoryService.getBpmnModel(id);

            ProcessDiagramGenerator diagramGenerator = processEngine.getProcessEngineConfiguration()
                    .getProcessDiagramGenerator();

            InputStream inputStream = diagramGenerator.generateDiagram(
                    bpmnModel, "png", Collections.emptyList(), Collections.emptyList(),
                    "宋体", "宋体", "宋体", null, 1.0, true);

            return inputStream.readAllBytes();
        } catch (Exception e) {
            log.error("生成流程图失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "生成流程图失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getProcessDefinitionStatistics(String id) {
        log.debug("获取流程定义统计信息: {}", id);

        ProcessDefinition processDefinition = repositoryService.getProcessDefinition(id);

        // 统计流程实例数量
        long totalInstances = historyService.createHistoricProcessInstanceQuery()
                .processDefinitionId(id)
                .count();

        long activeInstances = runtimeService.createProcessInstanceQuery()
                .processDefinitionId(id)
                .count();

        long completedInstances = historyService.createHistoricProcessInstanceQuery()
                .processDefinitionId(id)
                .finished()
                .count();

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("processDefinitionId", id);
        statistics.put("processDefinitionKey", processDefinition.getKey());
        statistics.put("processDefinitionName", processDefinition.getName());
        statistics.put("version", processDefinition.getVersion());
        statistics.put("totalInstances", totalInstances);
        statistics.put("activeInstances", activeInstances);
        statistics.put("completedInstances", completedInstances);
        statistics.put("completionRate", totalInstances > 0 ? (double) completedInstances / totalInstances * 100 : 0);

        return statistics;
    }

    @Override
    public List<String> getAllCategories() {
        log.debug("获取所有流程分类");

        return repositoryService.createProcessDefinitionQuery()
                .list()
                .stream()
                .map(ProcessDefinition::getCategory)
                .filter(Objects::nonNull)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }

    @Override
    public boolean existsByKey(String key) {
        return repositoryService.createProcessDefinitionQuery()
                .processDefinitionKey(key)
                .count() > 0;
    }

    @Override
    public Object getStartForm(String id) {
        log.debug("获取流程定义的启动表单: {}", id);

        // TODO: 实现启动表单获取逻辑
        return null;
    }

    /**
     * 转换为DTO
     */
    private ProcessDefinitionDTO convertToDTO(ProcessDefinition processDefinition) {
        return convertToDTO(processDefinition, null);
    }

    /**
     * 转换为DTO
     */
    private ProcessDefinitionDTO convertToDTO(ProcessDefinition processDefinition, Deployment deployment) {
        ProcessDefinitionDTO dto = ProcessDefinitionDTO.builder()
                .id(processDefinition.getId())
                .key(processDefinition.getKey())
                .name(processDefinition.getName())
                .description(processDefinition.getDescription())
                .version(processDefinition.getVersion())
                .category(processDefinition.getCategory())
                .deploymentId(processDefinition.getDeploymentId())
                .resourceName(processDefinition.getResourceName())
                .diagramResourceName(processDefinition.getDiagramResourceName())
                .suspended(processDefinition.isSuspended())
                .hasStartFormKey(processDefinition.hasStartFormKey())
                .hasGraphicalNotation(processDefinition.hasGraphicalNotation())
                .tenantId(processDefinition.getTenantId())
                .build();

        // 设置部署时间
        if (deployment != null) {
            dto.setDeploymentTime(LocalDateTime.ofInstant(deployment.getDeploymentTime().toInstant(), ZoneId.systemDefault()));
        } else {
            // 如果没有传入deployment，则查询获取
            Deployment dep = repositoryService.createDeploymentQuery()
                    .deploymentId(processDefinition.getDeploymentId())
                    .singleResult();
            if (dep != null) {
                dto.setDeploymentTime(LocalDateTime.ofInstant(dep.getDeploymentTime().toInstant(), ZoneId.systemDefault()));
            }
        }

        // 统计实例数量
        long totalInstances = historyService.createHistoricProcessInstanceQuery()
                .processDefinitionId(processDefinition.getId())
                .count();
        long activeInstances = runtimeService.createProcessInstanceQuery()
                .processDefinitionId(processDefinition.getId())
                .count();
        long completedInstances = historyService.createHistoricProcessInstanceQuery()
                .processDefinitionId(processDefinition.getId())
                .finished()
                .count();

        dto.setInstanceCount(totalInstances);
        dto.setActiveInstanceCount(activeInstances);
        dto.setCompletedInstanceCount(completedInstances);

        return dto;
    }

    // ==================== 版本管理功能实现 ====================

    @Override
    public List<ProcessDefinitionDTO> getProcessDefinitionVersions(String key) {
        log.info("获取流程定义所有版本: {}", key);

        try {
            List<ProcessDefinition> processDefinitions = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionKey(key)
                    .orderByProcessDefinitionVersion()
                    .desc()
                    .list();

            return processDefinitions.stream()
                    .map(pd -> convertToDTO(pd, null))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取流程定义版本列表失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取流程定义版本列表失败: " + e.getMessage());
        }
    }

    @Override
    public boolean setDefaultVersion(String id) {
        log.info("设置默认版本: {}", id);

        try {
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(id)
                    .singleResult();

            if (processDefinition == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "流程定义不存在: " + id);
            }

            // 激活指定版本
            repositoryService.activateProcessDefinitionById(id);

            // 挂起同一Key的其他版本
            List<ProcessDefinition> otherVersions = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionKey(processDefinition.getKey())
                    .active()
                    .list()
                    .stream()
                    .filter(pd -> !pd.getId().equals(id))
                    .collect(java.util.stream.Collectors.toList());

            for (ProcessDefinition otherVersion : otherVersions) {
                repositoryService.suspendProcessDefinitionById(otherVersion.getId());
            }

            return true;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("设置默认版本失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "设置默认版本失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> compareVersions(String id1, String id2) {
        log.info("版本比较: {} vs {}", id1, id2);

        try {
            ProcessDefinition pd1 = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(id1)
                    .singleResult();
            ProcessDefinition pd2 = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(id2)
                    .singleResult();

            if (pd1 == null || pd2 == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "流程定义不存在");
            }

            Map<String, Object> comparison = new HashMap<>();
            comparison.put("version1", convertToDTO(pd1, null));
            comparison.put("version2", convertToDTO(pd2, null));

            // 比较基本信息
            Map<String, Object> differences = new HashMap<>();
            differences.put("name", Map.of("version1", pd1.getName(), "version2", pd2.getName()));
            differences.put("description", Map.of("version1", pd1.getDescription(), "version2", pd2.getDescription()));
            differences.put("category", Map.of("version1", pd1.getCategory(), "version2", pd2.getCategory()));
            differences.put("version", Map.of("version1", pd1.getVersion(), "version2", pd2.getVersion()));

            comparison.put("differences", differences);
            comparison.put("compareTime", LocalDateTime.now());

            return comparison;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("版本比较失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "版本比较失败: " + e.getMessage());
        }
    }

    @Override
    public ProcessDefinitionDTO rollbackVersion(String id, String reason) {
        log.info("版本回滚: {}, 原因: {}", id, reason);

        try {
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(id)
                    .singleResult();

            if (processDefinition == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "流程定义不存在: " + id);
            }

            // 激活指定版本
            repositoryService.activateProcessDefinitionById(id);

            // 挂起同一Key的其他版本
            List<ProcessDefinition> otherVersions = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionKey(processDefinition.getKey())
                    .active()
                    .list()
                    .stream()
                    .filter(pd -> !pd.getId().equals(id))
                    .collect(java.util.stream.Collectors.toList());

            for (ProcessDefinition otherVersion : otherVersions) {
                repositoryService.suspendProcessDefinitionById(otherVersion.getId());
            }

            log.info("版本回滚成功: {} -> 版本{}, 原因: {}",
                    processDefinition.getKey(), processDefinition.getVersion(), reason);

            return convertToDTO(processDefinition, null);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("版本回滚失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "版本回滚失败: " + e.getMessage());
        }
    }

    // ==================== 元数据管理功能实现 ====================

    @Override
    public ProcessDefinitionDTO updateMetadata(String id, ProcessDefinitionMetadataUpdateRequest request) {
        log.info("更新流程定义元数据: {}, 请求: {}", id, request);

        try {
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(id)
                    .singleResult();

            if (processDefinition == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "流程定义不存在: " + id);
            }

            // 注意：Flowable的流程定义一旦部署就不能直接修改元数据
            // 这里我们可以通过重新部署的方式来实现元数据更新
            // 或者在自定义的流程定义信息表中维护这些元数据

            log.warn("流程定义元数据更新功能需要通过重新部署实现，当前返回原始数据");

            return convertToDTO(processDefinition, null);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新流程定义元数据失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新流程定义元数据失败: " + e.getMessage());
        }
    }

    @Override
    public boolean updateCategory(String id, String category) {
        log.info("更新流程分类: {}, 新分类: {}", id, category);

        try {
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(id)
                    .singleResult();

            if (processDefinition == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "流程定义不存在: " + id);
            }

            // 更新部署的分类
            repositoryService.setDeploymentCategory(processDefinition.getDeploymentId(), category);

            log.info("流程分类更新成功: {} -> {}", id, category);
            return true;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新流程分类失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新流程分类失败: " + e.getMessage());
        }
    }

    @Override
    public boolean updateDescription(String id, String description) {
        log.info("更新流程描述: {}, 新描述: {}", id, description);

        try {
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(id)
                    .singleResult();

            if (processDefinition == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "流程定义不存在: " + id);
            }

            // 注意：Flowable的流程定义描述不能直接修改
            // 这里记录日志，实际应用中可能需要通过重新部署或在自定义表中维护
            log.warn("流程定义描述更新功能需要通过重新部署实现，当前仅记录日志");
            log.info("流程描述更新请求: {} -> {}", id, description);

            return true;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新流程描述失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新流程描述失败: " + e.getMessage());
        }
    }

    // ==================== 导入导出功能实现 ====================

    @Override
    public byte[] exportProcessDefinitions(List<String> ids) {
        log.info("批量导出流程定义: {}", ids);

        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             ZipOutputStream zos = new ZipOutputStream(baos)) {

            for (String id : ids) {
                ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                        .processDefinitionId(id)
                        .singleResult();

                if (processDefinition != null) {
                    // 获取BPMN文件内容
                    InputStream inputStream = repositoryService.getResourceAsStream(
                            processDefinition.getDeploymentId(), processDefinition.getResourceName());

                    // 创建ZIP条目
                    String fileName = processDefinition.getKey() + "_v" + processDefinition.getVersion() + ".bpmn";
                    ZipEntry zipEntry = new ZipEntry(fileName);
                    zos.putNextEntry(zipEntry);

                    // 写入文件内容
                    byte[] buffer = new byte[1024];
                    int length;
                    while ((length = inputStream.read(buffer)) > 0) {
                        zos.write(buffer, 0, length);
                    }

                    zos.closeEntry();
                    inputStream.close();

                    log.info("已导出流程定义: {} -> {}", id, fileName);
                }
            }

            zos.finish();
            return baos.toByteArray();

        } catch (Exception e) {
            log.error("批量导出流程定义失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "批量导出流程定义失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> importProcessDefinitions(MultipartFile file, boolean overwrite) {
        log.info("批量导入流程定义: {}, 覆盖模式: {}", file.getOriginalFilename(), overwrite);

        Map<String, Object> result = new HashMap<>();
        List<String> successList = new ArrayList<>();
        List<String> failureList = new ArrayList<>();
        List<String> skippedList = new ArrayList<>();

        try (ZipInputStream zis = new ZipInputStream(file.getInputStream())) {
            ZipEntry zipEntry;
            while ((zipEntry = zis.getNextEntry()) != null) {
                if (!zipEntry.isDirectory() && zipEntry.getName().endsWith(".bpmn")) {
                    try {
                        // 读取BPMN文件内容
                        ByteArrayOutputStream baos = new ByteArrayOutputStream();
                        byte[] buffer = new byte[1024];
                        int length;
                        while ((length = zis.read(buffer)) > 0) {
                            baos.write(buffer, 0, length);
                        }

                        // 验证BPMN文件
                        byte[] bpmnContent = baos.toByteArray();
                        Map<String, Object> validationResult = validateBpmnContent(bpmnContent);

                        if (!(Boolean) validationResult.get("valid")) {
                            failureList.add(zipEntry.getName() + ": " + validationResult.get("error"));
                            continue;
                        }

                        // 检查是否已存在
                        String processKey = extractProcessKeyFromBpmn(bpmnContent);
                        if (!overwrite && existsByKey(processKey)) {
                            skippedList.add(zipEntry.getName() + ": 流程定义已存在");
                            continue;
                        }

                        // 部署流程定义
                        ProcessDeploymentRequest deployRequest = ProcessDeploymentRequest.builder()
                                .name(zipEntry.getName())
                                .category("导入")
                                .activate(true)
                                .build();

                        deployProcess(new ByteArrayInputStream(bpmnContent), zipEntry.getName(), deployRequest);
                        successList.add(zipEntry.getName());

                    } catch (Exception e) {
                        log.error("导入流程定义失败: {}", zipEntry.getName(), e);
                        failureList.add(zipEntry.getName() + ": " + e.getMessage());
                    }
                }
            }

        } catch (Exception e) {
            log.error("批量导入流程定义失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "批量导入流程定义失败: " + e.getMessage());
        }

        result.put("total", successList.size() + failureList.size() + skippedList.size());
        result.put("success", successList.size());
        result.put("failure", failureList.size());
        result.put("skipped", skippedList.size());
        result.put("successList", successList);
        result.put("failureList", failureList);
        result.put("skippedList", skippedList);
        result.put("importTime", LocalDateTime.now());

        log.info("批量导入完成: 成功{}, 失败{}, 跳过{}", successList.size(), failureList.size(), skippedList.size());
        return result;
    }

    @Override
    public Map<String, Object> validateBpmnFile(MultipartFile file) {
        log.info("验证BPMN文件: {}", file.getOriginalFilename());

        try {
            byte[] content = file.getBytes();
            return validateBpmnContent(content);
        } catch (Exception e) {
            log.error("验证BPMN文件失败: {}", e.getMessage(), e);
            Map<String, Object> result = new HashMap<>();
            result.put("valid", false);
            result.put("error", "文件读取失败: " + e.getMessage());
            return result;
        }
    }

    @Override
    public byte[] getProcessDefinitionTemplate(String templateType) {
        log.info("获取流程定义模板: {}", templateType);

        try {
            String template = generateBpmnTemplate(templateType);
            return template.getBytes("UTF-8");
        } catch (Exception e) {
            log.error("获取流程定义模板失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取流程定义模板失败: " + e.getMessage());
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 验证BPMN文件内容
     */
    private Map<String, Object> validateBpmnContent(byte[] content) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 基本格式验证
            String xmlContent = new String(content, "UTF-8");
            if (!xmlContent.contains("<?xml") || !xmlContent.contains("bpmn")) {
                result.put("valid", false);
                result.put("error", "不是有效的BPMN文件格式");
                return result;
            }

            // 尝试解析BPMN模型
            BpmnXMLConverter converter = new BpmnXMLConverter();
            BpmnModel bpmnModel = converter.convertToBpmnModel(
                    () -> new ByteArrayInputStream(content), false, false);

            if (bpmnModel.getProcesses().isEmpty()) {
                result.put("valid", false);
                result.put("error", "BPMN文件中没有找到流程定义");
                return result;
            }

            // 验证流程定义的基本要素
            Process process = bpmnModel.getProcesses().get(0);
            if (!StringUtils.hasText(process.getId())) {
                result.put("valid", false);
                result.put("error", "流程定义缺少ID");
                return result;
            }

            result.put("valid", true);
            result.put("processId", process.getId());
            result.put("processName", process.getName());
            result.put("elementCount", process.getFlowElements().size());
            result.put("validationTime", LocalDateTime.now());

        } catch (Exception e) {
            result.put("valid", false);
            result.put("error", "BPMN文件解析失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 从BPMN内容中提取流程Key
     */
    private String extractProcessKeyFromBpmn(byte[] content) {
        try {
            String xmlContent = new String(content, "UTF-8");
            // 简单的正则表达式提取process id
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("process\\s+id=\"([^\"]+)\"");
            java.util.regex.Matcher matcher = pattern.matcher(xmlContent);
            if (matcher.find()) {
                return matcher.group(1);
            }
        } catch (Exception e) {
            log.warn("提取流程Key失败: {}", e.getMessage());
        }
        return "unknown_process_" + System.currentTimeMillis();
    }

    /**
     * 生成BPMN模板
     */
    private String generateBpmnTemplate(String templateType) {
        switch (templateType.toLowerCase()) {
            case "basic":
                return generateBasicTemplate();
            case "approval":
                return generateApprovalTemplate();
            case "sequential":
                return generateSequentialTemplate();
            default:
                return generateBasicTemplate();
        }
    }

    /**
     * 生成基础模板
     */
    private String generateBasicTemplate() {
        return """
                <?xml version="1.0" encoding="UTF-8"?>
                <definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
                           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                           xmlns:flowable="http://flowable.org/bpmn"
                           targetNamespace="http://www.flowable.org/processdef">

                  <process id="basicProcess" name="基础流程模板" isExecutable="true">

                    <startEvent id="startEvent" name="开始"/>

                    <userTask id="userTask" name="用户任务" flowable:assignee="${assignee}"/>

                    <endEvent id="endEvent" name="结束"/>

                    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="userTask"/>
                    <sequenceFlow id="flow2" sourceRef="userTask" targetRef="endEvent"/>

                  </process>

                </definitions>
                """;
    }

    /**
     * 生成审批模板
     */
    private String generateApprovalTemplate() {
        return """
                <?xml version="1.0" encoding="UTF-8"?>
                <definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
                           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                           xmlns:flowable="http://flowable.org/bpmn"
                           targetNamespace="http://www.flowable.org/processdef">

                  <process id="approvalProcess" name="审批流程模板" isExecutable="true">

                    <startEvent id="startEvent" name="提交申请"/>

                    <userTask id="managerApproval" name="经理审批" flowable:assignee="${manager}"/>

                    <exclusiveGateway id="decision" name="审批决策"/>

                    <userTask id="hrApproval" name="HR审批" flowable:assignee="${hr}"/>

                    <endEvent id="approvedEnd" name="审批通过"/>
                    <endEvent id="rejectedEnd" name="审批拒绝"/>

                    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="managerApproval"/>
                    <sequenceFlow id="flow2" sourceRef="managerApproval" targetRef="decision"/>
                    <sequenceFlow id="flow3" sourceRef="decision" targetRef="hrApproval">
                      <conditionExpression xsi:type="tFormalExpression">${approved == true}</conditionExpression>
                    </sequenceFlow>
                    <sequenceFlow id="flow4" sourceRef="decision" targetRef="rejectedEnd">
                      <conditionExpression xsi:type="tFormalExpression">${approved == false}</conditionExpression>
                    </sequenceFlow>
                    <sequenceFlow id="flow5" sourceRef="hrApproval" targetRef="approvedEnd"/>

                  </process>

                </definitions>
                """;
    }

    /**
     * 生成顺序模板
     */
    private String generateSequentialTemplate() {
        return """
                <?xml version="1.0" encoding="UTF-8"?>
                <definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
                           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                           xmlns:flowable="http://flowable.org/bpmn"
                           targetNamespace="http://www.flowable.org/processdef">

                  <process id="sequentialProcess" name="顺序流程模板" isExecutable="true">

                    <startEvent id="startEvent" name="开始"/>

                    <userTask id="task1" name="任务1" flowable:assignee="${user1}"/>
                    <userTask id="task2" name="任务2" flowable:assignee="${user2}"/>
                    <userTask id="task3" name="任务3" flowable:assignee="${user3}"/>

                    <endEvent id="endEvent" name="结束"/>

                    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="task1"/>
                    <sequenceFlow id="flow2" sourceRef="task1" targetRef="task2"/>
                    <sequenceFlow id="flow3" sourceRef="task2" targetRef="task3"/>
                    <sequenceFlow id="flow4" sourceRef="task3" targetRef="endEvent"/>

                  </process>

                </definitions>
                """;
    }

    // ==================== 流程图增强功能实现 ====================

    @Override
    public byte[] generateProcessDiagramPreview(String id, String format, int width, int height) {
        log.debug("生成流程图预览: id={}, format={}, width={}, height={}", id, format, width, height);

        try {
            ProcessDefinition processDefinition = repositoryService.getProcessDefinition(id);
            BpmnModel bpmnModel = repositoryService.getBpmnModel(id);

            ProcessDiagramGenerator diagramGenerator = processEngine.getProcessEngineConfiguration()
                    .getProcessDiagramGenerator();

            InputStream inputStream;
            if ("svg".equalsIgnoreCase(format)) {
                inputStream = diagramGenerator.generateDiagram(
                        bpmnModel, "svg", Collections.emptyList(), Collections.emptyList(),
                        "宋体", "宋体", "宋体", null, 1.0, true);
            } else {
                inputStream = diagramGenerator.generateDiagram(
                        bpmnModel, "png", Collections.emptyList(), Collections.emptyList(),
                        "宋体", "宋体", "宋体", null, 1.0, true);
            }

            return inputStream.readAllBytes();
        } catch (Exception e) {
            log.error("生成流程图预览失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "生成流程图预览失败: " + e.getMessage());
        }
    }

    @Override
    public byte[] generateProcessDiagramWithHighlight(String id, List<String> highlightNodes,
                                                    List<String> highlightFlows, String format) {
        log.debug("生成高亮流程图: id={}, highlightNodes={}, highlightFlows={}, format={}",
                id, highlightNodes, highlightFlows, format);

        try {
            ProcessDefinition processDefinition = repositoryService.getProcessDefinition(id);
            BpmnModel bpmnModel = repositoryService.getBpmnModel(id);

            ProcessDiagramGenerator diagramGenerator = processEngine.getProcessEngineConfiguration()
                    .getProcessDiagramGenerator();

            List<String> nodes = highlightNodes != null ? highlightNodes : Collections.emptyList();
            List<String> flows = highlightFlows != null ? highlightFlows : Collections.emptyList();

            InputStream inputStream = diagramGenerator.generateDiagram(
                    bpmnModel, format.toLowerCase(), nodes, flows,
                    "宋体", "宋体", "宋体", null, 1.0, true);

            return inputStream.readAllBytes();
        } catch (Exception e) {
            log.error("生成高亮流程图失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "生成高亮流程图失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getProcessDiagramMetadata(String id) {
        log.debug("获取流程图元数据: {}", id);

        try {
            ProcessDefinition processDefinition = repositoryService.getProcessDefinition(id);
            BpmnModel bpmnModel = repositoryService.getBpmnModel(id);

            Map<String, Object> metadata = new HashMap<>();

            if (!bpmnModel.getProcesses().isEmpty()) {
                Process process = bpmnModel.getProcesses().get(0);

                // 基本信息
                metadata.put("processId", process.getId());
                metadata.put("processName", process.getName());
                metadata.put("processDefinitionId", id);
                metadata.put("processDefinitionKey", processDefinition.getKey());
                metadata.put("version", processDefinition.getVersion());

                // 节点信息
                List<Map<String, Object>> nodes = new ArrayList<>();
                List<Map<String, Object>> flows = new ArrayList<>();

                for (FlowElement element : process.getFlowElements()) {
                    if (element instanceof FlowNode) {
                        Map<String, Object> nodeInfo = new HashMap<>();
                        nodeInfo.put("id", element.getId());
                        nodeInfo.put("name", element.getName());
                        nodeInfo.put("type", element.getClass().getSimpleName());

                        if (element instanceof UserTask) {
                            UserTask userTask = (UserTask) element;
                            nodeInfo.put("assignee", userTask.getAssignee());
                            nodeInfo.put("candidateGroups", userTask.getCandidateGroups());
                            nodeInfo.put("candidateUsers", userTask.getCandidateUsers());
                        }

                        nodes.add(nodeInfo);
                    } else if (element instanceof SequenceFlow) {
                        SequenceFlow sequenceFlow = (SequenceFlow) element;
                        Map<String, Object> flowInfo = new HashMap<>();
                        flowInfo.put("id", sequenceFlow.getId());
                        flowInfo.put("name", sequenceFlow.getName());
                        flowInfo.put("sourceRef", sequenceFlow.getSourceRef());
                        flowInfo.put("targetRef", sequenceFlow.getTargetRef());
                        flowInfo.put("conditionExpression", sequenceFlow.getConditionExpression());

                        flows.add(flowInfo);
                    }
                }

                metadata.put("nodes", nodes);
                metadata.put("flows", flows);
                metadata.put("nodeCount", nodes.size());
                metadata.put("flowCount", flows.size());

                // 统计信息
                long startEventCount = nodes.stream().filter(n -> "StartEvent".equals(n.get("type"))).count();
                long endEventCount = nodes.stream().filter(n -> "EndEvent".equals(n.get("type"))).count();
                long userTaskCount = nodes.stream().filter(n -> "UserTask".equals(n.get("type"))).count();
                long gatewayCount = nodes.stream().filter(n -> n.get("type").toString().contains("Gateway")).count();

                metadata.put("startEventCount", startEventCount);
                metadata.put("endEventCount", endEventCount);
                metadata.put("userTaskCount", userTaskCount);
                metadata.put("gatewayCount", gatewayCount);
            }

            metadata.put("extractTime", LocalDateTime.now());
            return metadata;

        } catch (Exception e) {
            log.error("获取流程图元数据失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取流程图元数据失败: " + e.getMessage());
        }
    }
}
