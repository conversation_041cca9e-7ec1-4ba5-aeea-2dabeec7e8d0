package com.hzwangda.edu.workflow.enums;

/**
 * 任务状态枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum TaskStatus {

    // 基本任务状态
    PENDING("待处理", "任务等待处理"),
    CLAIMED("已认领", "任务已被认领"),
    IN_PROGRESS("处理中", "任务正在处理"),
    COMPLETED("已完成", "任务已完成"),
    CANCELLED("已取消", "任务已取消"),

    // 审批相关状态
    APPROVED("已通过", "任务审批通过"),
    REJECTED("已拒绝", "任务审批拒绝"),

    // 流转相关状态
    DELEGATED("已委派", "任务已委派给他人"),
    TRANSFERRED("已转办", "任务已转办给他人"),
    RETURNED("已退回", "任务已退回到上一节点"),
    JUMPED("已跳转", "任务已跳转到指定节点"),

    // 超时相关状态
    TIMEOUT("已超时", "任务处理超时"),
    ESCALATED("已升级", "任务已升级处理");

    private final String description;
    private final String detail;

    TaskStatus(String description, String detail) {
        this.description = description;
        this.detail = detail;
    }

    public String getDescription() {
        return description;
    }

    public String getDetail() {
        return detail;
    }

    public String getCode() {
        return this.name();
    }

    /**
     * 根据代码获取枚举
     */
    public static TaskStatus fromCode(String code) {
        for (TaskStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return PENDING;
    }

    /**
     * 判断是否为终态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == CANCELLED ||
               this == APPROVED || this == REJECTED;
    }

    /**
     * 判断是否为可操作状态（可以进行完成、委派等操作）
     */
    public boolean isOperableStatus() {
        return this == PENDING || this == CLAIMED || this == IN_PROGRESS;
    }

    /**
     * 判断是否为审批状态
     */
    public boolean isApprovalStatus() {
        return this == APPROVED || this == REJECTED;
    }

    /**
     * 判断是否为流转状态
     */
    public boolean isTransferStatus() {
        return this == DELEGATED || this == TRANSFERRED ||
               this == RETURNED || this == JUMPED;
    }
}
