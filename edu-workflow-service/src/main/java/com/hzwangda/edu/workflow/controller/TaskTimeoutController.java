package com.hzwangda.edu.workflow.controller;

import com.hzwangda.edu.common.result.Result;
import com.hzwangda.edu.workflow.service.TaskTimeoutService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.util.List;
import java.util.Map;

/**
 * 任务超时管理控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/task-timeout")
@Tag(name = "任务超时管理", description = "任务超时处理和配置管理功能")
@Validated
public class TaskTimeoutController {

    @Autowired
    private TaskTimeoutService taskTimeoutService;

    @Operation(summary = "检查并处理超时任务", description = "手动触发超时任务检查和处理")
    @PostMapping("/check-and-handle")
    public Result<Map<String, Object>> checkAndHandleTimeoutTasks() {
        log.info("手动触发超时任务检查和处理");

        Map<String, Object> result = taskTimeoutService.checkAndHandleTimeoutTasks();
        return Result.success(result);
    }

    @Operation(summary = "处理指定任务超时", description = "处理指定任务的超时情况")
    @PostMapping("/handle/{taskId}")
    public Result<Boolean> handleTaskTimeout(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        log.info("处理指定任务超时: {}", taskId);

        boolean result = taskTimeoutService.handleTaskTimeout(taskId);
        return Result.success(result);
    }

    @Operation(summary = "获取超时任务列表", description = "获取当前系统中的所有超时任务")
    @GetMapping("/timeout-tasks")
    public Result<List<Map<String, Object>>> getTimeoutTasks() {
        log.info("获取超时任务列表");

        List<Map<String, Object>> timeoutTasks = taskTimeoutService.getTimeoutTasks();
        return Result.success(timeoutTasks);
    }

    @Operation(summary = "设置任务超时配置", description = "为指定流程节点设置超时处理配置")
    @PostMapping("/config")
    public Result<Boolean> setTaskTimeoutConfig(
            @Parameter(description = "流程定义Key", required = true)
            @RequestParam @NotBlank(message = "流程定义Key不能为空") String processDefinitionKey,
            @Parameter(description = "活动节点ID", required = true)
            @RequestParam @NotBlank(message = "活动节点ID不能为空") String activityId,
            @Parameter(description = "超时时间（分钟）", required = true)
            @RequestParam @NotNull @Positive(message = "超时时间必须大于0") Integer timeoutMinutes,
            @Parameter(description = "超时处理动作", required = true)
            @RequestParam @NotBlank(message = "超时处理动作不能为空") String timeoutAction) {
        log.info("设置任务超时配置: processDefinitionKey={}, activityId={}, timeoutMinutes={}, timeoutAction={}",
                processDefinitionKey, activityId, timeoutMinutes, timeoutAction);

        boolean result = taskTimeoutService.setTaskTimeoutConfig(
                processDefinitionKey, activityId, timeoutMinutes, timeoutAction);
        return Result.success(result);
    }

    @Operation(summary = "获取任务超时配置", description = "获取指定流程节点的超时配置")
    @GetMapping("/config")
    public Result<Map<String, Object>> getTaskTimeoutConfig(
            @Parameter(description = "流程定义Key", required = true)
            @RequestParam @NotBlank(message = "流程定义Key不能为空") String processDefinitionKey,
            @Parameter(description = "活动节点ID", required = true)
            @RequestParam @NotBlank(message = "活动节点ID不能为空") String activityId) {
        log.info("获取任务超时配置: processDefinitionKey={}, activityId={}", processDefinitionKey, activityId);

        Map<String, Object> config = taskTimeoutService.getTaskTimeoutConfig(processDefinitionKey, activityId);
        return Result.success(config);
    }

    @Operation(summary = "删除任务超时配置", description = "删除指定流程节点的超时配置")
    @DeleteMapping("/config")
    public Result<Boolean> deleteTaskTimeoutConfig(
            @Parameter(description = "流程定义Key", required = true)
            @RequestParam @NotBlank(message = "流程定义Key不能为空") String processDefinitionKey,
            @Parameter(description = "活动节点ID", required = true)
            @RequestParam @NotBlank(message = "活动节点ID不能为空") String activityId) {
        log.info("删除任务超时配置: processDefinitionKey={}, activityId={}", processDefinitionKey, activityId);

        boolean result = taskTimeoutService.deleteTaskTimeoutConfig(processDefinitionKey, activityId);
        return Result.success(result);
    }

    @Operation(summary = "获取所有超时配置", description = "获取系统中所有的任务超时配置")
    @GetMapping("/configs")
    public Result<List<Map<String, Object>>> getAllTimeoutConfigs() {
        log.info("获取所有超时配置");

        List<Map<String, Object>> configs = taskTimeoutService.getAllTimeoutConfigs();
        return Result.success(configs);
    }

    @Operation(summary = "发送超时通知", description = "为指定任务发送超时通知")
    @PostMapping("/notification/{taskId}")
    public Result<Boolean> sendTimeoutNotification(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId,
            @Parameter(description = "通知类型", required = true)
            @RequestParam @NotBlank(message = "通知类型不能为空") String notificationType) {
        log.info("发送超时通知: taskId={}, notificationType={}", taskId, notificationType);

        boolean result = taskTimeoutService.sendTimeoutNotification(taskId, notificationType);
        return Result.success(result);
    }

    @Operation(summary = "任务超时升级", description = "将超时任务升级给其他用户处理")
    @PostMapping("/escalate/{taskId}")
    public Result<Boolean> escalateTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId,
            @Parameter(description = "升级到的用户ID", required = true)
            @RequestParam @NotBlank(message = "升级到的用户ID不能为空") String escalateToUserId) {
        log.info("任务超时升级: taskId={}, escalateToUserId={}", taskId, escalateToUserId);

        boolean result = taskTimeoutService.escalateTask(taskId, escalateToUserId);
        return Result.success(result);
    }

    @Operation(summary = "任务超时自动完成", description = "自动完成超时的任务")
    @PostMapping("/auto-complete/{taskId}")
    public Result<Boolean> autoCompleteTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId,
            @Parameter(description = "自动完成时的变量")
            @RequestBody(required = false) Map<String, Object> autoCompleteVariables) {
        log.info("任务超时自动完成: taskId={}", taskId);

        boolean result = taskTimeoutService.autoCompleteTask(taskId, autoCompleteVariables);
        return Result.success(result);
    }

    @Operation(summary = "启用超时监控", description = "启用任务超时监控功能")
    @PostMapping("/monitoring/enable")
    public Result<Void> enableTimeoutMonitoring() {
        log.info("启用任务超时监控");

        taskTimeoutService.enableTimeoutMonitoring();
        return Result.success();
    }

    @Operation(summary = "禁用超时监控", description = "禁用任务超时监控功能")
    @PostMapping("/monitoring/disable")
    public Result<Void> disableTimeoutMonitoring() {
        log.info("禁用任务超时监控");

        taskTimeoutService.disableTimeoutMonitoring();
        return Result.success();
    }

    @Operation(summary = "获取监控状态", description = "获取任务超时监控的启用状态")
    @GetMapping("/monitoring/status")
    public Result<Boolean> getTimeoutMonitoringStatus() {
        log.info("获取任务超时监控状态");

        boolean enabled = taskTimeoutService.isTimeoutMonitoringEnabled();
        return Result.success(enabled);
    }
}
