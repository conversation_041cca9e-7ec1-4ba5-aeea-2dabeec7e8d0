package com.hzwangda.edu.workflow.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hzwangda.edu.common.dto.PageResult;
import com.hzwangda.edu.common.exception.BusinessException;
import com.hzwangda.edu.common.result.ResultCode;
import com.hzwangda.edu.workflow.dto.ProcessInstanceResponse;
import com.hzwangda.edu.workflow.dto.ProcessStartRequest;
import com.hzwangda.edu.workflow.dto.TaskCompleteRequest;
import com.hzwangda.edu.workflow.dto.TaskResponse;
import com.hzwangda.edu.workflow.entity.ProcessInstanceInfo;
import com.hzwangda.edu.workflow.entity.TaskInfo;
import com.hzwangda.edu.workflow.enums.ApprovalResult;
import com.hzwangda.edu.workflow.enums.ProcessStatus;
import com.hzwangda.edu.workflow.repository.ProcessInstanceInfoRepository;
import com.hzwangda.edu.workflow.repository.TaskInfoRepository;
import com.hzwangda.edu.workflow.service.WorkflowService;
import com.hzwangda.edu.workflow.util.WorkflowMapper;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.*;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工作流服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Transactional
public class WorkflowServiceImpl implements WorkflowService {

    private static final Logger logger = LoggerFactory.getLogger(WorkflowServiceImpl.class);

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private ProcessEngine processEngine;

    @Autowired
    private ProcessInstanceInfoRepository processInstanceInfoRepository;

    @Autowired
    private TaskInfoRepository taskInfoRepository;

    @Autowired
    private WorkflowMapper workflowMapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String CACHE_KEY_PREFIX = "workflow:";
    private static final int CACHE_EXPIRE_HOURS = 1;

    @Override
    public ProcessInstanceResponse startProcess(ProcessStartRequest request) {
        logger.info("启动流程: 流程键={}, 发起人={}, 业务键={}",
                   request.getProcessKey(), request.getInitiatorUsername(), request.getBusinessKey());

        try {
            // 验证流程定义是否存在
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionKey(request.getProcessKey())
                    .active()
                    .latestVersion()
                    .singleResult();

            if (processDefinition == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "流程定义不存在或未激活: " + request.getProcessKey());
            }

            // 检查业务键是否已存在
            if (StringUtils.isNotBlank(request.getBusinessKey()) &&
                processInstanceInfoRepository.existsByBusinessKeyAndDeletedFalse(request.getBusinessKey())) {
                throw new BusinessException(ResultCode.BUSINESS_ERROR, "业务键已存在: " + request.getBusinessKey());
            }

            // 准备流程变量
            Map<String, Object> variables = new HashMap<>();
            if (request.getVariables() != null) {
                variables.putAll(request.getVariables());
            }

            // 设置发起人信息
            variables.put("initiatorId", request.getInitiatorId());
            variables.put("initiatorUsername", request.getInitiatorUsername());
            variables.put("initiatorName", request.getInitiatorName());
            variables.put("businessType", request.getBusinessType());
            variables.put("businessId", request.getBusinessId());

            // 启动流程实例
            ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(
                    request.getProcessKey(),
                    request.getBusinessKey(),
                    variables
            );

            // 保存流程实例信息
            ProcessInstanceInfo instanceInfo = createProcessInstanceInfo(processInstance, request);
            instanceInfo = processInstanceInfoRepository.save(instanceInfo);

            // 处理第一个用户任务
            if (!request.getSkipFirstUserTask()) {
                handleFirstUserTask(processInstance.getId(), request);
            }

            logger.info("流程启动成功: 流程实例ID={}", processInstance.getId());
            return workflowMapper.toProcessInstanceResponse(instanceInfo);

        } catch (Exception e) {
            logger.error("启动流程失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "启动流程失败: " + e.getMessage());
        }
    }

    @Override
    public TaskResponse completeTask(TaskCompleteRequest request) {
        logger.info("完成任务: 任务ID={}, 处理人={}, 审批结果={}",
                   request.getTaskId(), request.getAssigneeUsername(), request.getApprovalResult());

        try {
            // 查询Flowable任务
            Task task = taskService.createTaskQuery()
                    .taskId(request.getTaskId())
                    .singleResult();

            if (task == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "任务不存在: " + request.getTaskId());
            }

            // 查询任务信息
            TaskInfo taskInfo = taskInfoRepository.findByFlowableTaskIdAndDeletedFalse(request.getTaskId())
                    .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "任务信息不存在"));

            // 验证任务状态
            if (!ProcessStatus.PENDING.getCode().equals(taskInfo.getStatus())) {
                throw new BusinessException(ResultCode.BUSINESS_ERROR, "任务状态不允许完成: " + taskInfo.getStatus());
            }

            // 验证处理权限
            if (!canCompleteTask(task, request.getAssigneeUsername())) {
                throw new BusinessException(ResultCode.FORBIDDEN, "无权限完成此任务");
            }

            // 处理不同的审批结果
            ApprovalResult approvalResult = ApprovalResult.fromCode(request.getApprovalResult());
            switch (approvalResult) {
                case APPROVED:
                case REJECTED:
                    completeTaskWithResult(task, taskInfo, request);
                    break;
                case DELEGATED:
                    delegateTaskInternal(task, taskInfo, request.getTargetUser(), request.getDelegateReason());
                    break;
                case TRANSFERRED:
                    transferTaskInternal(task, taskInfo, request.getTargetUser(), request.getDelegateReason());
                    break;
                case RETURNED:
                    returnTask(task, taskInfo, request);
                    break;
                default:
                    throw new BusinessException(ResultCode.BUSINESS_ERROR, "不支持的审批结果: " + request.getApprovalResult());
            }

            // 更新任务信息
            updateTaskInfo(taskInfo, request);
            taskInfo = taskInfoRepository.save(taskInfo);

            // 更新流程实例信息
            updateProcessInstanceInfo(task.getProcessInstanceId());

            logger.info("任务完成成功: 任务ID={}", request.getTaskId());
            return workflowMapper.toTaskResponse(taskInfo);

        } catch (Exception e) {
            logger.error("完成任务失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "完成任务失败: " + e.getMessage());
        }
    }

    @Override
    public TaskResponse claimTask(String taskId, Long assigneeId, String assigneeUsername) {
        logger.info("认领任务: 任务ID={}, 认领人={}", taskId, assigneeUsername);

        try {
            // 查询Flowable任务
            Task task = taskService.createTaskQuery()
                    .taskId(taskId)
                    .singleResult();

            if (task == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "任务不存在: " + taskId);
            }

            // 验证任务是否可以认领
            if (task.getAssignee() != null) {
                throw new BusinessException(ResultCode.BUSINESS_ERROR, "任务已被认领");
            }

            // 认领任务
            taskService.claim(taskId, assigneeUsername);

            // 更新任务信息
            TaskInfo taskInfo = taskInfoRepository.findByFlowableTaskIdAndDeletedFalse(taskId)
                    .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "任务信息不存在"));

            taskInfo.setAssigneeId(assigneeId);
            taskInfo.setAssigneeUsername(assigneeUsername);
            taskInfo.setClaimTime(LocalDateTime.now());
            taskInfo.setStatus(ProcessStatus.CLAIMED.getCode());

            taskInfo = taskInfoRepository.save(taskInfo);

            logger.info("任务认领成功: 任务ID={}", taskId);
            return workflowMapper.toTaskResponse(taskInfo);

        } catch (Exception e) {
            logger.error("认领任务失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "认领任务失败: " + e.getMessage());
        }
    }

    @Override
    public TaskResponse delegateTask(String taskId, String targetUser, String reason) {
        logger.info("委派任务: 任务ID={}, 目标用户={}", taskId, targetUser);

        try {
            Task task = taskService.createTaskQuery()
                    .taskId(taskId)
                    .singleResult();

            if (task == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "任务不存在: " + taskId);
            }

            TaskInfo taskInfo = taskInfoRepository.findByFlowableTaskIdAndDeletedFalse(taskId)
                    .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "任务信息不存在"));

            delegateTaskInternal(task, taskInfo, targetUser, reason);

            taskInfo = taskInfoRepository.save(taskInfo);

            logger.info("任务委派成功: 任务ID={}", taskId);
            return workflowMapper.toTaskResponse(taskInfo);

        } catch (Exception e) {
            logger.error("委派任务失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "委派任务失败: " + e.getMessage());
        }
    }

    @Override
    public TaskResponse transferTask(String taskId, String targetUser, String reason) {
        logger.info("转办任务: 任务ID={}, 目标用户={}", taskId, targetUser);

        try {
            Task task = taskService.createTaskQuery()
                    .taskId(taskId)
                    .singleResult();

            if (task == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "任务不存在: " + taskId);
            }

            TaskInfo taskInfo = taskInfoRepository.findByFlowableTaskIdAndDeletedFalse(taskId)
                    .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "任务信息不存在"));

            transferTaskInternal(task, taskInfo, targetUser, reason);

            taskInfo = taskInfoRepository.save(taskInfo);

            logger.info("任务转办成功: 任务ID={}", taskId);
            return workflowMapper.toTaskResponse(taskInfo);

        } catch (Exception e) {
            logger.error("转办任务失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "转办任务失败: " + e.getMessage());
        }
    }

    /**
     * 创建流程实例信息
     */
    private ProcessInstanceInfo createProcessInstanceInfo(ProcessInstance processInstance, ProcessStartRequest request) {
        ProcessInstanceInfo instanceInfo = new ProcessInstanceInfo();

        instanceInfo.setFlowableInstanceId(processInstance.getId());
        instanceInfo.setProcessKey(processInstance.getProcessDefinitionKey());
        instanceInfo.setProcessName(processInstance.getProcessDefinitionName());
        instanceInfo.setBusinessKey(processInstance.getBusinessKey());
        instanceInfo.setBusinessType(request.getBusinessType());
        instanceInfo.setBusinessId(request.getBusinessId());
        instanceInfo.setInitiatorId(request.getInitiatorId());
        instanceInfo.setInitiatorUsername(request.getInitiatorUsername());
        instanceInfo.setInitiatorName(request.getInitiatorName());
        instanceInfo.setStartTime(LocalDateTime.now());
        instanceInfo.setStatus(ProcessStatus.RUNNING.getCode());
        instanceInfo.setTitle(request.getTitle());
        instanceInfo.setDescription(request.getDescription());
        instanceInfo.setPriority(request.getPriority());

        // 序列化变量
        if (request.getVariables() != null && !request.getVariables().isEmpty()) {
            try {
                instanceInfo.setVariables(objectMapper.writeValueAsString(request.getVariables()));
            } catch (JsonProcessingException e) {
                logger.warn("序列化流程变量失败: {}", e.getMessage());
            }
        }

        return instanceInfo;
    }

    /**
     * 处理第一个用户任务
     */
    private void handleFirstUserTask(String processInstanceId, ProcessStartRequest request) {
        // 查询第一个用户任务
        List<Task> tasks = taskService.createTaskQuery()
                .processInstanceId(processInstanceId)
                .list();

        for (Task task : tasks) {
            // 创建任务信息
            TaskInfo taskInfo = createTaskInfo(task, request);
            taskInfoRepository.save(taskInfo);
        }
    }

    /**
     * 创建任务信息
     */
    private TaskInfo createTaskInfo(Task task, ProcessStartRequest request) {
        TaskInfo taskInfo = new TaskInfo();

        taskInfo.setFlowableTaskId(task.getId());
        taskInfo.setProcessInstanceId(task.getProcessInstanceId());
        taskInfo.setProcessKey(request.getProcessKey());
        taskInfo.setTaskKey(task.getTaskDefinitionKey());
        taskInfo.setTaskName(task.getName());
        taskInfo.setTaskDescription(task.getDescription());
        taskInfo.setAssigneeUsername(task.getAssignee());
        taskInfo.setTaskCreateTime(task.getCreateTime() != null ?
                                  task.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() :
                                  LocalDateTime.now());
        taskInfo.setDueDate(task.getDueDate() != null ?
                           task.getDueDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() :
                           null);
        taskInfo.setStatus(ProcessStatus.PENDING.getCode());
        taskInfo.setPriority(task.getPriority());
        taskInfo.setBusinessKey(request.getBusinessKey());
        taskInfo.setBusinessType(request.getBusinessType());

        return taskInfo;
    }

    /**
     * 验证是否可以完成任务
     */
    private boolean canCompleteTask(Task task, String username) {
        // 如果任务已指派，检查是否为指派人
        if (task.getAssignee() != null) {
            return task.getAssignee().equals(username);
        }

        // 如果任务未指派，检查是否为候选人
        List<String> candidateUsers = taskService.createTaskQuery()
                .taskId(task.getId())
                .taskCandidateUser(username)
                .list()
                .stream()
                .map(Task::getId)
                .collect(Collectors.toList());

        return !candidateUsers.isEmpty();
    }

    /**
     * 完成任务并设置结果
     */
    private void completeTaskWithResult(Task task, TaskInfo taskInfo, TaskCompleteRequest request) {
        // 设置任务变量
        Map<String, Object> variables = new HashMap<>();
        if (request.getVariables() != null) {
            variables.putAll(request.getVariables());
        }

        // 设置审批结果变量
        variables.put("approvalResult", request.getApprovalResult());
        variables.put("approvalComment", request.getApprovalComment());
        variables.put("approver", request.getAssigneeUsername());
        variables.put("approveTime", LocalDateTime.now());

        // 完成任务
        taskService.complete(task.getId(), variables);
    }

    /**
     * 委派任务内部方法
     */
    private void delegateTaskInternal(Task task, TaskInfo taskInfo, String targetUser, String reason) {
        taskService.delegateTask(task.getId(), targetUser);

        taskInfo.setStatus(ProcessStatus.DELEGATED.getCode());
        taskInfo.setApprovalResult(ApprovalResult.DELEGATED.getCode());
        taskInfo.setApprovalComment(reason);
    }

    /**
     * 转办任务内部方法
     */
    private void transferTaskInternal(Task task, TaskInfo taskInfo, String targetUser, String reason) {
        taskService.setAssignee(task.getId(), targetUser);

        taskInfo.setAssigneeUsername(targetUser);
        taskInfo.setStatus(ProcessStatus.TRANSFERRED.getCode());
        taskInfo.setApprovalResult(ApprovalResult.TRANSFERRED.getCode());
        taskInfo.setApprovalComment(reason);
    }

    /**
     * 退回任务
     */
    private void returnTask(Task task, TaskInfo taskInfo, TaskCompleteRequest request) {
        // 设置退回变量
        Map<String, Object> variables = new HashMap<>();
        variables.put("approvalResult", ApprovalResult.RETURNED.getCode());
        variables.put("approvalComment", request.getApprovalComment());
        variables.put("returnReason", request.getApprovalComment());

        // 完成当前任务，流程会根据退回逻辑流转
        taskService.complete(task.getId(), variables);
    }

    /**
     * 更新任务信息
     */
    private void updateTaskInfo(TaskInfo taskInfo, TaskCompleteRequest request) {
        taskInfo.setAssigneeId(request.getAssigneeId());
        taskInfo.setAssigneeUsername(request.getAssigneeUsername());
        taskInfo.setAssigneeName(request.getAssigneeName());
        taskInfo.setCompleteTime(LocalDateTime.now());
        taskInfo.setApprovalResult(request.getApprovalResult());
        taskInfo.setApprovalComment(request.getApprovalComment());

        // 计算处理时长
        if (taskInfo.getTaskCreateTime() != null) {
            long duration = java.time.Duration.between(taskInfo.getTaskCreateTime(), LocalDateTime.now()).toMillis();
            taskInfo.setDuration(duration);
        }

        // 设置最终状态
        ApprovalResult result = ApprovalResult.fromCode(request.getApprovalResult());
        if (result.isFinalResult()) {
            taskInfo.setStatus(request.getApprovalResult());
        }

        // 序列化表单数据和变量
        if (request.getFormData() != null) {
            try {
                taskInfo.setFormData(objectMapper.writeValueAsString(request.getFormData()));
            } catch (JsonProcessingException e) {
                logger.warn("序列化表单数据失败: {}", e.getMessage());
            }
        }

        if (request.getVariables() != null) {
            try {
                taskInfo.setVariables(objectMapper.writeValueAsString(request.getVariables()));
            } catch (JsonProcessingException e) {
                logger.warn("序列化任务变量失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 更新流程实例信息
     */
    private void updateProcessInstanceInfo(String processInstanceId) {
        ProcessInstanceInfo instanceInfo = processInstanceInfoRepository
                .findByFlowableInstanceIdAndDeletedFalse(processInstanceId)
                .orElse(null);

        if (instanceInfo != null) {
            // 检查流程是否已结束
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();

            if (processInstance == null) {
                // 流程已结束，查询历史
                HistoricProcessInstance historicInstance = historyService.createHistoricProcessInstanceQuery()
                        .processInstanceId(processInstanceId)
                        .singleResult();

                if (historicInstance != null) {
                    instanceInfo.setEndTime(historicInstance.getEndTime() != null ?
                                          historicInstance.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() :
                                          LocalDateTime.now());
                    instanceInfo.setStatus(ProcessStatus.COMPLETED.getCode());
                    instanceInfo.setEndReason(historicInstance.getDeleteReason());

                    if (instanceInfo.getStartTime() != null && instanceInfo.getEndTime() != null) {
                        long duration = java.time.Duration.between(instanceInfo.getStartTime(), instanceInfo.getEndTime()).toMillis();
                        instanceInfo.setDuration(duration);
                    }
                }
            } else {
                // 更新当前活动信息
                updateCurrentActivity(instanceInfo, processInstanceId);
            }

            processInstanceInfoRepository.save(instanceInfo);
        }
    }

    /**
     * 更新当前活动信息
     */
    private void updateCurrentActivity(ProcessInstanceInfo instanceInfo, String processInstanceId) {
        List<Task> activeTasks = taskService.createTaskQuery()
                .processInstanceId(processInstanceId)
                .list();

        if (!activeTasks.isEmpty()) {
            Task currentTask = activeTasks.get(0);
            instanceInfo.setCurrentActivity(currentTask.getTaskDefinitionKey());
            instanceInfo.setCurrentActivityName(currentTask.getName());
        }
    }

    @Override
    public ProcessInstanceResponse terminateProcess(String processInstanceId, String reason) {
        logger.info("终止流程实例: 流程实例ID={}, 原因={}", processInstanceId, reason);

        try {
            // 删除流程实例
            runtimeService.deleteProcessInstance(processInstanceId, reason);

            // 更新流程实例信息
            ProcessInstanceInfo instanceInfo = processInstanceInfoRepository
                    .findByFlowableInstanceIdAndDeletedFalse(processInstanceId)
                    .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "流程实例不存在"));

            instanceInfo.setEndTime(LocalDateTime.now());
            instanceInfo.setStatus(ProcessStatus.TERMINATED.getCode());
            instanceInfo.setEndReason(reason);

            if (instanceInfo.getStartTime() != null) {
                long duration = java.time.Duration.between(instanceInfo.getStartTime(), instanceInfo.getEndTime()).toMillis();
                instanceInfo.setDuration(duration);
            }

            instanceInfo = processInstanceInfoRepository.save(instanceInfo);

            logger.info("流程实例终止成功: 流程实例ID={}", processInstanceId);
            return workflowMapper.toProcessInstanceResponse(instanceInfo);

        } catch (Exception e) {
            logger.error("终止流程实例失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "终止流程实例失败: " + e.getMessage());
        }
    }

    @Override
    public ProcessInstanceResponse suspendProcess(String processInstanceId) {
        logger.info("挂起流程实例: 流程实例ID={}", processInstanceId);

        try {
            runtimeService.suspendProcessInstanceById(processInstanceId);

            ProcessInstanceInfo instanceInfo = processInstanceInfoRepository
                    .findByFlowableInstanceIdAndDeletedFalse(processInstanceId)
                    .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "流程实例不存在"));

            instanceInfo.setStatus(ProcessStatus.SUSPENDED.getCode());
            instanceInfo = processInstanceInfoRepository.save(instanceInfo);

            logger.info("流程实例挂起成功: 流程实例ID={}", processInstanceId);
            return workflowMapper.toProcessInstanceResponse(instanceInfo);

        } catch (Exception e) {
            logger.error("挂起流程实例失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "挂起流程实例失败: " + e.getMessage());
        }
    }

    @Override
    public ProcessInstanceResponse activateProcess(String processInstanceId) {
        logger.info("激活流程实例: 流程实例ID={}", processInstanceId);

        try {
            runtimeService.activateProcessInstanceById(processInstanceId);

            ProcessInstanceInfo instanceInfo = processInstanceInfoRepository
                    .findByFlowableInstanceIdAndDeletedFalse(processInstanceId)
                    .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "流程实例不存在"));

            instanceInfo.setStatus(ProcessStatus.RUNNING.getCode());
            instanceInfo = processInstanceInfoRepository.save(instanceInfo);

            logger.info("流程实例激活成功: 流程实例ID={}", processInstanceId);
            return workflowMapper.toProcessInstanceResponse(instanceInfo);

        } catch (Exception e) {
            logger.error("激活流程实例失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "激活流程实例失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public ProcessInstanceResponse getProcessInstance(String processInstanceId) {
        logger.debug("查询流程实例: 流程实例ID={}", processInstanceId);

        ProcessInstanceInfo instanceInfo = processInstanceInfoRepository
                .findByFlowableInstanceIdAndDeletedFalse(processInstanceId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "流程实例不存在"));

        return workflowMapper.toProcessInstanceResponse(instanceInfo);
    }

    @Override
    @Transactional(readOnly = true)
    public ProcessInstanceResponse getProcessInstanceByBusinessKey(String businessKey) {
        logger.debug("根据业务键查询流程实例: 业务键={}", businessKey);

        ProcessInstanceInfo instanceInfo = processInstanceInfoRepository
                .findByBusinessKeyAndDeletedFalse(businessKey)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "流程实例不存在"));

        return workflowMapper.toProcessInstanceResponse(instanceInfo);
    }

    @Override
    @Transactional(readOnly = true)
    public TaskResponse getTask(String taskId) {
        logger.debug("查询任务: 任务ID={}", taskId);

        TaskInfo taskInfo = taskInfoRepository.findByFlowableTaskIdAndDeletedFalse(taskId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "任务不存在"));

        return workflowMapper.toTaskResponse(taskInfo);
    }

    @Override
    @Transactional(readOnly = true)
    public PageResult<TaskResponse> getPendingTasks(String username, Integer page, Integer size) {
        logger.debug("查询用户待办任务: 用户名={}, 页码={}, 大小={}", username, page, size);

        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "priority")
                .and(Sort.by(Sort.Direction.ASC, "taskCreateTime")));

        Page<TaskInfo> taskPage = taskInfoRepository.findByAssigneeUsernameAndStatusAndDeletedFalse(
                username, ProcessStatus.PENDING.getCode(), pageable);

        List<TaskResponse> responses = taskPage.getContent().stream()
                .map(workflowMapper::toTaskResponse)
                .collect(Collectors.toList());

        return new PageResult<>(responses, taskPage.getTotalElements(), page, size);
    }

    @Override
    @Transactional(readOnly = true)
    public PageResult<TaskResponse> getCompletedTasks(String username, Integer page, Integer size) {
        logger.debug("查询用户已办任务: 用户名={}, 页码={}, 大小={}", username, page, size);

        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "completeTime"));

        Page<TaskInfo> taskPage = taskInfoRepository.findHistoryTasksByUser(username, pageable);

        List<TaskResponse> responses = taskPage.getContent().stream()
                .map(workflowMapper::toTaskResponse)
                .collect(Collectors.toList());

        return new PageResult<>(responses, taskPage.getTotalElements(), page, size);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TaskResponse> getProcessTaskHistory(String processInstanceId) {
        logger.debug("查询流程任务历史: 流程实例ID={}", processInstanceId);

        List<TaskInfo> tasks = taskInfoRepository.findByProcessInstanceIdAndDeletedFalseOrderByTaskCreateTimeAsc(processInstanceId);

        return tasks.stream()
                .map(workflowMapper::toTaskResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getProcessActivityHistory(String processInstanceId) {
        logger.debug("查询流程活动历史: 流程实例ID={}", processInstanceId);

        List<HistoricActivityInstance> activities = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(processInstanceId)
                .orderByHistoricActivityInstanceStartTime()
                .asc()
                .list();

        return activities.stream()
                .map(this::convertActivityToMap)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public byte[] getProcessDiagram(String processInstanceId) {
        logger.debug("获取流程图: 流程实例ID={}", processInstanceId);

        try {
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();

            if (processInstance == null) {
                // 查询历史流程实例
                HistoricProcessInstance historicInstance = historyService.createHistoricProcessInstanceQuery()
                        .processInstanceId(processInstanceId)
                        .singleResult();

                if (historicInstance == null) {
                    throw new BusinessException(ResultCode.NOT_FOUND, "流程实例不存在");
                }

                return getProcessDefinitionDiagram(historicInstance.getProcessDefinitionId());
            }

            // 获取活动的任务节点
            List<String> activeActivityIds = runtimeService.getActiveActivityIds(processInstanceId);

            // 获取流程定义
            ProcessDefinition processDefinition = repositoryService.getProcessDefinition(processInstance.getProcessDefinitionId());

            // 获取流程图
            InputStream diagramStream = repositoryService.getProcessDiagram(processDefinition.getId());

            if (diagramStream != null) {
                return diagramStream.readAllBytes();
            }

            return new byte[0];

        } catch (Exception e) {
            logger.error("获取流程图失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取流程图失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public byte[] getProcessDefinitionDiagram(String processDefinitionId) {
        logger.debug("获取流程定义图: 流程定义ID={}", processDefinitionId);

        try {
            InputStream diagramStream = repositoryService.getProcessDiagram(processDefinitionId);

            if (diagramStream != null) {
                return diagramStream.readAllBytes();
            }

            return new byte[0];

        } catch (Exception e) {
            logger.error("获取流程定义图失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取流程定义图失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public PageResult<ProcessInstanceResponse> getProcessInstancesByInitiator(Long initiatorId, Integer page, Integer size) {
        logger.debug("查询用户发起的流程实例: 发起人ID={}, 页码={}, 大小={}", initiatorId, page, size);

        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "startTime"));

        Page<ProcessInstanceInfo> instancePage = processInstanceInfoRepository.findByInitiatorIdAndDeletedFalse(initiatorId, pageable);

        List<ProcessInstanceResponse> responses = instancePage.getContent().stream()
                .map(workflowMapper::toProcessInstanceResponse)
                .collect(Collectors.toList());

        return new PageResult<>(responses, instancePage.getTotalElements(), page, size);
    }

    @Override
    @Transactional(readOnly = true)
    public PageResult<ProcessInstanceResponse> getProcessInstancesByKey(String processKey, Integer page, Integer size) {
        logger.debug("查询指定流程键的流程实例: 流程键={}, 页码={}, 大小={}", processKey, page, size);

        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "startTime"));

        Page<ProcessInstanceInfo> instancePage = processInstanceInfoRepository.findByProcessKeyAndDeletedFalse(processKey, pageable);

        List<ProcessInstanceResponse> responses = instancePage.getContent().stream()
                .map(workflowMapper::toProcessInstanceResponse)
                .collect(Collectors.toList());

        return new PageResult<>(responses, instancePage.getTotalElements(), page, size);
    }

    @Override
    public void setProcessVariables(String processInstanceId, Map<String, Object> variables) {
        logger.debug("设置流程变量: 流程实例ID={}", processInstanceId);

        try {
            runtimeService.setVariables(processInstanceId, variables);

            // 更新流程实例信息中的变量
            ProcessInstanceInfo instanceInfo = processInstanceInfoRepository
                    .findByFlowableInstanceIdAndDeletedFalse(processInstanceId)
                    .orElse(null);

            if (instanceInfo != null) {
                try {
                    instanceInfo.setVariables(objectMapper.writeValueAsString(variables));
                    processInstanceInfoRepository.save(instanceInfo);
                } catch (JsonProcessingException e) {
                    logger.warn("序列化流程变量失败: {}", e.getMessage());
                }
            }

        } catch (Exception e) {
            logger.error("设置流程变量失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "设置流程变量失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getProcessVariables(String processInstanceId) {
        logger.debug("获取流程变量: 流程实例ID={}", processInstanceId);

        try {
            return runtimeService.getVariables(processInstanceId);
        } catch (Exception e) {
            logger.error("获取流程变量失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取流程变量失败: " + e.getMessage());
        }
    }

    @Override
    public void setTaskVariables(String taskId, Map<String, Object> variables) {
        logger.debug("设置任务变量: 任务ID={}", taskId);

        try {
            taskService.setVariables(taskId, variables);

            // 更新任务信息中的变量
            TaskInfo taskInfo = taskInfoRepository.findByFlowableTaskIdAndDeletedFalse(taskId)
                    .orElse(null);

            if (taskInfo != null) {
                try {
                    taskInfo.setVariables(objectMapper.writeValueAsString(variables));
                    taskInfoRepository.save(taskInfo);
                } catch (JsonProcessingException e) {
                    logger.warn("序列化任务变量失败: {}", e.getMessage());
                }
            }

        } catch (Exception e) {
            logger.error("设置任务变量失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "设置任务变量失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getTaskVariables(String taskId) {
        logger.debug("获取任务变量: 任务ID={}", taskId);

        try {
            return taskService.getVariables(taskId);
        } catch (Exception e) {
            logger.error("获取任务变量失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取任务变量失败: " + e.getMessage());
        }
    }

    /**
     * 转换活动实例为Map
     */
    private Map<String, Object> convertActivityToMap(HistoricActivityInstance activity) {
        Map<String, Object> activityMap = new HashMap<>();
        activityMap.put("id", activity.getId());
        activityMap.put("activityId", activity.getActivityId());
        activityMap.put("activityName", activity.getActivityName());
        activityMap.put("activityType", activity.getActivityType());
        activityMap.put("assignee", activity.getAssignee());
        activityMap.put("startTime", activity.getStartTime());
        activityMap.put("endTime", activity.getEndTime());
        activityMap.put("durationInMillis", activity.getDurationInMillis());
        activityMap.put("deleteReason", activity.getDeleteReason());
        return activityMap;
    }

    // ==================== 复杂审批流程增强功能实现 ====================

    @Override
    @Transactional
    public TaskResponse returnTask(String taskId, String targetNodeId, String reason) {
        logger.info("退回任务: 任务ID={}, 目标节点={}, 原因={}", taskId, targetNodeId, reason);

        try {
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "任务不存在: " + taskId);
            }

            TaskInfo taskInfo = taskInfoRepository.findByFlowableTaskIdAndDeletedFalse(taskId)
                    .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "任务信息不存在"));

            // 验证目标节点是否可达
            if (!isNodeReachable(task.getProcessInstanceId(), targetNodeId)) {
                throw new BusinessException(ResultCode.BUSINESS_ERROR, "无法退回到指定节点: " + targetNodeId);
            }

            // 使用Flowable的动态跳转功能
            runtimeService.createChangeActivityStateBuilder()
                    .processInstanceId(task.getProcessInstanceId())
                    .moveActivityIdTo(task.getTaskDefinitionKey(), targetNodeId)
                    .changeState();

            // 添加退回记录
            taskService.addComment(taskId, task.getProcessInstanceId(),
                    String.format("任务退回到节点[%s]: %s", targetNodeId, reason));

            // 更新任务状态
            taskInfo.setStatus(ProcessStatus.RETURNED.getCode());
            taskInfo.setApprovalComment(reason);
            taskInfo.setCompleteTime(LocalDateTime.now());
            taskInfo = taskInfoRepository.save(taskInfo);

            logger.info("任务退回成功: 任务ID={}", taskId);
            return workflowMapper.toTaskResponse(taskInfo);

        } catch (Exception e) {
            logger.error("退回任务失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "退回任务失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public TaskResponse jumpTask(String taskId, String targetNodeId, String reason) {
        logger.info("跳转任务: 任务ID={}, 目标节点={}, 原因={}", taskId, targetNodeId, reason);

        try {
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "任务不存在: " + taskId);
            }

            TaskInfo taskInfo = taskInfoRepository.findByFlowableTaskIdAndDeletedFalse(taskId)
                    .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "任务信息不存在"));

            // 验证目标节点存在
            if (!isValidNode(task.getProcessDefinitionId(), targetNodeId)) {
                throw new BusinessException(ResultCode.BUSINESS_ERROR, "目标节点不存在: " + targetNodeId);
            }

            // 执行跳转
            runtimeService.createChangeActivityStateBuilder()
                    .processInstanceId(task.getProcessInstanceId())
                    .moveActivityIdTo(task.getTaskDefinitionKey(), targetNodeId)
                    .changeState();

            // 添加跳转记录
            taskService.addComment(taskId, task.getProcessInstanceId(),
                    String.format("任务跳转到节点[%s]: %s", targetNodeId, reason));

            // 更新任务状态
            taskInfo.setStatus(ProcessStatus.JUMPED.getCode());
            taskInfo.setApprovalComment(reason);
            taskInfo.setCompleteTime(LocalDateTime.now());
            taskInfo = taskInfoRepository.save(taskInfo);

            logger.info("任务跳转成功: 任务ID={}", taskId);
            return workflowMapper.toTaskResponse(taskInfo);

        } catch (Exception e) {
            logger.error("跳转任务失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "跳转任务失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public List<TaskResponse> batchAssignTask(String taskId, List<String> assignees, String reason) {
        logger.info("批量分配任务: 任务ID={}, 分配用户数={}", taskId, assignees.size());

        try {
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "任务不存在: " + taskId);
            }

            List<TaskResponse> responses = new ArrayList<>();

            // 为每个用户创建子任务
            for (String assignee : assignees) {
                Task subTask = taskService.newTask();
                subTask.setParentTaskId(taskId);
                subTask.setName(task.getName() + " - 分配给 " + assignee);
                subTask.setDescription(task.getDescription());
                subTask.setAssignee(assignee);
                // 注意：在Flowable 7.1中，这些属性需要通过其他方式设置
                // subTask.setProcessInstanceId(task.getProcessInstanceId());
                // subTask.setProcessDefinitionId(task.getProcessDefinitionId());
                // subTask.setTaskDefinitionKey(task.getTaskDefinitionKey());

                taskService.saveTask(subTask);

                // 创建任务信息记录
                TaskInfo subTaskInfo = new TaskInfo();
                subTaskInfo.setFlowableTaskId(subTask.getId());
                subTaskInfo.setTaskName(subTask.getName());
                subTaskInfo.setTaskKey(subTask.getTaskDefinitionKey());
                subTaskInfo.setProcessInstanceId(task.getProcessInstanceId());
                subTaskInfo.setAssigneeId(getUserIdByUsername(assignee));
                subTaskInfo.setAssigneeUsername(assignee);
                subTaskInfo.setStatus(ProcessStatus.PENDING.getCode());
                subTaskInfo.setCreateTime(LocalDateTime.now());
                subTaskInfo.setDeleted(false);

                subTaskInfo = taskInfoRepository.save(subTaskInfo);
                responses.add(workflowMapper.toTaskResponse(subTaskInfo));
            }

            // 添加批量分配记录
            taskService.addComment(taskId, task.getProcessInstanceId(),
                    String.format("任务批量分配给用户: %s, 原因: %s", String.join(",", assignees), reason));

            logger.info("任务批量分配成功: 任务ID={}, 创建子任务数={}", taskId, responses.size());
            return responses;

        } catch (Exception e) {
            logger.error("批量分配任务失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "批量分配任务失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public TaskResponse addSignTask(String taskId, String signUser, String signType, String reason) {
        logger.info("加签任务: 任务ID={}, 加签用户={}, 加签类型={}", taskId, signUser, signType);

        try {
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "任务不存在: " + taskId);
            }

            TaskInfo taskInfo = taskInfoRepository.findByFlowableTaskIdAndDeletedFalse(taskId)
                    .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "任务信息不存在"));

            switch (signType.toLowerCase()) {
                case "before":
                    // 前加签：在当前任务前插入新任务
                    addSignBefore(task, signUser, reason);
                    break;
                case "after":
                    // 后加签：在当前任务后插入新任务
                    addSignAfter(task, signUser, reason);
                    break;
                case "parallel":
                    // 并行加签：创建并行任务
                    addSignParallel(task, signUser, reason);
                    break;
                default:
                    throw new BusinessException(ResultCode.BUSINESS_ERROR, "不支持的加签类型: " + signType);
            }

            // 添加加签记录
            taskService.addComment(taskId, task.getProcessInstanceId(),
                    String.format("任务加签[%s]: 用户=%s, 原因=%s", signType, signUser, reason));

            // 更新任务状态
            taskInfo.setApprovalComment("已加签: " + reason);
            taskInfo = taskInfoRepository.save(taskInfo);

            logger.info("任务加签成功: 任务ID={}", taskId);
            return workflowMapper.toTaskResponse(taskInfo);

        } catch (Exception e) {
            logger.error("加签任务失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "加签任务失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public TaskResponse reduceSignTask(String taskId, String signUser, String reason) {
        logger.info("减签任务: 任务ID={}, 减签用户={}, 原因={}", taskId, signUser, reason);

        try {
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "任务不存在: " + taskId);
            }

            // 查找要减签的子任务
            List<Task> subTasks = taskService.createTaskQuery()
                    .taskAssignee(signUser)
                    .processInstanceId(task.getProcessInstanceId())
                    .list();

            for (Task subTask : subTasks) {
                if (subTask.getParentTaskId() != null && subTask.getParentTaskId().equals(taskId)) {
                    // 删除子任务
                    taskService.deleteTask(subTask.getId(), reason);

                    // 更新任务信息状态
                    TaskInfo subTaskInfo = taskInfoRepository.findByFlowableTaskIdAndDeletedFalse(subTask.getId())
                            .orElse(null);
                    if (subTaskInfo != null) {
                        subTaskInfo.setStatus(ProcessStatus.CANCELLED.getCode());
                        subTaskInfo.setApprovalComment("减签删除: " + reason);
                        subTaskInfo.setCompleteTime(LocalDateTime.now());
                        taskInfoRepository.save(subTaskInfo);
                    }
                }
            }

            // 添加减签记录
            taskService.addComment(taskId, task.getProcessInstanceId(),
                    String.format("任务减签: 用户=%s, 原因=%s", signUser, reason));

            TaskInfo taskInfo = taskInfoRepository.findByFlowableTaskIdAndDeletedFalse(taskId)
                    .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "任务信息不存在"));

            taskInfo.setApprovalComment("已减签: " + reason);
            taskInfo = taskInfoRepository.save(taskInfo);

            logger.info("任务减签成功: 任务ID={}", taskId);
            return workflowMapper.toTaskResponse(taskInfo);

        } catch (Exception e) {
            logger.error("减签任务失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "减签任务失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getAvailableNodes(String taskId) {
        logger.debug("获取可跳转节点: 任务ID={}", taskId);

        try {
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "任务不存在: " + taskId);
            }

            // 获取流程定义
            ProcessDefinition processDefinition = repositoryService.getProcessDefinition(task.getProcessDefinitionId());

            // 获取BPMN模型
            org.flowable.bpmn.model.BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());

            List<Map<String, Object>> availableNodes = new ArrayList<>();

            if (!bpmnModel.getProcesses().isEmpty()) {
                org.flowable.bpmn.model.Process process = bpmnModel.getProcesses().get(0);

                // 获取所有用户任务节点
                for (org.flowable.bpmn.model.FlowElement element : process.getFlowElements()) {
                    if (element instanceof org.flowable.bpmn.model.UserTask) {
                        Map<String, Object> nodeInfo = new HashMap<>();
                        nodeInfo.put("id", element.getId());
                        nodeInfo.put("name", element.getName());
                        nodeInfo.put("type", "UserTask");
                        nodeInfo.put("canJump", true);
                        nodeInfo.put("canReturn", isNodeReachable(task.getProcessInstanceId(), element.getId()));
                        availableNodes.add(nodeInfo);
                    }
                }
            }

            return availableNodes;

        } catch (Exception e) {
            logger.error("获取可跳转节点失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取可跳转节点失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ProcessInstanceResponse rollbackProcess(String processInstanceId, String targetNodeId, String reason) {
        logger.info("流程回滚: 流程实例ID={}, 目标节点={}, 原因={}", processInstanceId, targetNodeId, reason);

        try {
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();

            if (processInstance == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "流程实例不存在: " + processInstanceId);
            }

            // 获取当前活动节点
            List<String> activeActivityIds = runtimeService.getActiveActivityIds(processInstanceId);

            // 执行回滚
            runtimeService.createChangeActivityStateBuilder()
                    .processInstanceId(processInstanceId)
                    .moveActivityIdsToSingleActivityId(activeActivityIds, targetNodeId)
                    .changeState();

            // 添加回滚记录
            taskService.addComment(null, processInstanceId,
                    String.format("流程回滚到节点[%s]: %s", targetNodeId, reason));

            // 更新流程实例信息
            ProcessInstanceInfo instanceInfo = processInstanceInfoRepository
                    .findByFlowableInstanceIdAndDeletedFalse(processInstanceId)
                    .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "流程实例信息不存在"));

            instanceInfo.setCurrentActivity(targetNodeId);
            instanceInfo = processInstanceInfoRepository.save(instanceInfo);

            logger.info("流程回滚成功: 流程实例ID={}", processInstanceId);
            return workflowMapper.toProcessInstanceResponse(instanceInfo);

        } catch (Exception e) {
            logger.error("流程回滚失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "流程回滚失败: " + e.getMessage());
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 检查节点是否可达（用于退回验证）
     */
    private boolean isNodeReachable(String processInstanceId, String targetNodeId) {
        try {
            // 获取流程历史活动
            List<HistoricActivityInstance> historicActivities = historyService
                    .createHistoricActivityInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .activityId(targetNodeId)
                    .list();

            // 如果历史中存在该节点，则可以退回
            return !historicActivities.isEmpty();
        } catch (Exception e) {
            logger.warn("检查节点可达性失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证节点是否存在于流程定义中
     */
    private boolean isValidNode(String processDefinitionId, String nodeId) {
        try {
            org.flowable.bpmn.model.BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);

            if (!bpmnModel.getProcesses().isEmpty()) {
                org.flowable.bpmn.model.Process process = bpmnModel.getProcesses().get(0);

                for (org.flowable.bpmn.model.FlowElement element : process.getFlowElements()) {
                    if (element.getId().equals(nodeId)) {
                        return true;
                    }
                }
            }

            return false;
        } catch (Exception e) {
            logger.warn("验证节点存在性失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 根据用户名获取用户ID（模拟方法，实际应该调用用户服务）
     */
    private Long getUserIdByUsername(String username) {
        // TODO: 实际应该调用用户服务获取用户ID
        // 这里返回一个模拟值
        return (long) username.hashCode();
    }

    /**
     * 前加签实现
     */
    private void addSignBefore(Task task, String signUser, String reason) {
        // 创建前加签任务
        Task signTask = taskService.newTask();
        signTask.setName("前加签: " + task.getName());
        signTask.setDescription("前加签任务: " + reason);
        signTask.setAssignee(signUser);
        // 注意：在Flowable 7.1中，这些属性需要通过其他方式设置
        // signTask.setProcessInstanceId(task.getProcessInstanceId());
        // signTask.setProcessDefinitionId(task.getProcessDefinitionId());
        // signTask.setTaskDefinitionKey(task.getTaskDefinitionKey() + "_before_sign");
        signTask.setParentTaskId(task.getId());

        taskService.saveTask(signTask);

        // 挂起原任务 - 在Flowable 7.1中需要提供原因
        taskService.suspendTask(task.getId(), "前加签挂起");

        logger.info("创建前加签任务: 原任务={}, 加签任务={}, 加签用户={}",
                   task.getId(), signTask.getId(), signUser);
    }

    /**
     * 后加签实现
     */
    private void addSignAfter(Task task, String signUser, String reason) {
        // 设置任务变量，标记需要后加签
        Map<String, Object> variables = new HashMap<>();
        variables.put("afterSignUser", signUser);
        variables.put("afterSignReason", reason);
        variables.put("needAfterSign", true);

        taskService.setVariables(task.getId(), variables);

        logger.info("设置后加签标记: 任务={}, 加签用户={}", task.getId(), signUser);
    }

    /**
     * 并行加签实现
     */
    private void addSignParallel(Task task, String signUser, String reason) {
        // 创建并行加签任务
        Task parallelTask = taskService.newTask();
        parallelTask.setName("并行加签: " + task.getName());
        parallelTask.setDescription("并行加签任务: " + reason);
        parallelTask.setAssignee(signUser);
        // 注意：在Flowable 7.1中，这些属性需要通过其他方式设置
        // parallelTask.setProcessInstanceId(task.getProcessInstanceId());
        // parallelTask.setProcessDefinitionId(task.getProcessDefinitionId());
        // parallelTask.setTaskDefinitionKey(task.getTaskDefinitionKey() + "_parallel_sign");
        parallelTask.setParentTaskId(task.getId());

        taskService.saveTask(parallelTask);

        // 创建任务信息记录
        TaskInfo parallelTaskInfo = new TaskInfo();
        parallelTaskInfo.setFlowableTaskId(parallelTask.getId());
        parallelTaskInfo.setTaskName(parallelTask.getName());
        parallelTaskInfo.setTaskKey(parallelTask.getTaskDefinitionKey());
        parallelTaskInfo.setProcessInstanceId(task.getProcessInstanceId());
        parallelTaskInfo.setAssigneeId(getUserIdByUsername(signUser));
        parallelTaskInfo.setAssigneeUsername(signUser);
        parallelTaskInfo.setStatus(ProcessStatus.PENDING.getCode());
        parallelTaskInfo.setCreateTime(LocalDateTime.now());
        parallelTaskInfo.setDeleted(false);

        taskInfoRepository.save(parallelTaskInfo);

        logger.info("创建并行加签任务: 原任务={}, 并行任务={}, 加签用户={}",
                   task.getId(), parallelTask.getId(), signUser);
    }
}
