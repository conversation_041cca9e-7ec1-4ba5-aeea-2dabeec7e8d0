package com.hzwangda.edu.workflow.service.impl;

import com.hzwangda.edu.common.exception.BusinessException;
import com.hzwangda.edu.common.result.ResultCode;
import com.hzwangda.edu.workflow.service.ProcessVisualizationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.image.ProcessDiagramGenerator;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 流程可视化服务实现
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProcessVisualizationServiceImpl implements ProcessVisualizationService {

    private final RepositoryService repositoryService;
    private final RuntimeService runtimeService;
    private final HistoryService historyService;
    private final ProcessDiagramGenerator processDiagramGenerator;

    @Override
    public String getProcessDiagramHtml(String processDefinitionId, boolean showGrid, boolean readonly) {
        log.info("生成流程图HTML: processDefinitionId={}, showGrid={}, readonly={}",
                processDefinitionId, showGrid, readonly);

        try {
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(processDefinitionId)
                    .singleResult();

            if (processDefinition == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "流程定义不存在: " + processDefinitionId);
            }

            // 获取BPMN XML
            InputStream inputStream = repositoryService.getResourceAsStream(
                    processDefinition.getDeploymentId(), processDefinition.getResourceName());

            String bpmnXml = new String(inputStream.readAllBytes(), "UTF-8");

            return generateBpmnViewerHtml(bpmnXml, processDefinition.getName(), showGrid, readonly);

        } catch (Exception e) {
            log.error("生成流程图HTML失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "生成流程图HTML失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getProcessDiagramJson(String processDefinitionId) {
        log.info("获取流程图JSON数据: {}", processDefinitionId);

        try {
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(processDefinitionId)
                    .singleResult();

            if (processDefinition == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "流程定义不存在: " + processDefinitionId);
            }

            // 获取BPMN模型
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);

            Map<String, Object> result = new HashMap<>();
            result.put("processDefinitionId", processDefinitionId);
            result.put("processDefinitionKey", processDefinition.getKey());
            result.put("processDefinitionName", processDefinition.getName());
            result.put("version", processDefinition.getVersion());

            // 获取BPMN XML
            InputStream inputStream = repositoryService.getResourceAsStream(
                    processDefinition.getDeploymentId(), processDefinition.getResourceName());
            result.put("bpmnXml", new String(inputStream.readAllBytes(), "UTF-8"));

            // 获取流程元素信息
            result.put("elements", extractProcessElements(bpmnModel));

            return result;

        } catch (Exception e) {
            log.error("获取流程图JSON数据失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取流程图JSON数据失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getProcessInstanceDiagram(String processInstanceId) {
        log.info("获取流程实例状态图: {}", processInstanceId);

        try {
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();

            if (processInstance == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "流程实例不存在: " + processInstanceId);
            }

            // 获取流程定义
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(processInstance.getProcessDefinitionId())
                    .singleResult();

            // 获取活动节点
            List<String> activeActivityIds = runtimeService.getActiveActivityIds(processInstanceId);

            // 获取已完成的活动节点
            List<String> completedActivityIds = getCompletedActivityIds(processInstanceId);

            Map<String, Object> result = new HashMap<>();
            result.put("processInstanceId", processInstanceId);
            result.put("processDefinitionId", processInstance.getProcessDefinitionId());
            result.put("processDefinitionKey", processDefinition.getKey());
            result.put("processDefinitionName", processDefinition.getName());
            result.put("activeActivityIds", activeActivityIds);
            result.put("completedActivityIds", completedActivityIds);
            result.put("startTime", processInstance.getStartTime());
            result.put("suspended", processInstance.isSuspended());

            // 获取BPMN XML
            InputStream inputStream = repositoryService.getResourceAsStream(
                    processDefinition.getDeploymentId(), processDefinition.getResourceName());
            result.put("bpmnXml", new String(inputStream.readAllBytes(), "UTF-8"));

            return result;

        } catch (Exception e) {
            log.error("获取流程实例状态图失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取流程实例状态图失败: " + e.getMessage());
        }
    }

    @Override
    public String getProcessEditor(String processDefinitionId, String template) {
        log.info("获取流程编辑器: processDefinitionId={}, template={}", processDefinitionId, template);

        try {
            String bpmnXml;
            String processName = "新建流程";

            if (StringUtils.hasText(processDefinitionId)) {
                // 编辑现有流程
                ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                        .processDefinitionId(processDefinitionId)
                        .singleResult();

                if (processDefinition == null) {
                    throw new BusinessException(ResultCode.NOT_FOUND, "流程定义不存在: " + processDefinitionId);
                }

                InputStream inputStream = repositoryService.getResourceAsStream(
                        processDefinition.getDeploymentId(), processDefinition.getResourceName());
                bpmnXml = new String(inputStream.readAllBytes(), "UTF-8");
                processName = processDefinition.getName();
            } else {
                // 创建新流程，使用模板
                bpmnXml = generateTemplateXml(template);
            }

            return generateBpmnEditorHtml(bpmnXml, processName);

        } catch (Exception e) {
            log.error("获取流程编辑器失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取流程编辑器失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> saveEditedProcess(String bpmnXml, String processName, String category, String description) {
        log.info("保存编辑的流程: processName={}, category={}", processName, category);

        try {
            // 验证BPMN XML
            Map<String, Object> validationResult = validateProcess(bpmnXml);
            if (!(Boolean) validationResult.get("valid")) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "BPMN格式错误: " + validationResult.get("error"));
            }

            // 部署流程
            String deploymentName = processName + "_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));

            org.flowable.engine.repository.Deployment deployment = repositoryService.createDeployment()
                    .name(deploymentName)
                    .category(category)
                    .addString(processName + ".bpmn", bpmnXml)
                    .deploy();

            // 获取部署的流程定义
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .deploymentId(deployment.getId())
                    .singleResult();

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("deploymentId", deployment.getId());
            result.put("processDefinitionId", processDefinition.getId());
            result.put("processDefinitionKey", processDefinition.getKey());
            result.put("version", processDefinition.getVersion());
            result.put("deploymentTime", LocalDateTime.now());

            log.info("流程保存成功: deploymentId={}, processDefinitionId={}",
                    deployment.getId(), processDefinition.getId());

            return result;

        } catch (Exception e) {
            log.error("保存编辑的流程失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "保存编辑的流程失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> validateProcess(String bpmnXml) {
        log.info("验证流程定义");

        Map<String, Object> result = new HashMap<>();

        try {
            // 基本格式验证
            if (!StringUtils.hasText(bpmnXml) || !bpmnXml.contains("<?xml") || !bpmnXml.contains("bpmn")) {
                result.put("valid", false);
                result.put("error", "不是有效的BPMN文件格式");
                return result;
            }

            // 尝试解析BPMN模型
            BpmnXMLConverter converter = new BpmnXMLConverter();
            BpmnModel bpmnModel = converter.convertToBpmnModel(
                    () -> new ByteArrayInputStream(bpmnXml.getBytes(java.nio.charset.StandardCharsets.UTF_8)), false, false);

            if (bpmnModel.getProcesses().isEmpty()) {
                result.put("valid", false);
                result.put("error", "BPMN文件中没有找到流程定义");
                return result;
            }

            // 验证流程定义的基本要素
            org.flowable.bpmn.model.Process process = bpmnModel.getProcesses().get(0);
            if (!StringUtils.hasText(process.getId())) {
                result.put("valid", false);
                result.put("error", "流程定义缺少ID");
                return result;
            }

            // 验证开始事件和结束事件
            boolean hasStartEvent = process.getFlowElements().stream()
                    .anyMatch(element -> element instanceof org.flowable.bpmn.model.StartEvent);
            boolean hasEndEvent = process.getFlowElements().stream()
                    .anyMatch(element -> element instanceof org.flowable.bpmn.model.EndEvent);

            if (!hasStartEvent) {
                result.put("valid", false);
                result.put("error", "流程定义缺少开始事件");
                return result;
            }

            if (!hasEndEvent) {
                result.put("valid", false);
                result.put("error", "流程定义缺少结束事件");
                return result;
            }

            result.put("valid", true);
            result.put("processId", process.getId());
            result.put("processName", process.getName());
            result.put("elementCount", process.getFlowElements().size());
            result.put("validationTime", LocalDateTime.now());

        } catch (Exception e) {
            result.put("valid", false);
            result.put("error", "BPMN文件解析失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public byte[] exportProcessDiagramAsPng(String processDefinitionId, int width, int height) {
        log.info("导出流程图PNG: processDefinitionId={}, width={}, height={}",
                processDefinitionId, width, height);

        try {
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);

            // 生成流程图
            InputStream inputStream = processDiagramGenerator.generateDiagram(
                    bpmnModel, "png", Collections.emptyList(), Collections.emptyList(), true);

            return inputStream.readAllBytes();

        } catch (Exception e) {
            log.error("导出流程图PNG失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "导出流程图PNG失败: " + e.getMessage());
        }
    }

    @Override
    public String exportProcessDiagramAsSvg(String processDefinitionId) {
        log.info("导出流程图SVG: {}", processDefinitionId);

        try {
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);

            // 生成SVG流程图
            InputStream inputStream = processDiagramGenerator.generateDiagram(
                    bpmnModel, "svg", Collections.emptyList(), Collections.emptyList(), true);

            return new String(inputStream.readAllBytes(), "UTF-8");

        } catch (Exception e) {
            log.error("导出流程图SVG失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "导出流程图SVG失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getProcessStatisticsChart(String processDefinitionId, int days) {
        log.info("获取流程统计图表数据: processDefinitionId={}, days={}", processDefinitionId, days);

        try {
            Map<String, Object> result = new HashMap<>();

            // 这里应该实现具体的统计逻辑
            // 由于篇幅限制，这里提供基本结构
            result.put("processDefinitionId", processDefinitionId);
            result.put("days", days);
            result.put("totalInstances", 0);
            result.put("completedInstances", 0);
            result.put("activeInstances", 0);
            result.put("chartData", new ArrayList<>());

            return result;

        } catch (Exception e) {
            log.error("获取流程统计图表数据失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取流程统计图表数据失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getProcessHeatmap(String processDefinitionId, int days) {
        log.info("获取流程热力图数据: processDefinitionId={}, days={}", processDefinitionId, days);

        try {
            Map<String, Object> result = new HashMap<>();

            // 这里应该实现具体的热力图数据生成逻辑
            result.put("processDefinitionId", processDefinitionId);
            result.put("days", days);
            result.put("heatmapData", new HashMap<>());

            return result;

        } catch (Exception e) {
            log.error("获取流程热力图数据失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取流程热力图数据失败: " + e.getMessage());
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 提取流程元素信息
     */
    private Map<String, Object> extractProcessElements(BpmnModel bpmnModel) {
        Map<String, Object> elements = new HashMap<>();

        if (!bpmnModel.getProcesses().isEmpty()) {
            org.flowable.bpmn.model.Process process = bpmnModel.getProcesses().get(0);

            List<Map<String, Object>> flowElements = new ArrayList<>();
            process.getFlowElements().forEach(element -> {
                Map<String, Object> elementInfo = new HashMap<>();
                elementInfo.put("id", element.getId());
                elementInfo.put("name", element.getName());
                elementInfo.put("type", element.getClass().getSimpleName());
                flowElements.add(elementInfo);
            });

            elements.put("flowElements", flowElements);
            elements.put("elementCount", flowElements.size());
        }

        return elements;
    }

    /**
     * 获取已完成的活动节点ID列表
     */
    private List<String> getCompletedActivityIds(String processInstanceId) {
        // 这里应该查询历史活动实例
        // 由于篇幅限制，返回空列表
        return new ArrayList<>();
    }

    /**
     * 生成模板XML
     */
    private String generateTemplateXml(String template) {
        switch (template.toLowerCase()) {
            case "approval":
                return generateApprovalTemplate();
            case "sequential":
                return generateSequentialTemplate();
            default:
                return generateBasicTemplate();
        }
    }

    /**
     * 生成基础模板
     */
    private String generateBasicTemplate() {
        return """
                <?xml version="1.0" encoding="UTF-8"?>
                <definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
                           xmlns:flowable="http://flowable.org/bpmn"
                           targetNamespace="http://www.flowable.org/processdef">

                  <process id="newProcess" name="新建流程" isExecutable="true">
                    <startEvent id="startEvent" name="开始"/>
                    <userTask id="userTask" name="用户任务" flowable:assignee="${assignee}"/>
                    <endEvent id="endEvent" name="结束"/>

                    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="userTask"/>
                    <sequenceFlow id="flow2" sourceRef="userTask" targetRef="endEvent"/>
                  </process>

                </definitions>
                """;
    }

    /**
     * 生成审批模板
     */
    private String generateApprovalTemplate() {
        return """
                <?xml version="1.0" encoding="UTF-8"?>
                <definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
                           xmlns:flowable="http://flowable.org/bpmn"
                           targetNamespace="http://www.flowable.org/processdef">

                  <process id="approvalProcess" name="审批流程" isExecutable="true">
                    <startEvent id="startEvent" name="提交申请"/>
                    <userTask id="managerApproval" name="经理审批" flowable:assignee="${manager}"/>
                    <exclusiveGateway id="decision" name="审批决策"/>
                    <userTask id="hrApproval" name="HR审批" flowable:assignee="${hr}"/>
                    <endEvent id="approvedEnd" name="审批通过"/>
                    <endEvent id="rejectedEnd" name="审批拒绝"/>

                    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="managerApproval"/>
                    <sequenceFlow id="flow2" sourceRef="managerApproval" targetRef="decision"/>
                    <sequenceFlow id="flow3" sourceRef="decision" targetRef="hrApproval"/>
                    <sequenceFlow id="flow4" sourceRef="decision" targetRef="rejectedEnd"/>
                    <sequenceFlow id="flow5" sourceRef="hrApproval" targetRef="approvedEnd"/>
                  </process>

                </definitions>
                """;
    }

    /**
     * 生成顺序模板
     */
    private String generateSequentialTemplate() {
        return """
                <?xml version="1.0" encoding="UTF-8"?>
                <definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
                           xmlns:flowable="http://flowable.org/bpmn"
                           targetNamespace="http://www.flowable.org/processdef">

                  <process id="sequentialProcess" name="顺序流程" isExecutable="true">
                    <startEvent id="startEvent" name="开始"/>
                    <userTask id="task1" name="任务1" flowable:assignee="${user1}"/>
                    <userTask id="task2" name="任务2" flowable:assignee="${user2}"/>
                    <userTask id="task3" name="任务3" flowable:assignee="${user3}"/>
                    <endEvent id="endEvent" name="结束"/>

                    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="task1"/>
                    <sequenceFlow id="flow2" sourceRef="task1" targetRef="task2"/>
                    <sequenceFlow id="flow3" sourceRef="task2" targetRef="task3"/>
                    <sequenceFlow id="flow4" sourceRef="task3" targetRef="endEvent"/>
                  </process>

                </definitions>
                """;
    }

    /**
     * 生成BPMN查看器HTML
     */
    private String generateBpmnViewerHtml(String bpmnXml, String processName, boolean showGrid, boolean readonly) {
        return String.format("""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>%s - 流程图查看器</title>
                    <script src="https://unpkg.com/bpmn-js@latest/dist/bpmn-viewer.development.js"></script>
                    <style>
                        body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
                        #canvas { height: 100vh; }
                        .toolbar { padding: 10px; background: #f5f5f5; border-bottom: 1px solid #ddd; }
                    </style>
                </head>
                <body>
                    <div class="toolbar">
                        <h3>%s</h3>
                        <button onclick="zoomIn()">放大</button>
                        <button onclick="zoomOut()">缩小</button>
                        <button onclick="zoomFit()">适应窗口</button>
                    </div>
                    <div id="canvas"></div>

                    <script>
                        var viewer = new BpmnJS({ container: '#canvas' });
                        var bpmnXML = `%s`;

                        viewer.importXML(bpmnXML).then(function(result) {
                            var canvas = viewer.get('canvas');
                            canvas.zoom('fit-viewport');
                        }).catch(function(err) {
                            console.error('导入BPMN失败:', err);
                        });

                        function zoomIn() { viewer.get('canvas').zoom(1.2); }
                        function zoomOut() { viewer.get('canvas').zoom(0.8); }
                        function zoomFit() { viewer.get('canvas').zoom('fit-viewport'); }
                    </script>
                </body>
                </html>
                """, processName, processName, bpmnXml.replace("`", "\\`"));
    }

    /**
     * 生成BPMN编辑器HTML
     */
    private String generateBpmnEditorHtml(String bpmnXml, String processName) {
        return String.format("""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>%s - 流程编辑器</title>
                    <script src="https://unpkg.com/bpmn-js@latest/dist/bpmn-modeler.development.js"></script>
                    <style>
                        body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
                        #canvas { height: calc(100vh - 60px); }
                        .toolbar { padding: 10px; background: #f5f5f5; border-bottom: 1px solid #ddd; }
                        button { margin-right: 10px; padding: 5px 10px; }
                    </style>
                </head>
                <body>
                    <div class="toolbar">
                        <h3>%s - 编辑器</h3>
                        <button onclick="saveProcess()">保存</button>
                        <button onclick="validateProcess()">验证</button>
                        <button onclick="exportXML()">导出XML</button>
                        <button onclick="zoomFit()">适应窗口</button>
                    </div>
                    <div id="canvas"></div>

                    <script>
                        var modeler = new BpmnJS({ container: '#canvas' });
                        var bpmnXML = `%s`;

                        modeler.importXML(bpmnXML).then(function(result) {
                            var canvas = modeler.get('canvas');
                            canvas.zoom('fit-viewport');
                        }).catch(function(err) {
                            console.error('导入BPMN失败:', err);
                        });

                        function saveProcess() {
                            modeler.saveXML({ format: true }).then(function(result) {
                                // 这里可以调用保存API
                                console.log('保存XML:', result.xml);
                                alert('保存功能需要与后端API集成');
                            });
                        }

                        function validateProcess() {
                            modeler.saveXML({ format: true }).then(function(result) {
                                // 这里可以调用验证API
                                console.log('验证XML:', result.xml);
                                alert('验证功能需要与后端API集成');
                            });
                        }

                        function exportXML() {
                            modeler.saveXML({ format: true }).then(function(result) {
                                var blob = new Blob([result.xml], { type: 'application/xml' });
                                var url = URL.createObjectURL(blob);
                                var a = document.createElement('a');
                                a.href = url;
                                a.download = '%s.bpmn';
                                a.click();
                            });
                        }

                        function zoomFit() { modeler.get('canvas').zoom('fit-viewport'); }
                    </script>
                </body>
                </html>
                """, processName, processName, bpmnXml.replace("`", "\\`"), processName);
    }
}
