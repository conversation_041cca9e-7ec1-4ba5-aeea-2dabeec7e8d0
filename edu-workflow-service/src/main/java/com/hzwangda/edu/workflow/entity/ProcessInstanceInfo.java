package com.hzwangda.edu.workflow.entity;

import com.hzwangda.edu.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 流程实例信息实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@Entity
@Table(name = "wf_process_instance_info", indexes = {
    @Index(name = "idx_process_instance_id", columnList = "flowable_instance_id"),
    @Index(name = "idx_process_key", columnList = "process_key"),
    @Index(name = "idx_business_key", columnList = "business_key"),
    @Index(name = "idx_initiator", columnList = "initiator_id"),
    @Index(name = "idx_pii_status", columnList = "status"),
    @Index(name = "idx_start_time", columnList = "start_time")
})
@Schema(description = "流程实例信息")
public class ProcessInstanceInfo extends BaseEntity {

    @NotBlank(message = "Flowable流程实例ID不能为空")
    @Size(max = 64, message = "Flowable流程实例ID长度不能超过64个字符")
    @Column(name = "flowable_instance_id", length = 64, nullable = false, unique = true)
    @Schema(description = "Flowable流程实例ID")
    private String flowableInstanceId;

    @NotBlank(message = "流程键不能为空")
    @Size(max = 64, message = "流程键长度不能超过64个字符")
    @Column(name = "process_key", length = 64, nullable = false)
    @Schema(description = "流程键", example = "leave_approval")
    private String processKey;

    @NotBlank(message = "流程名称不能为空")
    @Size(max = 128, message = "流程名称长度不能超过128个字符")
    @Column(name = "process_name", length = 128, nullable = false)
    @Schema(description = "流程名称", example = "请假审批流程")
    private String processName;

    @Size(max = 64, message = "业务键长度不能超过64个字符")
    @Column(name = "business_key", length = 64)
    @Schema(description = "业务键", example = "LEAVE_20241219_001")
    private String businessKey;

    @Size(max = 64, message = "业务类型长度不能超过64个字符")
    @Column(name = "business_type", length = 64)
    @Schema(description = "业务类型", example = "LEAVE_APPLICATION")
    private String businessType;

    @Size(max = 64, message = "业务ID长度不能超过64个字符")
    @Column(name = "business_id", length = 64)
    @Schema(description = "业务ID", example = "123")
    private String businessId;

    @NotNull(message = "发起人ID不能为空")
    @Column(name = "initiator_id", nullable = false)
    @Schema(description = "发起人ID", example = "1")
    private Long initiatorId;

    @NotBlank(message = "发起人用户名不能为空")
    @Size(max = 64, message = "发起人用户名长度不能超过64个字符")
    @Column(name = "initiator_username", length = 64, nullable = false)
    @Schema(description = "发起人用户名", example = "zhangsan")
    private String initiatorUsername;

    @Size(max = 64, message = "发起人姓名长度不能超过64个字符")
    @Column(name = "initiator_name", length = 64)
    @Schema(description = "发起人姓名", example = "张三")
    private String initiatorName;

    @NotNull(message = "开始时间不能为空")
    @Column(name = "start_time", nullable = false)
    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Column(name = "end_time")
    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @NotBlank(message = "状态不能为空")
    @Size(max = 20, message = "状态长度不能超过20个字符")
    @Column(name = "status", length = 20, nullable = false)
    @Schema(description = "状态", example = "RUNNING")
    private String status;

    @Size(max = 64, message = "当前节点长度不能超过64个字符")
    @Column(name = "current_activity", length = 64)
    @Schema(description = "当前节点", example = "manager_approval")
    private String currentActivity;

    @Size(max = 128, message = "当前节点名称长度不能超过128个字符")
    @Column(name = "current_activity_name", length = 128)
    @Schema(description = "当前节点名称", example = "部门经理审批")
    private String currentActivityName;

    @Size(max = 255, message = "标题长度不能超过255个字符")
    @Column(name = "title", length = 255)
    @Schema(description = "流程标题", example = "张三的请假申请")
    private String title;

    @Size(max = 500, message = "描述长度不能超过500个字符")
    @Column(name = "description", length = 500)
    @Schema(description = "流程描述")
    private String description;

    @Column(name = "priority")
    @Schema(description = "优先级", example = "50")
    private Integer priority = 50;

    @Column(name = "duration")
    @Schema(description = "持续时间(毫秒)")
    private Long duration;

    @Column(name = "variables", columnDefinition = "TEXT")
    @Schema(description = "流程变量JSON")
    private String variables;

    @Size(max = 500, message = "结束原因长度不能超过500个字符")
    @Column(name = "end_reason", length = 500)
    @Schema(description = "结束原因")
    private String endReason;

    // 构造函数
    public ProcessInstanceInfo() {
    }

    public ProcessInstanceInfo(String flowableInstanceId, String processKey, String processName,
                              Long initiatorId, String initiatorUsername) {
        this.flowableInstanceId = flowableInstanceId;
        this.processKey = processKey;
        this.processName = processName;
        this.initiatorId = initiatorId;
        this.initiatorUsername = initiatorUsername;
        this.startTime = LocalDateTime.now();
        this.status = "RUNNING";
    }
}
