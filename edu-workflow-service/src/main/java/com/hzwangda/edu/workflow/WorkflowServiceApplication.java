package com.hzwangda.edu.workflow;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.boot.autoconfigure.domain.EntityScan;

/**
 * 工作流服务启动类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = {"com.hzwangda.edu.workflow", "com.hzwangda.edu.common"})
@EnableJpaAuditing
@EnableJpaRepositories(basePackages = "com.hzwangda.edu.workflow.repository")
@EntityScan(basePackages = {"com.hzwangda.edu.workflow.entity"})
public class WorkflowServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(WorkflowServiceApplication.class, args);
    }
}
