<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.hzwangda.edu</groupId>
        <artifactId>edu-common-services</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>edu-workflow-service</artifactId>
    <packaging>jar</packaging>
    <name>工作流服务</name>
    <description>基于Flowable 7.1的工作流引擎服务，提供流程建模、执行、任务管理和监控功能</description>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <flowable.version>7.1.0</flowable.version>
    </properties>

    <dependencies>
        <!-- 依赖核心模块 -->
        <dependency>
            <groupId>com.hzwangda.edu</groupId>
            <artifactId>edu-common-web</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Flowable 7.1 工作流引擎 -->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter</artifactId>
        </dependency>

        <!-- Flowable UI 依赖在7.0.1版本中已移除，使用基础引擎即可 -->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-engine</artifactId>
            <version>${flowable.version}</version>
        </dependency>

        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-bpmn-model</artifactId>
            <version>${flowable.version}</version>
        </dependency>

        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-image-generator</artifactId>
            <version>${flowable.version}</version>
        </dependency>

        <!-- Flowable Task Service -->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-task-service</artifactId>
            <version>${flowable.version}</version>
        </dependency>

        <!-- Flowable History Service -->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-engine-common</artifactId>
            <version>${flowable.version}</version>
        </dependency>

        <!-- nacos注册中心 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- 数据库 -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>

        <!-- Apache POI for Excel export -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.4.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.4.1</version>
        </dependency>

        <!-- Test Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>postgresql</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Flowable测试依赖暂时移除，使用标准Spring Boot测试 -->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>HzWangdaRepo</id>
            <url>https://mvnrepository.hzwangda.com/repository/maven-public/</url>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>HzWangdaRepo</id>
            <url>https://mvnrepository.hzwangda.com/repository/maven-public/</url>
        </pluginRepository>
    </pluginRepositories>
</project>
