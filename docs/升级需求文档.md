该服务为支撑业务系统的通用基础服务，技术栈为：
### 技术栈
- Java 17, Spring Boot 3.4.7, Spring Cloud 2024.0.1
- 服务注册中心: Nacos 2023.0.3.3
- 数据库: PostgreSQL 15+
- 缓存: Redis 7+
- 对象存储: MinIO
- 工作流引擎: Flowable 7.1.0
- 分布式事务: Seata 2.4.0

包含的服务列表：
### 服务和端口
- edu-auth-service (8001) - 基于 CAS 的认证和授权
- edu-audit-service (8002) - 审计日志和合规跟踪
- edu-dictionary-service (8003) - 系统字典和配置管理
- edu-file-service (8004) - 基于 MinIO 对象存储的文件管理
- edu-notification-service (8005) - 消息通知服务（邮件、短信、微信、钉钉、WebSocket）
- edu-workflow-service (8006) - 使用 Flowable 的业务流程管理

升级需求：
1. 把缺少服务发现和注册的服务修改下，增加Nacos服务注册配置，可以参考edu-file-service服务
2. 服务间调用可能使用硬编码URL，升级参考以下改进方案，不合理的地方再优化下
**改进方案：**
```java
// 1. 添加Feign客户端
@FeignClient(name = "edu-auth-service", 
             fallback = AuthServiceFallback.class)
public interface AuthServiceClient {
    @GetMapping("/api/auth/user")
    Result<UserDTO> getCurrentUser();
}

// 2. 实现降级处理
@Component
public class AuthServiceFallback implements AuthServiceClient {
    @Override
    public Result<UserDTO> getCurrentUser() {
        return Result.error("认证服务暂时不可用");
    }
}

// 3. 配置熔断器
@Configuration
public class HystrixConfig {
    @Bean
    public HystrixCommandAspect hystrixCommandAspect() {
        return new HystrixCommandAspect();
    }
}
```
3. 消息通知服务按照以下设计模式优化下提高代码灵活性
**1. 工厂模式应用**
```java
// 通知渠道工厂
public interface NotificationChannelFactory {
    NotificationChannel createChannel(ChannelType type);
}

@Component
public class NotificationChannelFactoryImpl implements NotificationChannelFactory {
    private final Map<ChannelType, NotificationChannel> channels;
    
    @Override
    public NotificationChannel createChannel(ChannelType type) {
        return channels.get(type);
    }
}
```

**2. 模板方法模式**
```java
// 审批流程模板
public abstract class ApprovalProcessTemplate {
    
    public final void process(ApprovalRequest request) {
        validateRequest(request);
        checkPermissions(request);
        executeApproval(request);
        notifyStakeholders(request);
        recordAuditLog(request);
    }
    
    protected abstract void validateRequest(ApprovalRequest request);
    protected abstract void executeApproval(ApprovalRequest request);
    
    // 通用实现
    private void checkPermissions(ApprovalRequest request) { }
    private void notifyStakeholders(ApprovalRequest request) { }
    private void recordAuditLog(ApprovalRequest request) { }
}
```
4. 检查解决下N+1查询问题
5. 帮改进下ID生成器IdWorker的并发性能
6. 帮集成下链路追踪组件Zipkin
7. 移除SqlInjectionProtector，使用参数化查询
8. 帮扩展下认证授权服务edu-auth-service，再支持OAuth2.0
9. 以下开发支撑的实现，目的是为了提高上层业务开发人员的开发效率，你看还能做下什么帮优化下:
- 代码生成器
- 脚手架工具
- API测试工具