# 服务配置
server:
  port: 8003
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true

# Spring配置
spring:
  application:
    name: edu-dictionary-service
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_SERVER_ADDR:127.0.0.1:8848}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:nacos}
        namespace: ${NACOS_NAMESPACE:hky-hr}

  # 数据源配置
  datasource:
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:************}:${DB_PORT:31252}/${DB_NAME:hky_hr_db}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${DB_USER:sasa}
    password: ${DB_PWD:RApubone95}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariCP-DictionaryService
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1



  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:31000}
      password: ${REDIS_PWD:sjyt_cywKZHAl}
      database: ${REDIS_DB:6}
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 20
          max-wait: -1ms
          max-idle: 10
          min-idle: 5

  # Jackson配置
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# 日志配置
logging:
  level:
    com.hky.hr.dictionary: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/dictionary-service.log

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,tracing
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
  tracing:
    sampling:
      probability: 1.0
  zipkin:
    tracing:
      endpoint: ${ZIPKIN_ENDPOINT:http://localhost:9411/api/v2/spans}

# SpringDoc配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  info:
    title: 业务管理系统 - 字典配置服务 API
    description: 字典配置服务，提供系统字典和业务字典的统一管理，支持多级字典结构、缓存机制、导入导出等功能
    version: 1.0.0
    contact:
      name: HKY-HR-System
      email: <EMAIL>

# 字典配置服务配置
hky:
  dictionary:
    # 缓存配置
    cache:
      # 缓存过期时间（小时）
      expire-hours: 24
      # 缓存预热开关
      preload-enabled: true
      # 缓存刷新间隔（分钟）
      refresh-interval-minutes: 60

    # 导入导出配置
    import-export:
      # 单次导入最大记录数
      max-import-size: 10000
      # 导出文件保留天数
      export-file-retention-days: 7
      # 临时文件目录
      temp-dir: /tmp/dictionary-service

    # 使用统计配置
    statistics:
      # 是否启用使用统计
      enabled: true
      # 统计数据保留天数
      retention-days: 90
      # 批量统计处理大小
      batch-size: 1000

    # 数据校验配置
    validation:
      # 字典编码唯一性校验
      code-unique-check: true
      # 字典值长度限制
      max-value-length: 500
      # 字典名称长度限制
      max-name-length: 200

# Seata分布式事务配置（暂时禁用）
seata:
  enabled: false