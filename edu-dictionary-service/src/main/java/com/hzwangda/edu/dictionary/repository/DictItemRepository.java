package com.hzwangda.edu.dictionary.repository;

import com.hzwangda.edu.dictionary.entity.DictItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 字典项数据访问层
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Repository
public interface DictItemRepository extends JpaRepository<DictItem, String>, JpaSpecificationExecutor<DictItem> {

    /**
     * 根据类型ID查询字典项
     *
     * @param typeId 类型ID
     * @return 字典项列表
     */
    List<DictItem> findByTypeIdAndDeletedOrderBySortOrderAsc(String typeId, Integer deleted);

    /**
     * 根据类型编码查询字典项
     *
     * @param typeCode 类型编码
     * @return 字典项列表
     */
    @Query("SELECT di FROM DictItem di " +
           "INNER JOIN DictType dt ON di.typeId = dt.id " +
           "WHERE dt.typeCode = :typeCode AND di.deleted = 0 AND dt.deleted = 0 " +
           "ORDER BY di.sortOrder ASC")
    List<DictItem> findByTypeCode(@Param("typeCode") String typeCode);

    /**
     * 根据类型ID和字典编码查询字典项
     *
     * @param typeId 类型ID
     * @param itemCode 字典编码
     * @return 字典项
     */
    Optional<DictItem> findByTypeIdAndItemCodeAndDeleted(String typeId, String itemCode, Integer deleted);

    /**
     * 根据父级ID查询子项
     *
     * @param parentId 父级ID
     * @return 子项列表
     */
    List<DictItem> findByParentIdAndDeletedOrderBySortOrderAsc(String parentId, Integer deleted);

    /**
     * 统计指定类型和编码的数量（排除指定ID）
     *
     * @param typeId 类型ID
     * @param itemCode 字典编码
     * @param excludeId 排除的ID
     * @return 数量
     */
    @Query("SELECT COUNT(di) FROM DictItem di WHERE di.typeId = :typeId AND di.itemCode = :itemCode " +
           "AND di.deleted = 0 AND (:excludeId IS NULL OR di.id != :excludeId)")
    long countByTypeIdAndItemCodeAndExcludeId(@Param("typeId") String typeId,
                                              @Param("itemCode") String itemCode,
                                              @Param("excludeId") String excludeId);

    /**
     * 查询启用状态的字典项
     *
     * @param typeId 类型ID
     * @return 启用的字典项列表
     */
    List<DictItem> findByTypeIdAndStatusAndDeletedOrderBySortOrderAsc(String typeId, Integer status, Integer deleted);

    /**
     * 根据类型编码查询启用状态的字典项
     *
     * @param typeCode 类型编码
     * @return 启用的字典项列表
     */
    @Query("SELECT di FROM DictItem di " +
           "INNER JOIN DictType dt ON di.typeId = dt.id " +
           "WHERE dt.typeCode = :typeCode AND di.status = 1 AND di.deleted = 0 AND dt.deleted = 0 " +
           "ORDER BY di.sortOrder ASC")
    List<DictItem> findEnabledByTypeCode(@Param("typeCode") String typeCode);

    /**
     * 根据项目路径前缀查询所有子级项目
     *
     * @param pathPrefix 路径前缀
     * @param deleted 删除标记
     * @return 子级项目列表
     */
    @Query("SELECT di FROM DictItem di WHERE di.itemPath LIKE CONCAT(:pathPrefix, '%') AND di.deleted = :deleted ORDER BY di.itemPath ASC")
    List<DictItem> findByItemPathStartingWithAndDeleted(@Param("pathPrefix") String pathPrefix, @Param("deleted") Integer deleted);

    /**
     * 查询指定父级下的最大排序号
     *
     * @param parentId 父级ID
     * @param typeId 类型ID
     * @return 最大排序号
     */
    @Query("SELECT COALESCE(MAX(di.sortOrder), 0) FROM DictItem di WHERE di.typeId = :typeId AND " +
           "(:parentId IS NULL AND di.parentId IS NULL OR di.parentId = :parentId) AND di.deleted = 0")
    Integer findMaxSortOrderByParentIdAndTypeId(@Param("parentId") String parentId, @Param("typeId") String typeId);

    /**
     * 根据类型ID查询根级字典项
     *
     * @param typeId 类型ID
     * @return 根级字典项列表
     */
    @Query("SELECT di FROM DictItem di WHERE di.typeId = :typeId AND di.parentId IS NULL AND di.deleted = 0 ORDER BY di.sortOrder ASC")
    List<DictItem> findRootItemsByTypeId(@Param("typeId") String typeId);

    /**
     * 根据项目级别查询字典项
     *
     * @param typeId 类型ID
     * @param itemLevel 项目级别
     * @return 字典项列表
     */
    List<DictItem> findByTypeIdAndItemLevelAndDeletedOrderBySortOrderAsc(String typeId, Integer itemLevel, Integer deleted);

    /**
     * 查询系统内置的字典项
     *
     * @param typeId 类型ID
     * @return 系统内置字典项列表
     */
    List<DictItem> findByTypeIdAndIsSystemAndDeletedOrderBySortOrderAsc(String typeId, Integer isSystem, Integer deleted);
}
