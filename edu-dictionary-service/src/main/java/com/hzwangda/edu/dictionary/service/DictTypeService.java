package com.hzwangda.edu.dictionary.service;

import org.springframework.data.domain.Page;
import com.hzwangda.edu.dictionary.dto.DictTypeDTO;
import com.hzwangda.edu.dictionary.dto.DictTypeQueryRequest;

import java.util.List;

/**
 * 字典类型服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public interface DictTypeService {

    /**
     * 分页查询字典类型
     *
     * @param request 查询请求
     * @return 分页结果
     */
    Page<DictTypeDTO> queryDictTypes(DictTypeQueryRequest request);

    /**
     * 根据ID查询字典类型
     *
     * @param id 字典类型ID
     * @return 字典类型DTO
     */
    DictTypeDTO getDictTypeById(String id);

    /**
     * 根据类型编码查询字典类型
     *
     * @param typeCode 类型编码
     * @return 字典类型DTO
     */
    DictTypeDTO getDictTypeByCode(String typeCode);

    /**
     * 创建字典类型
     *
     * @param dictTypeDTO 字典类型DTO
     * @return 创建的字典类型DTO
     */
    DictTypeDTO createDictType(DictTypeDTO dictTypeDTO);

    /**
     * 更新字典类型
     *
     * @param id 字典类型ID
     * @param dictTypeDTO 字典类型DTO
     * @return 更新的字典类型DTO
     */
    DictTypeDTO updateDictType(String id, DictTypeDTO dictTypeDTO);

    /**
     * 删除字典类型
     *
     * @param id 字典类型ID
     * @return 是否删除成功
     */
    boolean deleteDictType(String id);

    /**
     * 批量删除字典类型
     *
     * @param ids 字典类型ID列表
     * @return 删除成功的数量
     */
    int batchDeleteDictTypes(List<String> ids);

    /**
     * 获取所有根节点（一级分类）
     *
     * @return 根节点列表
     */
    List<DictTypeDTO> getRootNodes();

    /**
     * 获取子节点列表
     *
     * @param parentId 父级ID
     * @return 子节点列表
     */
    List<DictTypeDTO> getChildren(String parentId);

    /**
     * 获取树形结构
     *
     * @return 树形结构列表
     */
    List<DictTypeDTO> getTreeStructure();

    /**
     * 获取启用状态的字典类型
     *
     * @return 启用的字典类型列表
     */
    List<DictTypeDTO> getEnabledDictTypes();

    /**
     * 检查类型编码是否存在
     *
     * @param typeCode 类型编码
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean isTypeCodeExists(String typeCode, String excludeId);

    /**
     * 更新字典类型状态
     *
     * @param id 字典类型ID
     * @param status 状态
     * @return 是否更新成功
     */
    boolean updateStatus(String id, Integer status);

    /**
     * 刷新缓存
     */
    void refreshCache();

    /**
     * 清理缓存
     */
    void clearCache();

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    Object getCacheStats();
}
