package com.hzwangda.edu.dictionary.service.impl;

import com.hzwangda.edu.common.exception.BusinessException;
import com.hzwangda.edu.common.result.ResultCode;
import com.hzwangda.edu.dictionary.dto.DictItemDTO;
import com.hzwangda.edu.dictionary.dto.DictItemQueryRequest;
import com.hzwangda.edu.dictionary.entity.DictItem;
import com.hzwangda.edu.dictionary.entity.DictType;
import com.hzwangda.edu.dictionary.enums.DictStatusEnum;
import com.hzwangda.edu.dictionary.repository.DictItemRepository;
import com.hzwangda.edu.dictionary.repository.DictTypeRepository;
import com.hzwangda.edu.dictionary.service.DictItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import jakarta.persistence.criteria.Predicate;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 字典项服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class DictItemServiceImpl implements DictItemService {

    @Autowired
    private DictItemRepository dictItemRepository;

    @Autowired
    private DictTypeRepository dictTypeRepository;

    @Override
    public Page<DictItemDTO> queryDictItems(DictItemQueryRequest request) {
        log.debug("分页查询字典项: {}", request);

        // 创建分页对象
        Pageable pageable = PageRequest.of(
            request.getPage(),
            request.getSize(),
            Sort.by(Sort.Direction.ASC, "sortOrder")
        );

        // 构建查询条件
        Specification<DictItem> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 逻辑删除条件
            predicates.add(criteriaBuilder.equal(root.get("deleted"), 0));

            // 类型ID查询
            if (StringUtils.hasText(request.getTypeId())) {
                predicates.add(criteriaBuilder.equal(root.get("typeId"), request.getTypeId()));
            }

            // 字典编码模糊查询
            if (StringUtils.hasText(request.getItemCode())) {
                predicates.add(criteriaBuilder.like(root.get("itemCode"), "%" + request.getItemCode() + "%"));
            }

            // 字典名称模糊查询
            if (StringUtils.hasText(request.getItemName())) {
                predicates.add(criteriaBuilder.like(root.get("itemName"), "%" + request.getItemName() + "%"));
            }

            // 状态查询
            if (request.getStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), request.getStatus()));
            }

            // 父级ID查询
            if (StringUtils.hasText(request.getParentId())) {
                predicates.add(criteriaBuilder.equal(root.get("parentId"), request.getParentId()));
            }

            // 是否系统内置查询
            if (request.getIsSystem() != null) {
                predicates.add(criteriaBuilder.equal(root.get("isSystem"), request.getIsSystem()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        Page<DictItem> resultPage = dictItemRepository.findAll(spec, pageable);

        // 转换为DTO
        return resultPage.map(this::convertToDTO);
    }

    @Override
    @Cacheable(value = "dict_item", key = "#id")
    public DictItemDTO getDictItemById(String id) {
        log.debug("根据ID查询字典项: {}", id);

        Optional<DictItem> dictItemOpt = dictItemRepository.findById(id);
        if (dictItemOpt.isEmpty() || dictItemOpt.get().getDeleted() != 0) {
            throw new BusinessException(ResultCode.NOT_FOUND, "字典项不存在");
        }

        return convertToDTO(dictItemOpt.get());
    }

    @Override
    @Cacheable(value = "dict_items_by_type", key = "#typeId")
    public List<DictItemDTO> getDictItemsByTypeId(String typeId) {
        log.debug("根据类型ID查询字典项列表: {}", typeId);

        List<DictItem> dictItems = dictItemRepository.findByTypeIdAndDeletedOrderBySortOrderAsc(typeId, 0);
        return dictItems.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "dict_items_by_code", key = "#typeCode")
    public List<DictItemDTO> getDictItemsByTypeCode(String typeCode) {
        log.debug("根据类型编码查询字典项列表: {}", typeCode);

        List<DictItem> dictItems = dictItemRepository.findByTypeCode(typeCode);
        return dictItems.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "dict_items_enabled", key = "#typeCode")
    public List<DictItemDTO> getEnabledDictItemsByTypeCode(String typeCode) {
        log.debug("根据类型编码查询启用状态的字典项列表: {}", typeCode);

        List<DictItem> dictItems = dictItemRepository.findEnabledByTypeCode(typeCode);
        return dictItems.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    public DictItemDTO getDictItemByTypeCodeAndItemCode(String typeCode, String itemCode) {
        log.debug("根据类型编码和字典编码查询字典项: typeCode={}, itemCode={}", typeCode, itemCode);

        // 先根据类型编码查找类型
        Optional<DictType> dictTypeOpt = dictTypeRepository.findByTypeCodeAndDeleted(typeCode, 0);
        if (dictTypeOpt.isEmpty()) {
            throw new BusinessException(ResultCode.NOT_FOUND, "字典类型不存在");
        }

        // 再根据类型ID和字典编码查找字典项
        Optional<DictItem> dictItemOpt = dictItemRepository.findByTypeIdAndItemCodeAndDeleted(
            dictTypeOpt.get().getId(), itemCode, 0);
        if (dictItemOpt.isEmpty()) {
            throw new BusinessException(ResultCode.NOT_FOUND, "字典项不存在");
        }

        return convertToDTO(dictItemOpt.get());
    }

    @Override
    public DictItemDTO getDictItemByTypeIdAndCode(String typeId, String itemCode) {
        log.debug("根据类型ID和字典编码查询字典项: typeId={}, itemCode={}", typeId, itemCode);

        Optional<DictItem> dictItemOpt = dictItemRepository.findByTypeIdAndItemCodeAndDeleted(typeId, itemCode, 0);
        if (dictItemOpt.isEmpty()) {
            throw new BusinessException(ResultCode.NOT_FOUND, "字典项不存在");
        }

        return convertToDTO(dictItemOpt.get());
    }

    @Override
    @Transactional
    public DictItemDTO createDictItem(DictItemDTO dictItemDTO) {
        log.info("创建字典项: typeId={}, itemCode={}", dictItemDTO.getTypeId(), dictItemDTO.getItemCode());

        // 验证字典类型是否存在
        Optional<DictType> dictTypeOpt = dictTypeRepository.findById(dictItemDTO.getTypeId());
        if (dictTypeOpt.isEmpty() || dictTypeOpt.get().getDeleted() != 0) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "字典类型不存在");
        }

        // 验证字典编码唯一性
        if (isItemCodeExists(dictItemDTO.getTypeId(), dictItemDTO.getItemCode(), null)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "字典编码已存在");
        }

        DictItem dictItem = new DictItem();
        BeanUtils.copyProperties(dictItemDTO, dictItem);
        dictItem.setId(UUID.randomUUID().toString());

        // 设置默认值
        if (dictItem.getStatus() == null) {
            dictItem.setStatus(DictStatusEnum.ENABLED.getCode());
        }
        if (dictItem.getSortOrder() == null) {
            dictItem.setSortOrder(0);
        }
        if (dictItem.getIsSystem() == null) {
            dictItem.setIsSystem(0);
        }
        if (dictItem.getAllowChildren() == null) {
            dictItem.setAllowChildren(0);
        }

        // 计算层级和路径
        calculateLevelAndPath(dictItem);

        DictItem savedItem = dictItemRepository.save(dictItem);

        // 清理相关缓存
        DictType dictType = dictTypeOpt.get();
        clearCache(dictType.getTypeCode());

        return convertToDTO(savedItem);
    }

    @Override
    @Transactional
    @CacheEvict(value = "dict_item", key = "#id")
    public DictItemDTO updateDictItem(String id, DictItemDTO dictItemDTO) {
        log.info("更新字典项: {}", id);

        Optional<DictItem> existingItemOpt = dictItemRepository.findById(id);
        if (existingItemOpt.isEmpty() || existingItemOpt.get().getDeleted() != 0) {
            throw new BusinessException(ResultCode.NOT_FOUND, "字典项不存在");
        }

        DictItem existingItem = existingItemOpt.get();

        // 验证字典编码唯一性
        if (!existingItem.getItemCode().equals(dictItemDTO.getItemCode()) &&
            isItemCodeExists(dictItemDTO.getTypeId(), dictItemDTO.getItemCode(), id)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "字典编码已存在");
        }

        // 更新字段
        BeanUtils.copyProperties(dictItemDTO, existingItem, "id", "createTime", "createBy", "deleted", "version");

        // 重新计算层级和路径
        calculateLevelAndPath(existingItem);

        DictItem updatedItem = dictItemRepository.save(existingItem);

        // 清理相关缓存
        Optional<DictType> dictTypeOpt = dictTypeRepository.findById(existingItem.getTypeId());
        if (dictTypeOpt.isPresent()) {
            clearCache(dictTypeOpt.get().getTypeCode());
        }

        return convertToDTO(updatedItem);
    }

    @Override
    @Transactional
    @CacheEvict(value = "dict_item", key = "#id")
    public boolean deleteDictItem(String id) {
        log.info("删除字典项: {}", id);

        Optional<DictItem> dictItemOpt = dictItemRepository.findById(id);
        if (dictItemOpt.isEmpty() || dictItemOpt.get().getDeleted() != 0) {
            throw new BusinessException(ResultCode.NOT_FOUND, "字典项不存在");
        }

        DictItem dictItem = dictItemOpt.get();

        // 检查是否为系统内置
        if (dictItem.getIsSystem() == 1) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "系统内置字典项不允许删除");
        }

        // 检查是否有子项
        List<DictItem> children = dictItemRepository.findByParentIdAndDeletedOrderBySortOrderAsc(id, 0);
        if (!children.isEmpty()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "存在子项，不允许删除");
        }

        // 逻辑删除
        dictItem.setDeleted(1);
        dictItemRepository.save(dictItem);

        // 清理相关缓存
        Optional<DictType> dictTypeOpt = dictTypeRepository.findById(dictItem.getTypeId());
        if (dictTypeOpt.isPresent()) {
            clearCache(dictTypeOpt.get().getTypeCode());
        }

        return true;
    }

    @Override
    @Transactional
    @CacheEvict(value = "dict_item", allEntries = true)
    public int batchDeleteDictItems(List<String> ids) {
        log.info("批量删除字典项: {}", ids);

        int successCount = 0;
        for (String id : ids) {
            try {
                if (deleteDictItem(id)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.warn("删除字典项失败: {}, 错误: {}", id, e.getMessage());
            }
        }

        return successCount;
    }

    @Override
    @Cacheable(value = "dict_item_children", key = "#parentId")
    public List<DictItemDTO> getChildrenByParentId(String parentId) {
        log.debug("根据父级ID查询子项: {}", parentId);

        List<DictItem> children = dictItemRepository.findByParentIdAndDeletedOrderBySortOrderAsc(parentId, 0);
        return children.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "dict_item_tree", key = "#typeId")
    public List<DictItemDTO> getTreeStructure(String typeId) {
        log.debug("获取树形结构: {}", typeId);

        List<DictItem> rootItems = dictItemRepository.findRootItemsByTypeId(typeId);
        List<DictItemDTO> rootDTOs = rootItems.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        for (DictItemDTO root : rootDTOs) {
            buildTree(root);
        }

        return rootDTOs;
    }

    @Override
    public boolean isItemCodeExists(String typeId, String itemCode, String excludeId) {
        long count = dictItemRepository.countByTypeIdAndItemCodeAndExcludeId(typeId, itemCode, excludeId);
        return count > 0;
    }

    @Override
    @CacheEvict(value = "dict_item", key = "#id")
    public boolean updateStatus(String id, Integer status) {
        log.info("更新字典项状态: {}, 状态: {}", id, status);

        Optional<DictItem> dictItemOpt = dictItemRepository.findById(id);
        if (dictItemOpt.isEmpty() || dictItemOpt.get().getDeleted() != 0) {
            return false;
        }

        DictItem dictItem = dictItemOpt.get();
        dictItem.setStatus(status);
        dictItemRepository.save(dictItem);

        return true;
    }

    /**
     * 构建树形结构
     */
    private void buildTree(DictItemDTO parent) {
        List<DictItemDTO> children = getChildrenByParentId(parent.getId());
        parent.setChildren(children);

        for (DictItemDTO child : children) {
            buildTree(child);
        }
    }

    /**
     * 计算层级和路径
     */
    private void calculateLevelAndPath(DictItem dictItem) {
        if (StringUtils.hasText(dictItem.getParentId())) {
            Optional<DictItem> parentOpt = dictItemRepository.findById(dictItem.getParentId());
            if (parentOpt.isPresent()) {
                DictItem parent = parentOpt.get();
                dictItem.setItemLevel(parent.getItemLevel() + 1);
                dictItem.setItemPath(parent.getItemPath() + "/" + dictItem.getId());
            } else {
                dictItem.setItemLevel(1);
                dictItem.setItemPath(dictItem.getId());
            }
        } else {
            dictItem.setItemLevel(1);
            dictItem.setItemPath(dictItem.getId());
        }
    }

    @Override
    @CacheEvict(value = {"dict_item", "dict_items_by_type", "dict_items_by_code", "dict_items_enabled", "dict_item_children", "dict_item_tree"}, allEntries = true)
    public void refreshCache(String typeCode) {
        log.info("刷新字典项缓存: {}", typeCode);
    }

    @Override
    @CacheEvict(value = {"dict_item", "dict_items_by_type", "dict_items_by_code", "dict_items_enabled", "dict_item_children", "dict_item_tree"}, allEntries = true)
    public void clearCache(String typeCode) {
        log.info("清理字典项缓存: {}", typeCode);
    }

    /**
     * 转换为DTO
     */
    private DictItemDTO convertToDTO(DictItem dictItem) {
        DictItemDTO dto = new DictItemDTO();
        BeanUtils.copyProperties(dictItem, dto);
        return dto;
    }
}
