package com.hzwangda.edu.dictionary.repository;

import com.hzwangda.edu.dictionary.entity.DictType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 字典类型数据访问层
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Repository
public interface DictTypeRepository extends JpaRepository<DictType, String>, JpaSpecificationExecutor<DictType> {

    /**
     * 根据类型编码查询字典类型
     *
     * @param typeCode 类型编码
     * @return 字典类型
     */
    Optional<DictType> findByTypeCodeAndDeleted(String typeCode, Integer deleted);

    /**
     * 查询所有根节点（一级分类）
     *
     * @return 根节点列表
     */
    @Query("SELECT dt FROM DictType dt WHERE dt.parentId IS NULL AND dt.deleted = 0 ORDER BY dt.sortOrder ASC")
    List<DictType> findRootNodes();

    /**
     * 根据父级ID查询子节点
     *
     * @param parentId 父级ID
     * @return 子节点列表
     */
    List<DictType> findByParentIdAndDeletedOrderBySortOrderAsc(String parentId, Integer deleted);

    /**
     * 查询启用状态的字典类型
     *
     * @return 启用的字典类型列表
     */
    List<DictType> findByStatusAndDeletedOrderBySortOrderAsc(Integer status, Integer deleted);

    /**
     * 统计指定类型编码的数量（排除指定ID）
     *
     * @param typeCode 类型编码
     * @param excludeId 排除的ID
     * @return 数量
     */
    @Query("SELECT COUNT(dt) FROM DictType dt WHERE dt.typeCode = :typeCode AND dt.deleted = 0 " +
           "AND (:excludeId IS NULL OR dt.id != :excludeId)")
    long countByTypeCodeAndExcludeId(@Param("typeCode") String typeCode, @Param("excludeId") String excludeId);

    /**
     * 根据类型路径前缀查询所有子级类型
     *
     * @param pathPrefix 路径前缀
     * @param deleted 删除标记
     * @return 子级类型列表
     */
    @Query("SELECT dt FROM DictType dt WHERE dt.typePath LIKE CONCAT(:pathPrefix, '%') AND dt.deleted = :deleted ORDER BY dt.typePath ASC")
    List<DictType> findByTypePathStartingWithAndDeleted(@Param("pathPrefix") String pathPrefix, @Param("deleted") Integer deleted);

    /**
     * 查询指定父级下的最大排序号
     *
     * @param parentId 父级ID
     * @return 最大排序号
     */
    @Query("SELECT COALESCE(MAX(dt.sortOrder), 0) FROM DictType dt WHERE " +
           "(:parentId IS NULL AND dt.parentId IS NULL OR dt.parentId = :parentId) AND dt.deleted = 0")
    Integer findMaxSortOrderByParentId(@Param("parentId") String parentId);

    /**
     * 根据类型级别查询字典类型
     *
     * @param typeLevel 类型级别
     * @return 字典类型列表
     */
    List<DictType> findByTypeLevelAndDeletedOrderBySortOrderAsc(Integer typeLevel, Integer deleted);

    /**
     * 查询系统内置的字典类型
     *
     * @return 系统内置字典类型列表
     */
    List<DictType> findByIsSystemAndDeletedOrderBySortOrderAsc(Integer isSystem, Integer deleted);

    /**
     * 根据状态和系统标记查询字典类型
     *
     * @param status 状态
     * @param isSystem 是否系统内置
     * @return 字典类型列表
     */
    List<DictType> findByStatusAndIsSystemAndDeletedOrderBySortOrderAsc(Integer status, Integer isSystem, Integer deleted);
}
