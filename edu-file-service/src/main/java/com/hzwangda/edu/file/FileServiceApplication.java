package com.hzwangda.edu.file;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * 文件管理服务启动类
 *
 * 提供文件上传、下载、存储管理功能，基于MinIO对象存储
 * 支持预签名URL、文件版本控制、安全访问控制等功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = "com.hzwangda.edu")
@EntityScan(basePackages = "com.hzwangda.edu.file.entity")
@EnableJpaRepositories(basePackages = "com.hzwangda.edu.file.repository")
@ComponentScan(value = {"com.hzwangda"})
@EnableJpaAuditing
public class FileServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(FileServiceApplication.class, args);
    }
}
