package com.hzwangda.edu.file.service.impl;

import com.hzwangda.edu.common.exception.BusinessException;
import com.hzwangda.edu.common.result.ResultCode;
import com.hzwangda.edu.file.config.FileServiceConfig;
import com.hzwangda.edu.file.config.MinioConfig;
import com.hzwangda.edu.file.dto.FileInfoResponse;
import com.hzwangda.edu.file.dto.FileStatistics;
import com.hzwangda.edu.file.dto.FileUploadRequest;
import com.hzwangda.edu.file.dto.PresignedUrlResponse;
import com.hzwangda.edu.file.entity.FileMetadata;
import com.hzwangda.edu.file.enums.FileStatus;
import com.hzwangda.edu.file.repository.FileMetadataRepository;
import com.hzwangda.edu.file.service.FileService;
import com.hzwangda.edu.file.util.FilePathUtil;
import com.hzwangda.edu.file.util.FileUtil;
import io.minio.*;
import io.minio.http.Method;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 文件服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@Transactional
public class FileServiceImpl implements FileService {

    @Autowired
    private MinioClient minioClient;

    @Autowired
    private MinioConfig minioConfig;

    @Autowired
    private FileServiceConfig fileServiceConfig;

    @Autowired
    private FileMetadataRepository fileInfoRepository;

    @Autowired
    private HttpServletRequest httpServletRequest;

    @Override
    public FileInfoResponse uploadFile(MultipartFile file, FileUploadRequest request) {
        log.info("开始上传文件: {}, 模块: {}, 业务ID: {}, 文件类型: {}",
                   file.getOriginalFilename(), request.getModuleCode(), request.getBizId(), request.getFileType());

        try {
            // 验证文件
            validateFile(file);

            // 生成文件ID和路径
            String fileId = UUID.randomUUID().toString();
            String filePath = FilePathUtil.generateFilePath(
                request.getModuleCode(),
                request.getBizId(),
                request.getFileType(),
                file.getOriginalFilename(),
                fileServiceConfig.getPathTemplate()
            );

            // 计算文件MD5
            String md5Hash = FileUtil.calculateMD5(file.getInputStream());

            // 检查是否存在相同文件
            if (!request.getOverwrite()) {
                List<FileMetadata> existingFiles = fileInfoRepository.findByMd5HashAndDeletedFalse(md5Hash);
                if (!existingFiles.isEmpty()) {
                    log.warn("文件已存在，MD5: {}", md5Hash);
                    throw new BusinessException(ResultCode.BAD_REQUEST, "文件已存在");
                }
            }

            // 确保存储桶存在
            ensureBucketExists();

            // 上传文件到MinIO
            PutObjectArgs putObjectArgs = PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(filePath)
                    .stream(file.getInputStream(), file.getSize(), -1)
                    .contentType(file.getContentType())
                    .build();

            ObjectWriteResponse response = minioClient.putObject(putObjectArgs);
            log.info("文件上传到MinIO成功: {}", filePath);

            // 保存文件信息到数据库
            FileMetadata fileInfo = createFileInfo(fileId, file, request, filePath, md5Hash);
            fileInfo = fileInfoRepository.save(fileInfo);

            log.info("文件上传完成: {}", fileId);
            return convertToResponse(fileInfo);

        } catch (Exception e) {
            log.error("文件上传失败: {}", file.getOriginalFilename(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public List<FileInfoResponse> uploadFiles(List<MultipartFile> files, FileUploadRequest request) {
        log.info("开始批量上传文件，数量: {}", files.size());

        List<FileInfoResponse> responses = new ArrayList<>();
        List<String> failedFiles = new ArrayList<>();

        for (MultipartFile file : files) {
            try {
                FileInfoResponse response = uploadFile(file, request);
                responses.add(response);
            } catch (Exception e) {
                log.error("文件上传失败: {}", file.getOriginalFilename(), e);
                failedFiles.add(file.getOriginalFilename() + ": " + e.getMessage());
            }
        }

        if (!failedFiles.isEmpty()) {
            log.warn("部分文件上传失败: {}", failedFiles);
            throw new BusinessException(ResultCode.PARTIAL_CONTENT,
                "部分文件上传失败: " + String.join(", ", failedFiles));
        }

        log.info("批量文件上传完成，成功数量: {}", responses.size());
        return responses;
    }

    @Override
    public PresignedUrlResponse getPresignedUploadUrl(String filename, String contentType,
                                                    FileUploadRequest request, Integer expirySeconds) {
        log.info("生成预签名上传URL: {}, 类型: {}", filename, contentType);

        try {
            // 验证文件类型
            if (!fileServiceConfig.isAllowedContentType(contentType)) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "不支持的文件类型: " + contentType);
            }

            // 生成文件ID和路径
            String fileId = UUID.randomUUID().toString();
            String filePath = FilePathUtil.generateFilePath(
                request.getModuleCode(),
                request.getBizId(),
                request.getFileType(),
                filename,
                fileServiceConfig.getPathTemplate()
            );

            // 确保存储桶存在
            ensureBucketExists();

            // 生成预签名URL
            int expiry = expirySeconds != null ? expirySeconds : fileServiceConfig.getPresignedUrlExpiry();
            GetPresignedObjectUrlArgs args = GetPresignedObjectUrlArgs.builder()
                    .method(Method.PUT)
                    .bucket(minioConfig.getBucketName())
                    .object(filePath)
                    .expiry(expiry)
                    .build();

            String uploadUrl = minioClient.getPresignedObjectUrl(args);

            // 创建文件信息记录（状态为上传中）
            FileMetadata fileInfo = new FileMetadata();
            fileInfo.setFileId(fileId);
            fileInfo.setOriginalFilename(filename);
            fileInfo.setStoredFilename(FilePathUtil.extractFilename(filePath));
            fileInfo.setFilePath(filePath);
            fileInfo.setContentType(contentType);
            fileInfo.setFileExtension(FileUtil.getFileExtension(filename));
            fileInfo.setModuleCode(request.getModuleCode());
            fileInfo.setBizId(request.getBizId());
            fileInfo.setFileType(request.getFileType());
            fileInfo.setStatus(FileStatus.UPLOADING.getCode());
            fileInfo.setVersion(1);
            fileInfo.setDescription(request.getDescription());
            fileInfo.setUploadIp(getClientIpAddress());

            fileInfoRepository.save(fileInfo);

            PresignedUrlResponse response = new PresignedUrlResponse(
                fileId, uploadUrl, LocalDateTime.now().plusSeconds(expiry), expiry);

            log.info("预签名上传URL生成成功: {}", fileId);
            return response;

        } catch (Exception e) {
            log.error("生成预签名上传URL失败: {}", filename, e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "生成预签名URL失败: " + e.getMessage());
        }
    }

    @Override
    public FileInfoResponse confirmPresignedUpload(String fileId, String etag) {
        log.info("确认预签名上传完成: {}, ETag: {}", fileId, etag);

        try {
            // 查找文件信息
            Optional<FileMetadata> optionalFileInfo = fileInfoRepository.findByFileId(fileId);
            if (optionalFileInfo.isEmpty()) {
                throw new BusinessException(ResultCode.NOT_FOUND, "文件信息不存在: " + fileId);
            }

            FileMetadata fileInfo = optionalFileInfo.get();
            if (!FileStatus.UPLOADING.getCode().equals(fileInfo.getStatus())) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "文件状态不正确: " + fileInfo.getStatus());
            }

            // 获取文件信息
            StatObjectArgs statArgs = StatObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(fileInfo.getFilePath())
                    .build();

            StatObjectResponse statResponse = minioClient.statObject(statArgs);

            // 更新文件信息
            fileInfo.setFileSize(statResponse.size());
            fileInfo.setMd5Hash(statResponse.etag());
            fileInfo.setStatus(FileStatus.ACTIVE.getCode());

            fileInfo = fileInfoRepository.save(fileInfo);

            log.info("预签名上传确认完成: {}", fileId);
            return convertToResponse(fileInfo);

        } catch (Exception e) {
            log.error("确认预签名上传失败: {}", fileId, e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "确认上传失败: " + e.getMessage());
        }
    }

    /**
     * 验证文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "文件不能为空");
        }

        // 检查文件大小
        if (file.getSize() > fileServiceConfig.getMaxFileSizeInBytes()) {
            throw new BusinessException(ResultCode.BAD_REQUEST,
                "文件大小超过限制: " + fileServiceConfig.getMaxFileSize());
        }

        // 检查文件类型
        String contentType = file.getContentType();
        if (!fileServiceConfig.isAllowedContentType(contentType)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "不支持的文件类型: " + contentType);
        }
    }

    /**
     * 确保存储桶存在
     */
    private void ensureBucketExists() {
        try {
            BucketExistsArgs bucketExistsArgs = BucketExistsArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .build();

            boolean exists = minioClient.bucketExists(bucketExistsArgs);
            if (!exists) {
                MakeBucketArgs makeBucketArgs = MakeBucketArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .build();
                minioClient.makeBucket(makeBucketArgs);
                log.info("创建存储桶成功: {}", minioConfig.getBucketName());
            }
        } catch (Exception e) {
            log.error("检查/创建存储桶失败", e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "存储桶操作失败");
        }
    }

    /**
     * 创建文件信息对象
     */
    private FileMetadata createFileInfo(String fileId, MultipartFile file, FileUploadRequest request,
                                        String filePath, String md5Hash) {
        FileMetadata fileInfo = new FileMetadata();
        fileInfo.setFileId(fileId);
        fileInfo.setOriginalFilename(file.getOriginalFilename());
        fileInfo.setStoredFilename(FilePathUtil.extractFilename(filePath));
        fileInfo.setFilePath(filePath);
        fileInfo.setFileSize(file.getSize());
        fileInfo.setContentType(file.getContentType());
        fileInfo.setFileExtension(FileUtil.getFileExtension(file.getOriginalFilename()));
        fileInfo.setMd5Hash(md5Hash);
        fileInfo.setModuleCode(request.getModuleCode());
        fileInfo.setBizId(request.getBizId());
        fileInfo.setFileType(request.getFileType());
        fileInfo.setStatus(FileStatus.ACTIVE.getCode());
        fileInfo.setVersion(1);
        fileInfo.setDescription(request.getDescription());
        fileInfo.setUploadIp(getClientIpAddress());

        return fileInfo;
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress() {
        String xForwardedFor = httpServletRequest.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = httpServletRequest.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }

        return httpServletRequest.getRemoteAddr();
    }

    @Override
    public InputStream downloadFile(String fileId) {
        log.info("开始下载文件: {}", fileId);

        try {
            // 查找文件信息
            Optional<FileMetadata> optionalFileInfo = fileInfoRepository.findByFileIdAndDeletedFalse(fileId);
            if (optionalFileInfo.isEmpty()) {
                throw new BusinessException(ResultCode.NOT_FOUND, "文件不存在: " + fileId);
            }

            FileMetadata fileInfo = optionalFileInfo.get();
            if (!FileStatus.ACTIVE.getCode().equals(fileInfo.getStatus())) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "文件状态不可用: " + fileInfo.getStatus());
            }

            // 从MinIO下载文件
            GetObjectArgs getObjectArgs = GetObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(fileInfo.getFilePath())
                    .build();

            InputStream inputStream = minioClient.getObject(getObjectArgs);

            // 更新下载次数
            fileInfoRepository.incrementDownloadCount(fileId);

            log.info("文件下载成功: {}", fileId);
            return inputStream;

        } catch (Exception e) {
            log.error("文件下载失败: {}", fileId, e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "文件下载失败: " + e.getMessage());
        }
    }

    @Override
    public PresignedUrlResponse getPresignedDownloadUrl(String fileId, Integer expirySeconds) {
        log.info("生成预签名下载URL: {}", fileId);

        try {
            // 查找文件信息
            Optional<FileMetadata> optionalFileInfo = fileInfoRepository.findByFileIdAndDeletedFalse(fileId);
            if (optionalFileInfo.isEmpty()) {
                throw new BusinessException(ResultCode.NOT_FOUND, "文件不存在: " + fileId);
            }

            FileMetadata fileInfo = optionalFileInfo.get();
            if (!FileStatus.ACTIVE.getCode().equals(fileInfo.getStatus())) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "文件状态不可用: " + fileInfo.getStatus());
            }

            // 生成预签名下载URL
            int expiry = expirySeconds != null ? expirySeconds : fileServiceConfig.getPresignedUrlExpiry();
            GetPresignedObjectUrlArgs args = GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(minioConfig.getBucketName())
                    .object(fileInfo.getFilePath())
                    .expiry(expiry)
                    .build();

            String downloadUrl = minioClient.getPresignedObjectUrl(args);

            PresignedUrlResponse response = new PresignedUrlResponse(
                fileId, downloadUrl, LocalDateTime.now().plusSeconds(expiry), expiry, true);

            log.info("预签名下载URL生成成功: {}", fileId);
            return response;

        } catch (Exception e) {
            log.error("生成预签名下载URL失败: {}", fileId, e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "生成预签名下载URL失败: " + e.getMessage());
        }
    }

    @Override
    public void deleteFile(String fileId) {
        log.info("开始删除文件: {}", fileId);

        try {
            // 查找文件信息
            Optional<FileMetadata> optionalFileInfo = fileInfoRepository.findByFileIdAndDeletedFalse(fileId);
            if (optionalFileInfo.isEmpty()) {
                throw new BusinessException(ResultCode.NOT_FOUND, "文件不存在: " + fileId);
            }

            // 逻辑删除
            int updated = fileInfoRepository.logicalDeleteByFileId(fileId, getCurrentUser(), LocalDateTime.now());
            if (updated == 0) {
                throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "文件删除失败");
            }

            log.info("文件删除成功: {}", fileId);

        } catch (Exception e) {
            log.error("文件删除失败: {}", fileId, e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "文件删除失败: " + e.getMessage());
        }
    }

    @Override
    public void physicalDeleteFile(String fileId) {
        log.info("开始物理删除文件: {}", fileId);

        try {
            // 查找文件信息
            Optional<FileMetadata> optionalFileInfo = fileInfoRepository.findByFileId(fileId);
            if (optionalFileInfo.isEmpty()) {
                throw new BusinessException(ResultCode.NOT_FOUND, "文件不存在: " + fileId);
            }

            FileMetadata fileInfo = optionalFileInfo.get();

            // 从MinIO删除文件
            RemoveObjectArgs removeObjectArgs = RemoveObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(fileInfo.getFilePath())
                    .build();

            minioClient.removeObject(removeObjectArgs);

            // 从数据库删除记录
            fileInfoRepository.delete(fileInfo);

            log.info("文件物理删除成功: {}", fileId);

        } catch (Exception e) {
            log.error("文件物理删除失败: {}", fileId, e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "文件物理删除失败: " + e.getMessage());
        }
    }

    @Override
    public FileInfoResponse getFileInfo(String fileId) {
        log.info("获取文件信息: {}", fileId);

        Optional<FileMetadata> optionalFileInfo = fileInfoRepository.findByFileIdAndDeletedFalse(fileId);
        if (optionalFileInfo.isEmpty()) {
            throw new BusinessException(ResultCode.NOT_FOUND, "文件不存在: " + fileId);
        }

        return convertToResponse(optionalFileInfo.get());
    }

    @Override
    public Page<FileInfoResponse> getFilesByBusiness(String moduleCode, String bizId, Pageable pageable) {
        log.info("查询业务文件列表: 模块={}, 业务ID={}", moduleCode, bizId);

        Page<FileMetadata> fileInfoPage = fileInfoRepository.findByModuleCodeAndBizIdAndDeletedFalseOrderByCreateTimeDesc(
                moduleCode, bizId, pageable);

        return fileInfoPage.map(this::convertToResponse);
    }

    @Override
    public Page<FileInfoResponse> getFilesByBusinessAndType(String moduleCode, String bizId,
                                                          String fileType, Pageable pageable) {
        log.info("查询业务文件列表: 模块={}, 业务ID={}, 文件类型={}", moduleCode, bizId, fileType);

        Page<FileMetadata> fileInfoPage = fileInfoRepository.findByModuleCodeAndBizIdAndFileTypeAndDeletedFalseOrderByCreateTimeDesc(
                moduleCode, bizId, fileType, pageable);

        return fileInfoPage.map(this::convertToResponse);
    }

    @Override
    public Page<FileInfoResponse> searchFilesByName(String filename, Pageable pageable) {
        log.info("根据文件名搜索文件: {}", filename);

        Page<FileMetadata> fileInfoPage = fileInfoRepository.findByOriginalFilenameContaining(filename, pageable);

        return fileInfoPage.map(this::convertToResponse);
    }

    @Override
    public List<FileInfoResponse> getFileVersions(String parentFileId) {
        log.info("获取文件版本列表: {}", parentFileId);

        List<FileMetadata> fileVersions = fileInfoRepository.findByParentFileIdAndDeletedFalseOrderByCreateTimeDesc(parentFileId);

        return fileVersions.stream()
                .map(this::convertToResponse)
                .toList();
    }

    @Override
    public InputStream previewFile(String fileId) {
        log.info("预览文件: {}", fileId);

        // 预览功能与下载功能相同，但不增加下载次数
        try {
            Optional<FileMetadata> optionalFileInfo = fileInfoRepository.findByFileIdAndDeletedFalse(fileId);
            if (optionalFileInfo.isEmpty()) {
                throw new BusinessException(ResultCode.NOT_FOUND, "文件不存在: " + fileId);
            }

            FileMetadata fileInfo = optionalFileInfo.get();
            if (!FileStatus.ACTIVE.getCode().equals(fileInfo.getStatus())) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "文件状态不可用: " + fileInfo.getStatus());
            }

            GetObjectArgs getObjectArgs = GetObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(fileInfo.getFilePath())
                    .build();

            InputStream inputStream = minioClient.getObject(getObjectArgs);

            log.info("文件预览成功: {}", fileId);
            return inputStream;

        } catch (Exception e) {
            log.error("文件预览失败: {}", fileId, e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "文件预览失败: " + e.getMessage());
        }
    }

    @Override
    public boolean fileExists(String fileId) {
        log.debug("检查文件是否存在: {}", fileId);

        Optional<FileMetadata> optionalFileInfo = fileInfoRepository.findByFileIdAndDeletedFalse(fileId);
        return optionalFileInfo.isPresent() && FileStatus.ACTIVE.getCode().equals(optionalFileInfo.get().getStatus());
    }

    @Override
    public FileStatistics getFileStatistics(String moduleCode) {
        log.info("获取文件统计信息: {}", moduleCode);

        try {
            long totalFiles = fileInfoRepository.countByModuleCodeAndDeletedFalse(moduleCode);
            Long totalSize = fileInfoRepository.sumFileSizeByModuleCode(moduleCode);

            // 这里简化处理，实际可以根据需要添加更多统计
            long activeFiles = totalFiles;
            long deletedFiles = 0;

            return new FileStatistics(totalFiles, totalSize != null ? totalSize : 0, activeFiles, deletedFiles);

        } catch (Exception e) {
            log.error("获取文件统计信息失败: {}", moduleCode, e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户
     */
    private String getCurrentUser() {
        // TODO: 从Spring Security上下文获取当前用户
        return "system";
    }

    /**
     * 转换为响应对象
     */
    private FileInfoResponse convertToResponse(FileMetadata fileInfo) {
        FileInfoResponse response = new FileInfoResponse();
        response.setFileId(fileInfo.getFileId());
        response.setOriginalFilename(fileInfo.getOriginalFilename());
        response.setFileSize(fileInfo.getFileSize());
        response.setContentType(fileInfo.getContentType());
        response.setFileExtension(fileInfo.getFileExtension());
        response.setModuleCode(fileInfo.getModuleCode());
        response.setBizId(fileInfo.getBizId());
        response.setFileType(fileInfo.getFileType());
        response.setStatus(fileInfo.getStatus());
        response.setVersion(fileInfo.getVersion() != null ? fileInfo.getVersion().intValue() : 1);
        response.setDescription(fileInfo.getDescription());
        response.setDownloadCount(fileInfo.getDownloadCount());
        response.setCreateTime(fileInfo.getCreateTime());
        response.setCreateBy(fileInfo.getCreateBy());
        response.setUpdateTime(fileInfo.getUpdateTime());
        response.setUpdateBy(fileInfo.getUpdateBy());

        // 生成文件访问URL
        response.setFileUrl("/api/v1/files/download/" + fileInfo.getFileId());

        return response;
    }
}
