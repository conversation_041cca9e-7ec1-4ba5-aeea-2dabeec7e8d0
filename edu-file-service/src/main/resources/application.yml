server:
  port: 8004
  servlet:
    context-path: /file-service
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

spring:
  application:
    name: edu-file-service
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_SERVER_ADDR:127.0.0.1:8848}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:nacos}
        namespace: ${NACOS_NAMESPACE:hky-hr}

  profiles:
    active: ${SPRING_PROFILES:dev}

  datasource:
    url: jdbc:postgresql://${DB_HOST:************}:${DB_PORT:31252}/${DB_NAME:hky_hr_db}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${DB_USER:sasa}
    password: ${DB_PWD:RApubone95}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: ${SPRING_JPA_SHOW_SQL:false}
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
    open-in-view: false

  data:
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:31000}
      password: ${REDIS_PWD:sjyt_cywKZHAl}
      database: ${REDIS_DB:6}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms

  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:8001

# MinIO配置
minio:
  endpoint: ${MINIO_ENDPOINT:https://qyb-minio.hzwangda.com}
  access-key: ${MINIO_ACCESS_KEY:minio}
  secret-key: ${MINIO_SECRET_KEY:p5S3A9nDrUAvCjof}
  bucket-name: ${MINIO_BUCKET_NAME:hky-hr}
  region: us-east-1
  secure: false

# 文件服务配置
file:
  # 文件大小限制
  max-file-size: 100MB
  max-request-size: 100MB

  # 支持的文件类型
  allowed-types:
    - image/jpeg
    - image/png
    - image/gif
    - image/webp
    - application/pdf
    - application/msword
    - application/vnd.openxmlformats-officedocument.wordprocessingml.document
    - application/vnd.ms-excel
    - application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
    - application/vnd.ms-powerpoint
    - application/vnd.openxmlformats-officedocument.presentationml.presentation
    - text/plain
    - application/zip
    - application/x-rar-compressed

  # 预签名URL过期时间（秒）
  presigned-url-expiry: 3600

  # 文件存储路径模板
  path-template: "{module}/{bizId}/{fileType}/{date}/{uuid}.{ext}"

  # 文件版本控制
  versioning:
    enabled: true
    max-versions: 10

# 日志配置
logging:
  level:
    com.hzwangda.edu.file: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/file-service.log

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  prometheus:
    metrics:
      export:
        enabled: true
  # 链路追踪配置
  tracing:
    sampling:
      probability: 1.0
  zipkin:
    tracing:
      endpoint: ${ZIPKIN_ENDPOINT:http://localhost:9411/api/v2/spans}

# SpringDoc配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  info:
    title: 业务管理系统 - 文件管理服务 API
    description: 提供文件上传、下载、存储管理功能，基于MinIO对象存储
    version: 1.0.0
    contact:
      name: EDU-FILE-SYSTEM
      email: <EMAIL>

# Seata分布式事务配置（暂时禁用）
seata:
  enabled: false