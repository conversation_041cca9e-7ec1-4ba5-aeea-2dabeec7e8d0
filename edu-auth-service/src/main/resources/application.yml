server:
  port: 8001
  servlet:
    context-path: /auth-service

spring:
  application:
    name: edu-auth-service
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_SERVER_ADDR:127.0.0.1:8848}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:nacos}
        namespace: ${NACOS_NAMESPACE:hky-hr}
  datasource:
    url: jdbc:postgresql://${DB_HOST:************}:${DB_PORT:31252}/${DB_NAME:hky_hr_auth}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${DB_USER:sasa}
    password: ${DB_PWD:RApubone95}
    driver-class-name: org.postgresql.Driver
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: update
    show-sql: ${SPRING_JPA_SHOW_SQL:true}
    properties:
      hibernate:
        format_sql: true

  data:
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:31000}
      password: ${REDIS_PWD:sjyt_cywKZHAl}
      database: ${REDIS_DB:6}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms

cas:
  server:
    base-url: http://localhost:8080/realms/hr-college/protocol/cas
#    base-url: https://cas.zjjcxy.cn/cas
    login-url: ${cas.server.base-url}/login
    logout-url: ${cas.server.base-url}/logout
  service:
    base-url: http://localhost:${server.port}${server.servlet.context-path}
    login-url: ${cas.service.base-url}/cas/auth/login
    logout-url: ${cas.service.base-url}/cas/auth/logout

logging:
  level:
    root: INFO
    com.hzwangda.hr.common.auth: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# 链路追踪配置
management:
  tracing:
    sampling:
      probability: 1.0
  zipkin:
    tracing:
      endpoint: ${ZIPKIN_ENDPOINT:http://localhost:9411/api/v2/spans}
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,tracing

# Seata分布式事务配置（暂时禁用）
seata:
  enabled: false