package com.hzwangda.edu.auth.config;

import jakarta.annotation.Resource;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/7
 * @description CAS服务相关配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "cas.server")
public class CasConfig {
    private String baseUrl;
    private String loginUrl;
    private String logoutUrl;
    @Resource
    private CasServiceConfig casServiceConfig;
}
