package com.hzwangda.edu.auth.controller;

import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/auth")
public class AuthController {

    @GetMapping("/user")
    public Authentication getUser(Authentication authentication) {
        return authentication;
    }

    @GetMapping("/health")
    public String health() {
        return "Auth Service is running";
    }
}