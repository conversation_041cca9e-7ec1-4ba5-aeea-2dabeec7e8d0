package com.hzwangda.edu.common.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

// 临时注释掉javax.servlet依赖，使用jakarta.servlet
// import jakarta.servlet.FilterChain;
// import jakarta.servlet.ServletException;
// import jakarta.servlet.http.HttpServletRequest;
// import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * 安全验证过滤器
 * 集成SQL注入防护、XSS防护、IP黑名单检查等安全功能
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Component
@Order(1) // 最高优先级
public class SecurityValidationFilter extends OncePerRequestFilter {

    private static final Logger logger = LoggerFactory.getLogger(SecurityValidationFilter.class);

    @Autowired
    private SqlInjectionProtector sqlInjectionProtector;

    @Autowired
    private IpBlacklistService ipBlacklistService;

    @Autowired
    private ObjectMapper objectMapper;

    // 白名单路径，不进行安全检查
    private static final String[] WHITELIST_PATHS = {
        "/actuator/health",
        "/swagger-ui",
        "/v3/api-docs",
        "/favicon.ico",
        "/static/",
        "/public/"
    };

    // 限流配置
    private static final int DEFAULT_RATE_LIMIT = 100; // 每分钟100次请求
    private static final int RATE_LIMIT_WINDOW = 1; // 1分钟窗口

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
                                  FilterChain filterChain) throws ServletException, IOException {

        String requestUri = request.getRequestURI();
        String clientIp = getClientIp(request);
        String method = request.getMethod();

        logger.debug("安全验证开始: ip={}, method={}, uri={}", clientIp, method, requestUri);

        try {
            // 1. 检查是否在白名单中
            if (isWhitelistPath(requestUri)) {
                logger.debug("白名单路径，跳过安全检查: {}", requestUri);
                filterChain.doFilter(request, response);
                return;
            }

            // 2. 检查IP黑名单
            if (ipBlacklistService.isBlacklisted(clientIp)) {
                logger.warn("IP在黑名单中，拒绝访问: ip={}, uri={}", clientIp, requestUri);
                sendSecurityResponse(response, HttpStatus.FORBIDDEN, "IP已被封禁", "BLACKLISTED_IP");
                return;
            }

            // 3. 检查访问频率限制
            if (ipBlacklistService.checkRateLimit(clientIp, DEFAULT_RATE_LIMIT, RATE_LIMIT_WINDOW)) {
                logger.warn("访问频率超限: ip={}, uri={}", clientIp, requestUri);
                sendSecurityResponse(response, HttpStatus.TOO_MANY_REQUESTS, "访问频率过高", "RATE_LIMIT_EXCEEDED");
                return;
            }

            // 4. 检查请求参数安全性
            SecurityValidationResult validationResult = validateRequestSecurity(request);
            if (!validationResult.isValid()) {
                logger.warn("请求参数安全检查失败: ip={}, uri={}, risks={}",
                           clientIp, requestUri, validationResult.getRisks());

                // 记录违规行为
                if (validationResult.isSqlInjectionDetected()) {
                    ipBlacklistService.recordViolation(clientIp, IpBlacklistService.ViolationType.SQL_INJECTION);
                }
                if (validationResult.isXssDetected()) {
                    ipBlacklistService.recordViolation(clientIp, IpBlacklistService.ViolationType.XSS_ATTACK);
                }

                sendSecurityResponse(response, HttpStatus.BAD_REQUEST, "请求参数包含恶意内容", "MALICIOUS_REQUEST");
                return;
            }

            // 5. 安全检查通过，继续处理请求
            logger.debug("安全验证通过: ip={}, uri={}", clientIp, requestUri);
            filterChain.doFilter(request, response);

        } catch (Exception e) {
            logger.error("安全验证过程中发生异常: ip={}, uri={}", clientIp, requestUri, e);
            sendSecurityResponse(response, HttpStatus.INTERNAL_SERVER_ERROR, "安全验证异常", "VALIDATION_ERROR");
        }
    }

    /**
     * 验证请求安全性
     */
    private SecurityValidationResult validateRequestSecurity(HttpServletRequest request) {
        SecurityValidationResult result = new SecurityValidationResult();
        result.setValid(true);

        // 检查URL参数
        String queryString = request.getQueryString();
        if (StringUtils.hasText(queryString)) {
            SqlInjectionProtector.ValidationResult queryValidation = sqlInjectionProtector.validateInput(queryString);
            if (!queryValidation.isValid()) {
                result.setValid(false);
                result.setSqlInjectionDetected(queryValidation.isSqlInjectionDetected());
                result.setXssDetected(queryValidation.isXssDetected());
                result.addRisk("URL参数包含恶意内容: " + String.join(", ", queryValidation.getRisks()));
            }
        }

        // 检查请求参数
        Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            String[] paramValues = request.getParameterValues(paramName);

            if (paramValues != null) {
                for (String paramValue : paramValues) {
                    SqlInjectionProtector.ValidationResult paramValidation = sqlInjectionProtector.validateInput(paramValue);
                    if (!paramValidation.isValid()) {
                        result.setValid(false);
                        result.setSqlInjectionDetected(paramValidation.isSqlInjectionDetected());
                        result.setXssDetected(paramValidation.isXssDetected());
                        result.addRisk("参数 " + paramName + " 包含恶意内容: " + String.join(", ", paramValidation.getRisks()));
                    }
                }
            }
        }

        // 检查请求头
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);

            if (StringUtils.hasText(headerValue)) {
                // 只检查可能包含用户输入的请求头
                if (isUserInputHeader(headerName)) {
                    SqlInjectionProtector.ValidationResult headerValidation = sqlInjectionProtector.validateInput(headerValue);
                    if (!headerValidation.isValid()) {
                        result.setValid(false);
                        result.setSqlInjectionDetected(headerValidation.isSqlInjectionDetected());
                        result.setXssDetected(headerValidation.isXssDetected());
                        result.addRisk("请求头 " + headerName + " 包含恶意内容: " + String.join(", ", headerValidation.getRisks()));
                    }
                }
            }
        }

        return result;
    }

    /**
     * 检查是否为用户输入的请求头
     */
    private boolean isUserInputHeader(String headerName) {
        if (headerName == null) {
            return false;
        }

        String lowerHeaderName = headerName.toLowerCase();
        return lowerHeaderName.contains("user") ||
               lowerHeaderName.contains("custom") ||
               lowerHeaderName.contains("x-") ||
               lowerHeaderName.equals("referer") ||
               lowerHeaderName.equals("origin");
    }

    /**
     * 检查是否为白名单路径
     */
    private boolean isWhitelistPath(String requestUri) {
        if (requestUri == null) {
            return false;
        }

        for (String whitelistPath : WHITELIST_PATHS) {
            if (requestUri.startsWith(whitelistPath)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * 发送安全响应
     */
    private void sendSecurityResponse(HttpServletResponse response, HttpStatus status,
                                    String message, String errorCode) throws IOException {
        response.setStatus(status.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("code", errorCode);
        errorResponse.put("message", message);
        errorResponse.put("timestamp", System.currentTimeMillis());

        String jsonResponse = objectMapper.writeValueAsString(errorResponse);
        response.getWriter().write(jsonResponse);
        response.getWriter().flush();
    }

    /**
     * 安全验证结果
     */
    private static class SecurityValidationResult {
        private boolean valid;
        private boolean sqlInjectionDetected;
        private boolean xssDetected;
        private java.util.List<String> risks = new java.util.ArrayList<>();

        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }

        public boolean isSqlInjectionDetected() { return sqlInjectionDetected; }
        public void setSqlInjectionDetected(boolean sqlInjectionDetected) { this.sqlInjectionDetected = sqlInjectionDetected; }

        public boolean isXssDetected() { return xssDetected; }
        public void setXssDetected(boolean xssDetected) { this.xssDetected = xssDetected; }

        public java.util.List<String> getRisks() { return risks; }
        public void setRisks(java.util.List<String> risks) { this.risks = risks; }

        public void addRisk(String risk) {
            if (this.risks == null) {
                this.risks = new java.util.ArrayList<>();
            }
            this.risks.add(risk);
        }
    }
}
