# 平台服务统一配置模板
# 所有业务服务都应该引用此配置，确保平台服务集成的一致性

# 平台服务配置
hky:
  platform-services:
    # Feign客户端全局配置
    connect-timeout: 5000      # 连接超时时间（毫秒）
    read-timeout: 10000        # 读取超时时间（毫秒）
    retry-period: 1000         # 重试间隔时间（毫秒）
    max-retry-period: 3000     # 最大重试间隔时间（毫秒）
    max-attempts: 3            # 最大重试次数
    
    # 熔断器全局配置
    circuit-breaker-timeout: 10000                              # 熔断器超时时间（毫秒）
    circuit-breaker-sliding-window-size: 10                     # 滑动窗口大小
    circuit-breaker-minimum-number-of-calls: 5                  # 最小调用次数
    circuit-breaker-failure-rate-threshold: 50.0               # 失败率阈值（百分比）
    circuit-breaker-wait-duration-in-open-state: 30            # 熔断器打开状态等待时间（秒）
    circuit-breaker-permitted-number-of-calls-in-half-open-state: 3  # 半开状态允许的调用次数
    
    # 审计服务配置
    audit:
      url: ${HKY_AUDIT_SERVICE_URL:http://localhost:8004}
      timeout: 5000
      name: hky-audit-service
      enabled: true
      health-check-path: /actuator/health
      description: 审计日志服务
    
    # 工作流服务配置
    workflow:
      url: ${HKY_WORKFLOW_SERVICE_URL:http://localhost:8006}
      timeout: 15000  # 工作流操作可能较慢，设置较长超时时间
      name: hky-workflow-service
      enabled: true
      health-check-path: /actuator/health
      description: 工作流引擎服务
    
    # 通知服务配置
    notification:
      url: ${HKY_NOTIFICATION_SERVICE_URL:http://localhost:8007}
      timeout: 8000
      name: hky-notification-service
      enabled: true
      health-check-path: /actuator/health
      description: 消息通知服务
    
    # 字典服务配置
    dictionary:
      url: ${HKY_DICTIONARY_SERVICE_URL:http://localhost:8008}
      timeout: 3000   # 字典服务响应快，设置较短超时时间
      name: hky-dictionary-service
      enabled: true
      health-check-path: /actuator/health
      description: 数据字典服务

# Feign客户端配置
feign:
  client:
    config:
      default:
        connect-timeout: ${hky.platform-services.connect-timeout}
        read-timeout: ${hky.platform-services.read-timeout}
        logger-level: basic
        error-decoder: com.hky.hr.common.config.PlatformServiceFeignConfig.PlatformServiceErrorDecoder
        retryer: com.hky.hr.common.config.PlatformServiceFeignConfig
        request-interceptors:
          - com.hky.hr.common.interceptor.FeignRequestInterceptor
      
      # 审计服务专用配置
      hky-audit-service:
        connect-timeout: 3000
        read-timeout: ${hky.platform-services.audit.timeout}
        logger-level: basic
      
      # 工作流服务专用配置  
      hky-workflow-service:
        connect-timeout: 8000
        read-timeout: ${hky.platform-services.workflow.timeout}
        logger-level: basic
      
      # 通知服务专用配置
      hky-notification-service:
        connect-timeout: 5000
        read-timeout: ${hky.platform-services.notification.timeout}
        logger-level: basic
      
      # 字典服务专用配置
      hky-dictionary-service:
        connect-timeout: 2000
        read-timeout: ${hky.platform-services.dictionary.timeout}
        logger-level: basic
  
  # 启用请求/响应压缩
  compression:
    request:
      enabled: true
      mime-types: text/xml,application/xml,application/json
      min-request-size: 2048
    response:
      enabled: true
  
  # 启用指标收集
  metrics:
    enabled: true
  
  # HTTP客户端配置
  httpclient:
    enabled: true
    max-connections: 200
    max-connections-per-route: 50
    connection-timeout: ${hky.platform-services.connect-timeout}
    connection-timer-repeat: 3000
    follow-redirects: true

# Spring Cloud LoadBalancer配置
spring:
  cloud:
    loadbalancer:
      ribbon:
        enabled: false
      cache:
        enabled: true
        ttl: 35s
        capacity: 256
      health-check:
        initial-delay: 0
        interval: 25s
        path:
          default: /actuator/health

# Resilience4j熔断器配置
resilience4j:
  circuitbreaker:
    configs:
      default:
        sliding-window-size: ${hky.platform-services.circuit-breaker-sliding-window-size}
        minimum-number-of-calls: ${hky.platform-services.circuit-breaker-minimum-number-of-calls}
        failure-rate-threshold: ${hky.platform-services.circuit-breaker-failure-rate-threshold}
        wait-duration-in-open-state: ${hky.platform-services.circuit-breaker-wait-duration-in-open-state}s
        permitted-number-of-calls-in-half-open-state: ${hky.platform-services.circuit-breaker-permitted-number-of-calls-in-half-open-state}
        automatic-transition-from-open-to-half-open-enabled: true
        record-exceptions:
          - java.lang.Exception
        ignore-exceptions:
          - java.lang.IllegalArgumentException
    instances:
      hky-audit-service:
        base-config: default
        failure-rate-threshold: 70.0
        wait-duration-in-open-state: 15s
      hky-workflow-service:
        base-config: default
        failure-rate-threshold: 60.0
        wait-duration-in-open-state: 45s
        sliding-window-size: 15
      hky-notification-service:
        base-config: default
        failure-rate-threshold: 80.0
        wait-duration-in-open-state: 10s
      hky-dictionary-service:
        base-config: default
        failure-rate-threshold: 40.0
        wait-duration-in-open-state: 20s
        sliding-window-size: 8
  
  # 重试配置
  retry:
    configs:
      default:
        max-attempts: ${hky.platform-services.max-attempts}
        wait-duration: ${hky.platform-services.retry-period}ms
        retry-exceptions:
          - java.net.SocketTimeoutException
          - java.net.ConnectException
          - feign.RetryableException
        ignore-exceptions:
          - java.lang.IllegalArgumentException
          - java.lang.SecurityException
    instances:
      hky-audit-service:
        base-config: default
        max-attempts: 2  # 审计服务重试次数较少
      hky-workflow-service:
        base-config: default
        max-attempts: 4  # 工作流服务允许更多重试
        wait-duration: 2000ms
      hky-notification-service:
        base-config: default
        max-attempts: 2  # 通知服务重试次数较少
      hky-dictionary-service:
        base-config: default
        max-attempts: 3
        wait-duration: 500ms  # 字典服务重试间隔较短

# 监控和指标配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,circuitbreakers,retries
  endpoint:
    health:
      show-details: always
      show-components: always
  health:
    circuitbreakers:
      enabled: true
    ratelimiters:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
        resilience4j.circuitbreaker.calls: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
        resilience4j.circuitbreaker.calls: 0.5, 0.95, 0.99

# 日志配置
logging:
  level:
    com.hky.hr.common.config.PlatformServiceFeignConfig: DEBUG
    com.hky.hr.common.monitor: INFO
    feign: DEBUG
    org.springframework.cloud.openfeign: DEBUG
    io.github.resilience4j: INFO
