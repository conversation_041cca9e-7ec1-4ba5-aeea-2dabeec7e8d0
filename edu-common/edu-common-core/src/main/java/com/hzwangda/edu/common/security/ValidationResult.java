package com.hzwangda.edu.common.security;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 输入验证结果
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
public class ValidationResult {

    /**
     * 原始输入
     */
    private String originalInput;

    /**
     * 清理后的输入
     */
    private String cleanedInput;

    /**
     * 是否验证通过
     */
    private boolean valid;

    /**
     * 是否检测到XSS攻击
     */
    private boolean xssDetected;

    /**
     * 是否检测到路径遍历攻击
     */
    private boolean pathTraversalDetected;

    /**
     * 是否检测到命令注入攻击
     */
    private boolean commandInjectionDetected;

    /**
     * 风险列表
     */
    private List<String> risks = new ArrayList<>();

    /**
     * 添加风险信息
     *
     * @param risk 风险描述
     */
    public void addRisk(String risk) {
        this.risks.add(risk);
    }

    /**
     * 是否有安全风险
     *
     * @return 是否有风险
     */
    public boolean hasRisk() {
        return !risks.isEmpty();
    }

    /**
     * 获取风险描述
     *
     * @return 风险描述字符串
     */
    public String getRiskDescription() {
        return String.join(", ", risks);
    }

    /**
     * 是否需要清理
     *
     * @return 是否需要清理
     */
    public boolean needsCleaning() {
        return xssDetected || pathTraversalDetected || commandInjectionDetected;
    }
}
