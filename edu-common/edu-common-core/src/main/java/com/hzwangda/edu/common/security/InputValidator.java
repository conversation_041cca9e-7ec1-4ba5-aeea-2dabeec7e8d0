package com.hzwangda.edu.common.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.regex.Pattern;

/**
 * 输入验证工具类
 * 替代SqlInjectionProtector，专注于输入验证而非SQL注入防护
 * SQL注入防护应该通过参数化查询来实现
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
public class InputValidator {

    // XSS攻击模式
    private static final Pattern[] XSS_PATTERNS = {
        Pattern.compile("<script[^>]*>.*?</script>", Pattern.CASE_INSENSITIVE),
        Pattern.compile("javascript:", Pattern.CASE_INSENSITIVE),
        Pattern.compile("vbscript:", Pattern.CASE_INSENSITIVE),
        Pattern.compile("onload\\s*=", Pattern.CASE_INSENSITIVE),
        Pattern.compile("onerror\\s*=", Pattern.CASE_INSENSITIVE),
        Pattern.compile("onclick\\s*=", Pattern.CASE_INSENSITIVE),
        Pattern.compile("onmouseover\\s*=", Pattern.CASE_INSENSITIVE)
    };

    // 文件路径遍历模式
    private static final Pattern[] PATH_TRAVERSAL_PATTERNS = {
        Pattern.compile("\\.\\./"),
        Pattern.compile("\\.\\.\\\\"),
        Pattern.compile("%2e%2e%2f", Pattern.CASE_INSENSITIVE),
        Pattern.compile("%2e%2e\\\\", Pattern.CASE_INSENSITIVE)
    };

    // 命令注入模式
    private static final Pattern[] COMMAND_INJECTION_PATTERNS = {
        Pattern.compile("[;&|`]"),
        Pattern.compile("\\$\\(.*\\)"),
        Pattern.compile("`.*`")
    };

    /**
     * 验证输入是否安全
     *
     * @param input 输入字符串
     * @return 验证结果
     */
    public static ValidationResult validateInput(String input) {
        ValidationResult result = new ValidationResult();
        result.setOriginalInput(input);
        result.setValid(true);

        if (!StringUtils.hasText(input)) {
            result.setCleanedInput(input);
            return result;
        }

        // 检查XSS攻击
        if (containsXss(input)) {
            result.setValid(false);
            result.setXssDetected(true);
            result.addRisk("检测到XSS攻击模式");
        }

        // 检查路径遍历攻击
        if (containsPathTraversal(input)) {
            result.setValid(false);
            result.setPathTraversalDetected(true);
            result.addRisk("检测到路径遍历攻击");
        }

        // 检查命令注入攻击
        if (containsCommandInjection(input)) {
            result.setValid(false);
            result.setCommandInjectionDetected(true);
            result.addRisk("检测到命令注入攻击");
        }

        // 清理输入
        String cleaned = input;
        if (result.isXssDetected()) {
            cleaned = cleanXss(cleaned);
        }
        if (result.isPathTraversalDetected()) {
            cleaned = cleanPathTraversal(cleaned);
        }
        if (result.isCommandInjectionDetected()) {
            cleaned = cleanCommandInjection(cleaned);
        }

        result.setCleanedInput(cleaned);
        return result;
    }

    /**
     * 检查是否包含XSS攻击
     */
    public static boolean containsXss(String input) {
        if (!StringUtils.hasText(input)) {
            return false;
        }

        for (Pattern pattern : XSS_PATTERNS) {
            if (pattern.matcher(input).find()) {
                log.warn("检测到XSS攻击模式: {} in input: {}", pattern.pattern(), input);
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否包含路径遍历攻击
     */
    public static boolean containsPathTraversal(String input) {
        if (!StringUtils.hasText(input)) {
            return false;
        }

        for (Pattern pattern : PATH_TRAVERSAL_PATTERNS) {
            if (pattern.matcher(input).find()) {
                log.warn("检测到路径遍历攻击: {} in input: {}", pattern.pattern(), input);
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否包含命令注入攻击
     */
    public static boolean containsCommandInjection(String input) {
        if (!StringUtils.hasText(input)) {
            return false;
        }

        for (Pattern pattern : COMMAND_INJECTION_PATTERNS) {
            if (pattern.matcher(input).find()) {
                log.warn("检测到命令注入攻击: {} in input: {}", pattern.pattern(), input);
                return true;
            }
        }
        return false;
    }

    /**
     * 清理XSS攻击字符
     */
    public static String cleanXss(String input) {
        if (!StringUtils.hasText(input)) {
            return input;
        }

        String cleaned = input;
        for (Pattern pattern : XSS_PATTERNS) {
            cleaned = pattern.matcher(cleaned).replaceAll("");
        }

        // 转义HTML特殊字符
        cleaned = cleaned.replace("<", "&lt;")
                        .replace(">", "&gt;")
                        .replace("\"", "&quot;")
                        .replace("'", "&#x27;")
                        .replace("/", "&#x2F;");

        return cleaned;
    }

    /**
     * 清理路径遍历攻击字符
     */
    public static String cleanPathTraversal(String input) {
        if (!StringUtils.hasText(input)) {
            return input;
        }

        String cleaned = input;
        for (Pattern pattern : PATH_TRAVERSAL_PATTERNS) {
            cleaned = pattern.matcher(cleaned).replaceAll("");
        }
        return cleaned;
    }

    /**
     * 清理命令注入攻击字符
     */
    public static String cleanCommandInjection(String input) {
        if (!StringUtils.hasText(input)) {
            return input;
        }

        String cleaned = input;
        for (Pattern pattern : COMMAND_INJECTION_PATTERNS) {
            cleaned = pattern.matcher(cleaned).replaceAll("");
        }
        return cleaned;
    }

    /**
     * 验证文件名是否安全
     */
    public static boolean isValidFileName(String fileName) {
        if (!StringUtils.hasText(fileName)) {
            return false;
        }

        // 检查文件名长度
        if (fileName.length() > 255) {
            return false;
        }

        // 检查非法字符
        String[] illegalChars = {"<", ">", ":", "\"", "|", "?", "*", "\0"};
        for (String illegalChar : illegalChars) {
            if (fileName.contains(illegalChar)) {
                return false;
            }
        }

        // 检查路径遍历
        return !containsPathTraversal(fileName);
    }

    /**
     * 验证邮箱格式
     */
    public static boolean isValidEmail(String email) {
        if (!StringUtils.hasText(email)) {
            return false;
        }
        
        Pattern emailPattern = Pattern.compile(
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
        );
        return emailPattern.matcher(email).matches();
    }

    /**
     * 验证手机号格式
     */
    public static boolean isValidPhone(String phone) {
        if (!StringUtils.hasText(phone)) {
            return false;
        }
        
        Pattern phonePattern = Pattern.compile("^1[3-9]\\d{9}$");
        return phonePattern.matcher(phone).matches();
    }

    /**
     * 验证URL格式
     */
    public static boolean isValidUrl(String url) {
        if (!StringUtils.hasText(url)) {
            return false;
        }
        
        Pattern urlPattern = Pattern.compile(
            "^(https?|ftp)://[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}(/.*)?$"
        );
        return urlPattern.matcher(url).matches();
    }
}
