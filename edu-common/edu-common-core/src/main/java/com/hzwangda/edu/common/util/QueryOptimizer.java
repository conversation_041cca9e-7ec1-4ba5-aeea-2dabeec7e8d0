package com.hzwangda.edu.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 查询优化工具类
 * 提供解决N+1查询问题的通用方法
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
public class QueryOptimizer {

    /**
     * 批量查询并映射关联数据，解决N+1问题
     *
     * @param <T> 主实体类型
     * @param <K> 关联键类型
     * @param <R> 关联实体类型
     * @param mainEntities 主实体列表
     * @param keyExtractor 从主实体提取关联键的函数
     * @param batchLoader 批量加载关联数据的函数
     * @param associationSetter 设置关联数据的函数
     */
    public static <T, K, R> void batchLoadAssociations(
            List<T> mainEntities,
            Function<T, K> keyExtractor,
            Function<List<K>, Map<K, R>> batchLoader,
            BiConsumer<T, R> associationSetter) {
        
        if (mainEntities == null || mainEntities.isEmpty()) {
            return;
        }

        // 提取所有关联键
        List<K> keys = mainEntities.stream()
                .map(keyExtractor)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (keys.isEmpty()) {
            return;
        }

        // 批量加载关联数据
        Map<K, R> associationMap = batchLoader.apply(keys);

        // 设置关联数据
        for (T entity : mainEntities) {
            K key = keyExtractor.apply(entity);
            if (key != null) {
                R association = associationMap.get(key);
                associationSetter.accept(entity, association);
            }
        }
    }

    /**
     * 批量查询并映射一对多关联数据
     *
     * @param <T> 主实体类型
     * @param <K> 关联键类型
     * @param <R> 关联实体类型
     * @param mainEntities 主实体列表
     * @param keyExtractor 从主实体提取关联键的函数
     * @param batchLoader 批量加载关联数据的函数
     * @param associationSetter 设置关联数据列表的函数
     */
    public static <T, K, R> void batchLoadOneToManyAssociations(
            List<T> mainEntities,
            Function<T, K> keyExtractor,
            Function<List<K>, List<R>> batchLoader,
            Function<R, K> associationKeyExtractor,
            BiConsumer<T, List<R>> associationSetter) {
        
        if (mainEntities == null || mainEntities.isEmpty()) {
            return;
        }

        // 提取所有关联键
        List<K> keys = mainEntities.stream()
                .map(keyExtractor)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (keys.isEmpty()) {
            return;
        }

        // 批量加载关联数据
        List<R> associations = batchLoader.apply(keys);

        // 按关联键分组
        Map<K, List<R>> associationMap = associations.stream()
                .collect(Collectors.groupingBy(associationKeyExtractor));

        // 设置关联数据
        for (T entity : mainEntities) {
            K key = keyExtractor.apply(entity);
            if (key != null) {
                List<R> entityAssociations = associationMap.getOrDefault(key, Collections.emptyList());
                associationSetter.accept(entity, entityAssociations);
            }
        }
    }

    /**
     * 分批处理大量数据，避免内存溢出
     *
     * @param <T> 数据类型
     * @param data 数据列表
     * @param batchSize 批次大小
     * @param processor 批次处理器
     */
    public static <T> void processBatches(List<T> data, int batchSize, Consumer<List<T>> processor) {
        if (data == null || data.isEmpty()) {
            return;
        }

        if (batchSize <= 0) {
            throw new IllegalArgumentException("批次大小必须大于0");
        }

        for (int i = 0; i < data.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, data.size());
            List<T> batch = data.subList(i, endIndex);
            processor.accept(batch);
        }
    }

    /**
     * 优化分页查询，预加载关联数据
     *
     * @param <T> 主实体类型
     * @param <K> 关联键类型
     * @param <R> 关联实体类型
     * @param page 分页结果
     * @param keyExtractor 从主实体提取关联键的函数
     * @param batchLoader 批量加载关联数据的函数
     * @param associationSetter 设置关联数据的函数
     * @return 优化后的分页结果
     */
    public static <T, K, R> Page<T> optimizePage(
            Page<T> page,
            Function<T, K> keyExtractor,
            Function<List<K>, Map<K, R>> batchLoader,
            BiConsumer<T, R> associationSetter) {
        
        List<T> content = page.getContent();
        batchLoadAssociations(content, keyExtractor, batchLoader, associationSetter);
        
        return new PageImpl<>(content, page.getPageable(), page.getTotalElements());
    }

    /**
     * 创建IN查询的安全批次
     * 避免IN子句过长导致的性能问题
     *
     * @param <T> 参数类型
     * @param <R> 结果类型
     * @param parameters 参数列表
     * @param maxBatchSize 最大批次大小
     * @param queryFunction 查询函数
     * @return 合并后的结果列表
     */
    public static <T, R> List<R> batchInQuery(
            List<T> parameters,
            int maxBatchSize,
            Function<List<T>, List<R>> queryFunction) {
        
        if (parameters == null || parameters.isEmpty()) {
            return Collections.emptyList();
        }

        if (maxBatchSize <= 0) {
            maxBatchSize = 1000; // 默认批次大小
        }

        List<R> results = new ArrayList<>();
        
        for (int i = 0; i < parameters.size(); i += maxBatchSize) {
            int endIndex = Math.min(i + maxBatchSize, parameters.size());
            List<T> batch = parameters.subList(i, endIndex);
            List<R> batchResults = queryFunction.apply(batch);
            results.addAll(batchResults);
        }
        
        return results;
    }

    /**
     * 缓存查询结果，避免重复查询
     *
     * @param <K> 缓存键类型
     * @param <V> 缓存值类型
     * @param cache 缓存Map
     * @param key 缓存键
     * @param loader 数据加载器
     * @return 缓存值
     */
    public static <K, V> V cacheQuery(Map<K, V> cache, K key, Function<K, V> loader) {
        return cache.computeIfAbsent(key, loader);
    }

    /**
     * 批量缓存查询结果
     *
     * @param <K> 缓存键类型
     * @param <V> 缓存值类型
     * @param cache 缓存Map
     * @param keys 缓存键列表
     * @param batchLoader 批量数据加载器
     * @return 缓存值Map
     */
    public static <K, V> Map<K, V> batchCacheQuery(
            Map<K, V> cache,
            List<K> keys,
            Function<List<K>, Map<K, V>> batchLoader) {
        
        // 找出未缓存的键
        List<K> uncachedKeys = keys.stream()
                .filter(key -> !cache.containsKey(key))
                .collect(Collectors.toList());
        
        // 批量加载未缓存的数据
        if (!uncachedKeys.isEmpty()) {
            Map<K, V> newData = batchLoader.apply(uncachedKeys);
            cache.putAll(newData);
        }
        
        // 返回所有请求的数据
        return keys.stream()
                .filter(cache::containsKey)
                .collect(Collectors.toMap(
                    Function.identity(),
                    cache::get,
                    (existing, replacement) -> existing,
                    LinkedHashMap::new
                ));
    }

    // 函数式接口定义
    @FunctionalInterface
    public interface BiConsumer<T, U> {
        void accept(T t, U u);
    }

    @FunctionalInterface
    public interface Consumer<T> {
        void accept(T t);
    }
}
