package com.hzwangda.edu.common.sync;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 数据同步服务接口
 * 用于确保跨服务数据的一致性
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public interface DataSyncService {

    /**
     * 同步单个实体数据
     *
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @param data 数据内容
     * @param operation 操作类型（CREATE, UPDATE, DELETE）
     * @return 同步结果
     */
    SyncResult syncEntity(String entityType, String entityId, Map<String, Object> data, SyncOperation operation);

    /**
     * 批量同步实体数据
     *
     * @param syncRequests 同步请求列表
     * @return 批量同步结果
     */
    BatchSyncResult batchSyncEntities(List<SyncRequest> syncRequests);

    /**
     * 检查数据一致性
     *
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @return 一致性检查结果
     */
    ConsistencyCheckResult checkConsistency(String entityType, String entityId);

    /**
     * 修复数据不一致
     *
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @param sourceService 数据源服务
     * @return 修复结果
     */
    RepairResult repairInconsistency(String entityType, String entityId, String sourceService);

    /**
     * 获取同步状态
     *
     * @param syncId 同步ID
     * @return 同步状态
     */
    SyncStatus getSyncStatus(String syncId);

    /**
     * 同步操作枚举
     */
    enum SyncOperation {
        CREATE, UPDATE, DELETE, BATCH_UPDATE
    }

    /**
     * 同步请求
     */
    @Getter
    @Setter
    class SyncRequest {
        private String entityType;
        private String entityId;
        private Map<String, Object> data;
        private SyncOperation operation;
        private String sourceService;
        private Long timestamp;

        // 构造函数
        public SyncRequest(String entityType, String entityId, Map<String, Object> data,
                          SyncOperation operation, String sourceService) {
            this.entityType = entityType;
            this.entityId = entityId;
            this.data = data;
            this.operation = operation;
            this.sourceService = sourceService;
            this.timestamp = System.currentTimeMillis();
        }
    }

    /**
     * 同步结果
     */
    @Getter
    @Setter
    class SyncResult {
        private boolean success;
        private String syncId;
        private String message;
        private Long timestamp;
        private Map<String, Object> metadata;

        public SyncResult(boolean success, String syncId, String message) {
            this.success = success;
            this.syncId = syncId;
            this.message = message;
            this.timestamp = System.currentTimeMillis();
        }
    }

    /**
     * 批量同步结果
     */
    @Getter
    @Setter
    class BatchSyncResult {
        private int totalCount;
        private int successCount;
        private int failureCount;
        private List<SyncResult> results;
        private Long timestamp;

        public BatchSyncResult(int totalCount, int successCount, int failureCount, List<SyncResult> results) {
            this.totalCount = totalCount;
            this.successCount = successCount;
            this.failureCount = failureCount;
            this.results = results;
            this.timestamp = System.currentTimeMillis();
        }
    }

    /**
     * 一致性检查结果
     */
    @Getter
    @Setter
    class ConsistencyCheckResult {
        private boolean consistent;
        private String entityType;
        private String entityId;
        private Map<String, Object> differences;
        private List<String> inconsistentServices;
        private Long timestamp;

        public ConsistencyCheckResult(boolean consistent, String entityType, String entityId) {
            this.consistent = consistent;
            this.entityType = entityType;
            this.entityId = entityId;
            this.timestamp = System.currentTimeMillis();
        }

    }

    /**
     * 修复结果
     */
    @Getter
    @Setter
    class RepairResult {
        private boolean success;
        private String message;
        private int repairedCount;
        private Long timestamp;

        public RepairResult(boolean success, String message, int repairedCount) {
            this.success = success;
            this.message = message;
            this.repairedCount = repairedCount;
            this.timestamp = System.currentTimeMillis();
        }

    }

    /**
     * 同步状态
     */
    enum SyncStatus {
        PENDING, IN_PROGRESS, COMPLETED, FAILED, CANCELLED
    }
}
