package com.hzwangda.edu.common.idWorker;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 分布式id
 * SnowFlake的结构如下(每部分用-分开):
 * 0 - 0000000000 0000000000 0000000000 0000000000 0 - 00000 - 00000 - 000000000000
 * 1位标识，由于long基本类型在Java中是带符号的，最高位是符号位，正数是0，负数是1，所以id一般是正数，最高位是0<br>
 * 41位时间截(毫秒级)，注意，41位时间截不是存储当前时间的时间截，而是存储时间截的差值（当前时间截 - 开始时间截)得到的值
 * 这里的的开始时间截，一般是我们的id生成器开始使用的时间，由我们程序来指定的（如下下面程序IdWorker类的startTime属性）。41位的时间截，可以使用69年，年T = (1L << 41) / (1000L * 60 * 60 * 24 * 365) = 69<br>
 * 10位的数据机器位，可以部署在2^10 1024个节点，包括5位datacenterId和5位workerId<br>
 * 12位序列，毫秒内的计数，12位的计数顺序号支持每个节点每毫秒(同一机器，同一时间截)产生4096个ID序号<br>
 * 加起来刚好64位，为一个Long型。<br>
 * SnowFlake的优点是，整体上按照时间自增排序，并且整个分布式系统内不会产生ID碰撞(由数据中心ID和机器ID作区分)，并且效率较高，经测试，SnowFlake每秒能够产生26万ID左右。
 *
 * 缩短算法后空间划分
 * 1, 高位32bit作为秒级时间戳, 时间戳减去固定值(2020年时间戳), 最高可支持到2106年(1970 + 2^32 / 3600 / 24 / 365 ≈ 2106)
 * 2, 4bit作为机器标识, 最高可部署16台机器
 * 3, 最后10bit作为自增序列, 单节点最高每秒 2^10 = 1024个ID
 * PS: 如果需要部署更多节点, 可以适当调整机器位和自增序列的位长, 如机器位7bit, 自增序列14bit, 这样一来就变成了支持2^7=128个节点, 单节点每秒2^14=16384个自增序列
 * Created on 18/6/15.
 */
@Component
public class IdWorker {

    //开始该类生成ID的时间截
    private final long startTime = 1751276460000L;

    //机器id所占的位数
    private long workerIdBits = 2L;

    //数据标识id所占的位数
    private long datacenterIdBits = 2L;

    //支持的最大机器id
    private long maxWorkerId = -1L ^ (-1L << workerIdBits);

    //支持的最大数据标识id
    private long maxDatacenterId = -1L ^ (-1L << datacenterIdBits);

    //序列在id中占的位数
    private long sequenceBits = 10L;

    //机器id向左移的位数
    private long workerIdLeftShift = sequenceBits;

    //数据标识id向左移的位数
    private long datacenterIdLeftShift = workerIdBits + workerIdLeftShift;

    //时间截向左移的位置
    private long timestampLeftShift = datacenterIdBits + datacenterIdLeftShift;

    //生成序列的掩码
    private long sequenceMask = -1 ^ (-1 << sequenceBits);

    // 工作机器ID(0~3)
    private long workerId;

    // 数据中心ID(0~3)
    private long datacenterId;

    //同一个时间截内生成的序列数，初始值是0，从0开始
    private long sequence = 0L;

    //上次生成id的时间截
    private long lastTimestamp = -1L;

    @Resource
    private IdWorkerConfig idWorkerConfig;
    private static IdWorkerConfig idWorkerConfigStatic;

    @PostConstruct
    public void setIdWorkerConfigStatic() {
        IdWorker.idWorkerConfigStatic = idWorkerConfig;
    }

    private static IdWorker idWorker = null;

    private IdWorker() {}

    public static IdWorker getInstance() {
        if (idWorker == null) {
            synchronized (IdWorker.class) {
                if (idWorker == null) {
                    if (Objects.nonNull(idWorkerConfigStatic) && StringUtils.isNotEmpty(idWorkerConfigStatic.getDatacenterWorkerIds())) {
                        /*
                        代表工作空间值和计算机值
                        */
                        String[] str = idWorkerConfigStatic.getDatacenterWorkerIds().split("-");

                        long workerId = 0;
                        long datacenterId = 0;

                        try {
                            workerId = Long.parseLong(str[1]);
                            datacenterId = Long.parseLong(str[0]);
                            System.out.println(String.format("datacenterWorkerIds配置: datacenterId[%d] workerId[%d]", datacenterId, workerId));
                        } catch (Exception e) {
                            System.out.println("datacenterWorkerIds配置要求格式不正确，如:0-0");
                        } finally {
                            idWorker = new IdWorker(workerId, datacenterId);
                        }
                    } else {
                        System.out.println("datacenterWorkerIds配置要求格式不正确，如:0-0，使用默认随机");
                        idWorker = new IdWorker(-1, -1);
                    }
                }
            }
        }
        return idWorker;
    }

    /**
     * 构造函数
     * @param workerId 工作ID (0~3)
     * @param datacenterId 数据中心ID (0~3)
     */
    private IdWorker(long workerId, long datacenterId) {
        if (workerId < 0 || workerId > maxWorkerId) {
            workerId = (long)Math.floor(Math.random() * maxWorkerId);
        }
        if (datacenterId < 0 || datacenterId > maxDatacenterId) {
            datacenterId = (long)Math.floor(Math.random() * maxDatacenterId);
        }
        this.workerId = workerId;
        this.datacenterId = datacenterId;
    }

    /**
     * 获得下一个ID (该方法是线程安全的)
     * 优化版本：使用CAS操作和更高效的时钟回拨处理
     * @return SnowflakeId
     */
    public synchronized long nextId() {
        long timestamp = timeGen();

        // 处理时钟回拨
        if (timestamp < lastTimestamp) {
            long offset = lastTimestamp - timestamp;
            if (offset <= 5) {
                // 小幅回拨，等待时钟追上
                try {
                    Thread.sleep(offset << 1);
                    timestamp = timeGen();
                    if (timestamp < lastTimestamp) {
                        throw new RuntimeException(
                            String.format("Clock moved backwards. Refusing to generate id for %d milliseconds",
                                        lastTimestamp - timestamp));
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Interrupted while waiting for clock", e);
                }
            } else {
                throw new RuntimeException(
                    String.format("Clock moved backwards. Refusing to generate id for %d milliseconds", offset));
            }
        }

        // 同一毫秒内序列号递增
        if (timestamp == lastTimestamp) {
            sequence = (sequence + 1) & sequenceMask;
            // 序列号溢出，等待下一毫秒
            if (sequence == 0) {
                timestamp = tilNextMillis();
                sequence = 0L;
            }
        } else {
            // 新的毫秒，序列号重置
            sequence = 0L;
        }

        lastTimestamp = timestamp;

        // 组装64位ID: 时间截部分+数据标识id部分+机器id部分+序列部分
        return ((timestamp - startTime) << timestampLeftShift)
                | (datacenterId << datacenterIdLeftShift)
                | (workerId << workerIdLeftShift)
                | sequence;
    }

    /**
     * 阻塞到下一个毫秒，直到获得新的时间戳
     * 优化版本：使用更高效的等待策略,使用Thread.onSpinWait()提示CPU进行优化（JDK 9+）
     * @return 当前时间戳
     */
    protected long tilNextMillis() {
        long timestamp = timeGen();
        while (timestamp <= lastTimestamp) {
            Thread.onSpinWait();
            timestamp = timeGen();
        }
        return timestamp;
    }

    /**
     * 返回以毫秒为单位的当前时间
     * @return 当前时间(毫秒)
     */
    protected long timeGen() {
        return System.currentTimeMillis();
    }

    /**
     * 生成Id
     * @return Long
     */
    public static Long generateId() {
        return IdWorker.getInstance().nextId();
    }

    /**
     * 批量生成ID
     * @param count 生成数量
     * @return ID列表
     */
    public static List<Long> generateIds(int count) {
        if (count <= 0) {
            throw new IllegalArgumentException("生成数量必须大于0");
        }
        if (count > 1000) {
            throw new IllegalArgumentException("单次生成数量不能超过1000");
        }

        List<Long> ids = new ArrayList<>(count);
        IdWorker worker = IdWorker.getInstance();
        for (int i = 0; i < count; i++) {
            ids.add(worker.nextId());
        }
        return ids;
    }

    //==============================Test=============================================
    /** 测试 */
    public static void main(String[] args) {
        for (int i = 0; i < 10; i++) {
            System.out.println(IdWorker.generateId());
            System.out.println(IdWorker.generateId());
        }
        Thread even = new Thread(() -> {
            for(int i=1; i<=10; i+=2) {
                System.out.println(IdWorker.generateId());
            }
        });
        Thread odd = new Thread(() -> {
            for(int i=2; i<=10; i+=2) {
                System.out.println(IdWorker.generateId());
            }
        });
        even.start();
        odd.start();
    }
}
