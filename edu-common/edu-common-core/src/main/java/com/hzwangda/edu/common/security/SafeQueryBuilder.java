package com.hzwangda.edu.common.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 安全查询构建器
 * 用于构建安全的动态查询，防止SQL注入
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
public class SafeQueryBuilder {

    // 允许的排序字段模式（只允许字母、数字、下划线、点号）
    private static final Pattern SAFE_FIELD_PATTERN = Pattern.compile("^[a-zA-Z0-9_.]+$");
    
    // 允许的排序方向
    private static final List<String> ALLOWED_SORT_DIRECTIONS = List.of("ASC", "DESC", "asc", "desc");

    /**
     * 验证字段名是否安全
     *
     * @param fieldName 字段名
     * @return 是否安全
     */
    public static boolean isSafeFieldName(String fieldName) {
        if (!StringUtils.hasText(fieldName)) {
            return false;
        }
        
        // 检查长度限制
        if (fieldName.length() > 64) {
            log.warn("字段名长度超过限制: {}", fieldName);
            return false;
        }
        
        // 检查字符模式
        if (!SAFE_FIELD_PATTERN.matcher(fieldName).matches()) {
            log.warn("字段名包含非法字符: {}", fieldName);
            return false;
        }
        
        return true;
    }

    /**
     * 验证排序方向是否安全
     *
     * @param direction 排序方向
     * @return 是否安全
     */
    public static boolean isSafeSortDirection(String direction) {
        if (!StringUtils.hasText(direction)) {
            return false;
        }
        
        return ALLOWED_SORT_DIRECTIONS.contains(direction.trim());
    }

    /**
     * 构建安全的ORDER BY子句
     *
     * @param sortField 排序字段
     * @param sortDirection 排序方向
     * @return ORDER BY子句
     * @throws IllegalArgumentException 如果参数不安全
     */
    public static String buildSafeOrderBy(String sortField, String sortDirection) {
        if (!isSafeFieldName(sortField)) {
            throw new IllegalArgumentException("不安全的排序字段: " + sortField);
        }
        
        if (!isSafeSortDirection(sortDirection)) {
            throw new IllegalArgumentException("不安全的排序方向: " + sortDirection);
        }
        
        return String.format("ORDER BY %s %s", sortField, sortDirection.toUpperCase());
    }

    /**
     * 构建安全的分页查询
     *
     * @param baseQuery 基础查询
     * @param sortField 排序字段
     * @param sortDirection 排序方向
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 完整的分页查询
     */
    public static String buildSafePaginationQuery(String baseQuery, String sortField, 
                                                 String sortDirection, int offset, int limit) {
        if (!StringUtils.hasText(baseQuery)) {
            throw new IllegalArgumentException("基础查询不能为空");
        }
        
        if (offset < 0) {
            throw new IllegalArgumentException("偏移量不能为负数");
        }
        
        if (limit <= 0 || limit > 1000) {
            throw new IllegalArgumentException("限制数量必须在1-1000之间");
        }
        
        StringBuilder query = new StringBuilder(baseQuery);
        
        // 添加排序
        if (StringUtils.hasText(sortField)) {
            query.append(" ").append(buildSafeOrderBy(sortField, sortDirection));
        }
        
        // 添加分页
        query.append(" LIMIT ").append(limit).append(" OFFSET ").append(offset);
        
        return query.toString();
    }

    /**
     * 验证表名是否安全
     *
     * @param tableName 表名
     * @return 是否安全
     */
    public static boolean isSafeTableName(String tableName) {
        if (!StringUtils.hasText(tableName)) {
            return false;
        }
        
        // 表名长度限制
        if (tableName.length() > 64) {
            return false;
        }
        
        // 表名只能包含字母、数字、下划线，且必须以字母开头
        Pattern tableNamePattern = Pattern.compile("^[a-zA-Z][a-zA-Z0-9_]*$");
        return tableNamePattern.matcher(tableName).matches();
    }

    /**
     * 构建安全的表名引用
     *
     * @param tableName 表名
     * @return 安全的表名引用
     */
    public static String buildSafeTableReference(String tableName) {
        if (!isSafeTableName(tableName)) {
            throw new IllegalArgumentException("不安全的表名: " + tableName);
        }
        
        // 使用反引号包围表名，防止关键字冲突
        return "`" + tableName + "`";
    }

    /**
     * 验证LIKE模式是否安全
     *
     * @param pattern LIKE模式
     * @return 是否安全
     */
    public static boolean isSafeLikePattern(String pattern) {
        if (!StringUtils.hasText(pattern)) {
            return false;
        }
        
        // 检查长度限制
        if (pattern.length() > 255) {
            return false;
        }
        
        // 检查是否包含过多的通配符（防止性能问题）
        long wildcardCount = pattern.chars().filter(ch -> ch == '%' || ch == '_').count();
        if (wildcardCount > 10) {
            log.warn("LIKE模式包含过多通配符: {}", pattern);
            return false;
        }
        
        return true;
    }

    /**
     * 转义LIKE模式中的特殊字符
     *
     * @param pattern 原始模式
     * @return 转义后的模式
     */
    public static String escapeLikePattern(String pattern) {
        if (!StringUtils.hasText(pattern)) {
            return pattern;
        }
        
        return pattern.replace("\\", "\\\\")
                     .replace("%", "\\%")
                     .replace("_", "\\_");
    }

    /**
     * 构建安全的LIKE查询条件
     *
     * @param fieldName 字段名
     * @param pattern 搜索模式
     * @param escapeSpecialChars 是否转义特殊字符
     * @return LIKE条件
     */
    public static String buildSafeLikeCondition(String fieldName, String pattern, boolean escapeSpecialChars) {
        if (!isSafeFieldName(fieldName)) {
            throw new IllegalArgumentException("不安全的字段名: " + fieldName);
        }
        
        String safePattern = escapeSpecialChars ? escapeLikePattern(pattern) : pattern;
        
        if (!isSafeLikePattern(safePattern)) {
            throw new IllegalArgumentException("不安全的LIKE模式: " + safePattern);
        }
        
        return String.format("%s LIKE ? ESCAPE '\\'", fieldName);
    }

    /**
     * 验证IN子句的值数量
     *
     * @param valueCount 值的数量
     * @return 是否安全
     */
    public static boolean isSafeInClauseSize(int valueCount) {
        // 限制IN子句的值数量，防止性能问题
        return valueCount > 0 && valueCount <= 1000;
    }

    /**
     * 构建安全的IN子句占位符
     *
     * @param fieldName 字段名
     * @param valueCount 值的数量
     * @return IN子句
     */
    public static String buildSafeInClause(String fieldName, int valueCount) {
        if (!isSafeFieldName(fieldName)) {
            throw new IllegalArgumentException("不安全的字段名: " + fieldName);
        }
        
        if (!isSafeInClauseSize(valueCount)) {
            throw new IllegalArgumentException("IN子句值数量超出限制: " + valueCount);
        }
        
        StringBuilder placeholders = new StringBuilder();
        for (int i = 0; i < valueCount; i++) {
            if (i > 0) {
                placeholders.append(", ");
            }
            placeholders.append("?");
        }
        
        return String.format("%s IN (%s)", fieldName, placeholders.toString());
    }
}
